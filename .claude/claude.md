# 项目提示词（Prompt）

---

## 1. 技术栈总结

本项目主要技术栈如下：

- **后端语言**：Go（Golang）
- **微服务框架**：Hertz、Kitex
- **数据库**：MySQL
- **缓存**：Redis
- **消息队列**：Kafka、RocketMQ、RedisMQ
- **配置中心**：Nacos
- **API 定义**：Protobuf（IDL 目录下 .proto 文件）
- **容器化与部署**：Docker、docker-compose
- **日志与监控**：自研 logger、链路追踪
- **测试**：REST API 测试、集成测试脚本

---

## 2. 数据库连接配置

- 数据库连接配置主要集中在：
  - `config/nacos_config.yaml` 或 `nacos_config.template.yaml`
  - 代码入口：`pkg/mysql/config.go`、`pkg/nacos/config.go`
- 一般通过 Nacos 配置中心动态下发，或本地配置文件覆盖。
- 本地开发/测试时，请确保数据库连接信息已正确配置。

---

## 3. 单元测试环境变量

运行单元测试前，需设置以下环境变量：

1. `UOFFER_ENV=test` // 指定测试环境
2. `GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore` // 忽略 protobuf 注册冲突，防止多 proto 文件重复注册报错

**示例：**

```bash
export UOFFER_ENV=test
export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore
go test ./...
```

详细说明可参考 `docs/testing/integration_test_guide.md`。

---

## 4. 代码规范与文档

- **注释规范**：统一采用 JSDoc 风格注释，要求每个导出函数、结构体、接口均有详细注释。
- **目录结构**：遵循领域驱动设计（DDD），分层清晰，详见 `docs/architecture/`。
- **API 文档**：所有接口定义均在 `idl/api/` 下维护，自动生成文档。
- **代码风格**：Go 代码遵循官方 gofmt 规范，建议提交前执行 `go fmt ./...`。
- **提交规范**：建议参考 `docs/standards/documentation_guide.md`，包括注释、命名、提交信息等。

---

## 参考资料

- `docs/architecture/`   项目架构与分层说明
- `docs/database/`   数据库设计与迁移说明
- `docs/testing/`   测试用例与集成测试指南
- `docs/standards/`   代码与文档规范

---

## 开发环境依赖与工具版本

- 推荐使用的工具及版本：
  - Go：1.23.3
  - protoc：v28.3
  - protoc-gen-go：v1.35.2
  - hz：v0.9.1
  - kitex：v0.11.3
  - gorm-gen：最新版
- 安装方式与详细说明请参考 README.md。
- 建议团队成员保持工具版本一致，避免生成代码格式冲突。

## 常用 Make 命令

- 代码生成：
  - `make update`  // 全部更新
  - `make update_order`  // 只更新 order
  - `make update_admin_api`  // 只更新 admin_api
- 编译：
  - `make build`  // 全部编译
  - `make build_order`  // 编译单个服务
- 本地运行：
  - `make order`、`make product`、`make customer`、`make user` 等
- 详细命令和说明请查阅 Makefile 和 README.md。

## 本地开发注意事项

- 如需本地调试整套服务，需在 nacos 创建自建命名空间，并修改 `config/nacos_config.yaml` 的 namespace。
- 启动前设置环境变量：
  ```bash
  export HOST_ENV=local
  export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn
  ```
- 本地开发建议优先查阅 README.md 获取最新环境配置和调试建议。

## 依赖服务

- 依赖服务的 docker-compose 配置见 `supports/devops/nacos/docker-compose.yml`，可参考官方文档快速启动。
- 相关依赖服务如 nacos、redis、kafka 等，均可通过 supports 目录下的 docker-compose 文件一键部署。

## 5. 新需求开发标准流程

完成一个新需求，建议遵循如下步骤：

### 1. 数据表设计或理解

- 如需新建数据表，在 `migrations/` 目录下编写 SQL 脚本（如 `xxxx_create_xxx_table.up.sql`）。
- 如复用已有表，先查阅 `docs/database/` 及 `migrations/`，理解表结构和字段。
  需要执行`make update_model` 更新模型。

### 2. 定义 IDL（接口描述语言）

- **API 层 IDL**：在 `idl/api/admin_api/`（或 `idl/api/app_api/` 等）目录下定义，面向前端或外部接口。
- **RPC 层 IDL**：在 `idl/svc/order/`（或 `idl/svc/xxx/`）目录下定义，服务间调用用。
- API 层和 RPC 层的 IDL 分离，便于解耦和维护。

### 3. 自动生成代码

- 定义完 IDL 后，需调用脚本自动生成对应的 Go 代码。
- 参考 `Makefile`，常用命令：
  - `make update order`  // 自动生成 order rpc service 的代码
  - `make update admin_api`  // 自动生成 admin_api 下的代码
- 生成代码无误后再进行后续开发。

### 4. 实现 Service 层逻辑

- 以 order 服务为例：
  - `server/cmd/order/handler_xxxService.go`  // 自动生成的 handler 桥接代码
  - `server/cmd/order/internal/services/`  // 实现具体业务逻辑，供 handler 调用
  - `server/cmd/order/internal/models/`  // 数据库操作相关代码，所有 DB 相关逻辑应在此实现
- 原则：handler 只做参数校验和调用，业务逻辑在 services，数据操作在 models。

### 5. 实现 API 层逻辑

- 以 admin_api 为例：
  - `server/cmd/admin_api/biz/handler/admin_api/`  // API 层入口，负责接收请求、参数校验
  - `server/cmd/admin_api/internal/services/`  // 具体功能实现，通常调用 RPC 服务
- 建议：API handler 只做参数校验和调用，具体实现放在 internal/services，便于复用和测试。

### 时间规范

前后端统一用 utc 的毫秒级时间戳。

### 7. git commit 规范

- 本地已配置 git commit 钩子（见 script/install-hooks.sh），默认会在提交前自动检查代码风格、单元测试等。
- 建议每次提交前确保本地单测通过，遵循规范提交。
- 如因特殊原因（如部分代码暂时无法通过单测），可在提交时加 `--no-verify` 跳过钩子：
  ```bash
  git commit -m "feat: 新增xxx功能" --no-verify
  ```
- 推荐 commit message 格式：
  - `feat: xxx`  // 新功能
  - `fix: xxx`  // 修复 bug
  - `refactor: xxx`  // 重构
  - `docs: xxx`  // 文档
  - `test: xxx`  // 测试
  - `chore: xxx`  // 其他杂项
- 详细规范可参考 `docs/standards/documentation_guide.md`。

### sepcs规范
当你需要创建specs时，请放入specs目录。
这样就不会提交上去。
