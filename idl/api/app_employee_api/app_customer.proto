syntax = "proto3";
package app_employee_api;
option go_package = "server/cmd/app_employee_api";

import "hz.proto"; // hz框架注解

//获取客户绑定列表
message GetBindCustomersReq {
  //客户id
  int64 customer_id = 1 [ (api.vd) = "$>0;msg:'客户id不能为空'" ];
  //用户身份（1用户，2家长）
  int32 identity = 2 [ (api.vd) = "$>0;msg:'客户身份不能为空'" ];
}
message GetBindCustomersRsp {
  repeated GroupChatInfo customers = 1;
}

// 群聊客户信息
message GroupChatInfo {
  // 用户id
  int32 id = 1;
  // 用户名称
  string name = 2;
  // 用户头像
  string avatar = 3;
  // im 群id
  string im_group_id = 4;
  //im用户id
  string im_user_id = 5;
}
// 客户跟进员工信息
message EmployeeItem {
  // 员工角色
  string role_name = 1;
  // role id
  int32 role_id = 2;
  // 员工id
  int32 id = 3;
  // 员工名称
  string name = 4;
  //部门id
  int64 dept_id = 5;
  //部门名称
  string dept_name = 6;
  // 头像
  string avatar = 7;
}
//获取客户跟进员工列表
message GetCustomerFollowEmployeeListReq {
  int64 customer_id = 1;
}
message GetCustomerFollowEmployeeListRsp {
  repeated EmployeeItem employees = 1;
}

// 新增跟进员工
message AddCustomerSupportAgentsReq {
  int64 customer_id = 1;
  // 跟进员工id列表
  repeated int64 employee_ids = 2;
}
message AddCustomerSupportAgentsRsp {

}
//删除客户跟进员工
message DeleteCustomerSupportAgentsReq{
  int64 customer_id = 1;
  repeated int64 employee_ids = 2;
}
message DeleteCustomerSupportAgentsRsp{

}