syntax = "proto3";
package admin_api;
option go_package = "server/cmd/admin_api";

// 收款账户类型
enum PaymentAccountType {
  PAYMENT_ACCOUNT_TYPE_UNSPECIFIED = 0;
  PAYMENT_ACCOUNT_TYPE_ALIPAY = 1;          // 支付宝
  PAYMENT_ACCOUNT_TYPE_WECHAT = 2;          // 微信
  PAYMENT_ACCOUNT_TYPE_BANK_ACCOUNT = 3;    // 中国银行账户
  PAYMENT_ACCOUNT_TYPE_UK_BANK_ACCOUNT = 4; // 英国银行账户
  PAYMENT_ACCOUNT_TYPE_POS = 5;             // pos机
  PAYMENT_ACCOUNT_TYPE_PAYPAL = 6;          // paypal
  PAYMENT_ACCOUNT_TYPE_OTHER = 7;           // 其他
}

// 客户类型
enum FinancialCustomerType {
  FINANCIAL_CUSTOMER_TYPE_UNSPECIFIED = 0;
  FINANCIAL_CUSTOMER_TYPE_NEW = 1;   // 新客户
  FINANCIAL_CUSTOMER_TYPE_OLD = 2;   // 老客户
  FINANCIAL_CUSTOMER_TYPE_OTHER = 3; // 其他
}

// 收款类型
enum FundType {
  FUND_TYPE_UNSPECIFIED = 0;
  FUND_TYPE_DEPOSIT = 1;        // 定金
  FUND_TYPE_FIRST_PAYMENT = 2;  // 首期款
  FUND_TYPE_FINAL_PAYMENT = 3;  // 尾款
  FUND_TYPE_THIRD_PARTY_FEE = 4; // 第三方申请费
}
