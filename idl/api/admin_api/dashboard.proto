syntax = "proto3";
package admin_api;
option go_package = "server/cmd/admin_api";

import "hz.proto";
import "financial_enum.proto";
import "workflow_enum.proto";
import "order.proto";

// 待办事项统计请求参数
message GetPendingTaskStatsReq {
  // 收款单筛选条件
  FundFilterOptions fund_filter = 1;
  // 支款单筛选条件
  RefundFilterOptions refund_filter = 2;

  // 工单筛选条件
  WorkflowViewFilterOptions workflow_view_filter = 3;

  // 工单任务筛选条件
  WorkflowTaskViewFilterOptions workflow_task_view_filter = 4;
}

// 收款单筛选条件
message FundFilterOptions {
  repeated int64 brand_ids = 1;                          // 品牌ID列表
  repeated PaymentAccountType payment_account_types = 2; // 收款账户类型
  repeated int64 payment_accounts = 3;                   // 支付账户ID列表
}

// 支款单筛选条件
message RefundFilterOptions {
  repeated int32 refund_types = 1; // 支款类型列表
}

// 工单筛选条件
message WorkflowViewFilterOptions { WorkflowStatus workflow_status = 1; }

// 工单任务筛选条件
message WorkflowTaskViewFilterOptions {
  WorkflowTaskViewStatus workflow_task_view_status = 1;
}

// 待办事项统计响应数据
message GetPendingTaskStatsRsp {
  // 收款单统计
  FundStats fund_stats = 1;
  // 支款单统计
  RefundStats refund_stats = 2;
  // 工单统计
  WorkflowViewStats workflow_view_stats = 3;
  // 工单任务统计
  WorkflowTaskViewStats workflow_task_view_stats = 4;
  // 工单第三方申请费统计
  WorkflowThirdFundStats workflow_third_fund_stats = 5;
  // 订单统计（员工侧）
  OrderStaticEmployeeStats order_static_employee_stats = 6;
}

// 待办事项统计-收款单统计
message FundStats {
  int32 new_pending_review = 1;        // 新增待审核
  int32 rejected_resubmit_pending = 2; // 驳回重新提交待审核
  int32 overdue_pending_review = 3;    // 超3个工作日未审核
}

// 待办事项统计-支款单统计
message RefundStats {
  int32 new_pending_review = 1;        // 新增待审核
  int32 rejected_resubmit_pending = 2; // 驳回重新提交待审核
  int32 overdue_pending_review = 3;    // 超3个工作日未审核
  int32 pending_payment = 4;           // 待支款
  int32 near_deadline_payment = 5;     // 临近支款截止日期
}

// FinancialStat represents a statistical value for a period.
message PeriodComparisonStat {
  int64 current_period_count = 1;  // 当前周期数量
  int64 previous_period_count = 2; // 上一周期数量
  double percentage_change = 3;    // 环比变化率
  bool is_valid =
      4; // 百分比变化是否有效（当previous_period_count > 0时为true）
}

// CollectionStats contains statistics for receipts.
message CollectionStats {
  PeriodComparisonStat new_pending_receipts = 1;           // 新增待审核收款单
  PeriodComparisonStat approved_receipts = 2;              // 审核通过收款单
  PeriodComparisonStat rejected_receipts = 3;              // 审核驳回收款单
  PeriodComparisonStat average_approve_duration_hours = 4; // 平均审核时长(小时)
}

// 待办事项统计-工单统计
message WorkflowViewStats {
  int32 pending_workflow_view = 1; // 待接收工单
}

// 待办事项统计-工单任务统计
message WorkflowTaskViewStats {
  int32 pending_workflow_task_view = 1; // 待处理工单任务
}

// DisbursementApprovalStats contains statistics for disbursement approvals.
message DisbursementApprovalStats {
  PeriodComparisonStat new_pending_disbursements = 1;      // 新增待审核支款单
  PeriodComparisonStat approved_disbursements = 2;         // 审核通过支款单
  PeriodComparisonStat rejected_disbursements = 3;         // 审核驳回支款单
  PeriodComparisonStat average_approve_duration_hours = 4; // 平均审核时长(小时)
}

// DisbursementPaymentStats contains statistics for disbursement payments.
message DisbursementPaymentStats {
  PeriodComparisonStat new_pending_payments = 1;           // 新增待打款支款单
  PeriodComparisonStat successful_payments = 2;            // 支款成功支款单
  PeriodComparisonStat rejected_payments = 3;              // 审核驳回支款单
  PeriodComparisonStat overdue_payments = 4;               // 支款超时支款单
  PeriodComparisonStat average_payment_duration_hours = 5; // 平均打款时长(小时)
}

message WorkflowStats {
  PeriodComparisonStat assigned_workflows = 1;          // 被派单的工单数
  PeriodComparisonStat received_workflows = 2;          // 接收的工单数
  PeriodComparisonStat completed_workflows = 3;         // 服务完成的工单数
  PeriodComparisonStat served_customers = 4;            // 服务的客户数量
  PeriodComparisonStat average_processing_duration = 5; // 平均工单处理时长
}

message WorkflowNodeStats {
  PeriodComparisonStat assigned_workflow_nodes = 1;     // 新增工单任务数
  PeriodComparisonStat received_workflow_nodes = 2;     // 接收的工单任务数
  PeriodComparisonStat completed_workflow_nodes = 3;    // 完成的工单任务数
  PeriodComparisonStat average_processing_duration = 5; // 平均工单任务处理时长
}

// GetFinancialStatsRequest is the request for financial statistics.
message GetKeyDataReq {
  int64 start_date = 1;              // 开始日期时间戳
  int64 end_date = 2;                // 结束日期时间戳
  repeated int64 brand_ids = 3;      // 出单品牌ID列表
  repeated int32 account_types = 4;  // 收款账号类型列表
  repeated int64 account_ids = 5;    // 收款账号ID列表
  repeated int32 refund_types = 6;   // 支款类型列表
}

// GetFinancialStatsResponse is the response for financial statistics.
message GetKeyDataRsp {
  CollectionStats collection_stats = 1;                      // 收款信息
  DisbursementApprovalStats disbursement_approval_stats = 2; // 支款审核信息
  DisbursementPaymentStats disbursement_payment_stats = 3;   // 支款打款信息
  WorkflowStats workflow_stats = 4;                          // 工单统计
  WorkflowNodeStats workflow_node_stats = 5;                 // 工单节点统计
  SalesCompareStats sales_compare_stats = 6;                 // 销售对比统计
}

message WorkflowThirdFundStats {
  int64 approve_count = 1; // 审批通过数量
  int64 reject_count = 2;  // 审批驳回数量
}

// 销售统计项，参考 FinancialStat 设计
message SalesStatItem {
  int64 current_period_count = 1;   // 当前周期数量
  int64 previous_period_count = 2;  // 上一周期数量
  double percentage_change = 3;     // 变化百分比
  bool is_valid = 4;               // 数据是否有效（上一周期是否有数据）
}

// 销售金额统计项
message SalesAmountStatItem {
  string current_period_amount = 1;   // 当前周期金额
  string previous_period_amount = 2;  // 上一周期金额
  double percentage_change = 3;       // 变化百分比
  bool is_valid = 4;                 // 数据是否有效（上一周期是否有数据）
}

// 销售对比统计数据
message SalesCompareStats {
  SalesStatItem num_new = 1;                    // 新建订单数统计
  SalesStatItem num_first_pass = 2;             // 审核通过订单数统计
  SalesAmountStatItem amount_first_pass = 3;    // 审核通过销售额统计
  SalesStatItem num_disbursement_apply = 4;     // 申请支款订单数统计
}

// 待办事项统计-订单统计（员工侧）
message OrderStaticEmployeeStats {
  OrderReviewStatic deposit = 1;      // 已下订单
  OrderReviewStatic first = 2;        // 首款订单
  OrderReviewStatic final = 3;        // 尾款待支付
  OrderReviewStatic success = 4;      // 支付成功
  OrderReviewStatic disbursement = 5; // 支款订单
  OrderReviewStatic close = 6;        // 订单关闭
  int64 disbursement_paid = 7;        // 支款订单-支款完成
  int64 final_workflow_completed = 8; // 尾款待支付-尾款待提交
  int64 final_workflow_process = 9;   // 尾款待支付-服务未完成
  int64 workflow_rejected_count = 10; // 派单被驳回数量
}
