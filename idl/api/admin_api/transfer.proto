syntax = "proto3";
package admin_api;
option go_package = "server/cmd/admin_api";

import "hz.proto"; // hz框架注解


// 创建交接任务请求
message CreateTransferTaskReq {
    // 交出方员工ID<必填>
    int64 from_employee_id = 1;
    // 接收方员工ID<必填>
    int64 to_employee_id = 2;
    // 客户ID列表<必填>
    repeated int64 customer_ids = 3;
}
  
// 创建交接任务响应数据
message CreateTransferTaskRsp {
    // 交接任务ID
    int64 task_id = 1;
    // 交接任务编号
    string task_no = 2;
}

// 获取交接任务详情请求
message GetTransferTaskDetailReq {
    // 交出方员工ID<必填>
    int64 from_employee_id = 1;
}

// 获取交接任务详情响应
message GetTransferTaskDetailRsp {
    // 交接任务ID
    int64 task_id = 1;
    // 交接任务编号
    string task_no = 2;
    // 交出方员工ID
    int64 from_employee_id = 3;
    // 接收方员工ID
    int64 to_employee_id = 4;
    // 总数量
    int64 total_count = 5;
    // 成功数量
    int64 success_count = 6;
    // 失败数量
    int64 fail_count = 7;
    // 处理中数量
    int64 processing_count = 8;
    // 任务状态 1-待处理 2-处理中 3-已完成 4-已取消
    int32 task_status = 9;
    // 创建时间
    int64 created_at = 10;
    // 更新时间
    int64 updated_at = 11;
}
