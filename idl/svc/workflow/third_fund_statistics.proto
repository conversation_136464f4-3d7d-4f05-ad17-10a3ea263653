syntax = "proto3";
package workflow;
option go_package = "server/cmd/workflow";

import "base.proto";
import "workflow_entity.proto";

enum ThirdFundWorkflowStatus {
  ThirdFundWorkflowStatusUnknown = 0;
  ThirdFundWorkflowStatusWaiting = 1; // 待审批
  ThirdFundWorkflowStatusApprove = 2; // 通过
  ThirdFundWorkflowStatusReject = 3; // 驳回
}

message CurrentThirdFundWorkflowReq {
  ThirdFundWorkflowStatus approve_status = 1; // 审批状态
  int64 page_num = 2; // 页码
  int64 page_size = 3; // 页大小
}

message WorkflowThirdFundEntity {
  WorkflowInfoEntity workflow_info = 1;
  int64 operation_time = 2; // 审批通过/驳回时间
}

message CurrentThirdFundWorkflowRsp {
  repeated WorkflowThirdFundEntity items = 1;
  int32 total = 2;

  base.BaseRsp base = 255;
}

// 复用ListWorkflowRsp
//message CurrentThirdFundWorkflowRsp {
//
//}