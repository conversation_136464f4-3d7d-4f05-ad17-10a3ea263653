syntax = "proto3";
package order;
option go_package = "server/cmd/order";

import "enum.proto";

// 订单信息
message OrderEntity {
  int64 id = 1; // 订单ID

  string order_no = 2; // 订单编号(时间戳+6位随机数)

  int64 customer_id = 3; // 客户UID
  int64 reviewer_id = 4; // 审核人UID（最后审核人UID）
  int64 executor_id = 5; // 派单对象UID

  int64 brand_id = 7;    // 品牌ID
  int64 business_id = 8; // 业务线ID
  int64 service_id = 9;  // 服务项目ID

  string brand_name = 10;    // 品牌名称
  string business_name = 11; // 业务线名称
  string service_name = 12;  // 服务项目名称

  OrderInstallmentType installment_type = 13;   // 付款方式：分期方式
  OrderDisbursementType disbursement_type = 14; // 支款类型

  StatusOrder status = 15;                   // 订单状态
  StatusOrderReview status_review = 16;      // 审核状态
  StatusOrderWorkflow status_workflow = 17;  // 工单服务状态
  StatusOrder status_prev = 18;              // 订单状态（撤销审核驳回支款专用）
  StatusOrderReview status_review_prev = 19; // 审核状态（撤销审核驳回支款专用）

  StatusOrderPay status_pay_deposit = 21;      // 定金-支付状态
  StatusOrderPay status_pay_first = 22;        // 首款-支付状态
  StatusOrderPay status_pay_final = 23;        // 尾款-支付状态
  StatusOrderPay status_pay_disbursement = 24; // 支款-支付状态

  int64 pay_deposit_at = 31; // 定金-付款时间（定金-实际付款日期，单位：毫秒）
  int64 pay_first_at = 32;   // 首款-付款时间（首款-实际付款日期，单位：毫秒）
  int64 pay_final_at = 33;   // 尾款-付款时间（尾款-实际付款日期，单位：毫秒）
  int64 pay_disbursement_at =
      34; // 支款-付款时间（支款-实际付款日期，单位：毫秒）（多次，仅记录最新一次）

  int64 apply_deposit_at = 41; // 定金-申请-审核时间（单位：毫秒）
  int64 apply_first_at = 42;   // 首款-申请-审核时间（单位：毫秒）
  int64 apply_final_at = 43;   // 尾款-申请-审核时间（单位：毫秒）
  int64 apply_disbursement_at =
      44; // 支款-申请-审核时间（多次，仅记录最新一次）（单位：毫秒）

  int64 pass_deposit_at = 51; // 定金-审核通过时间（单位：毫秒）
  int64 pass_first_at = 52;   // 首款-审核通过时间（单位：毫秒）
  int64 pass_final_at = 53;   // 尾款-审核通过时间（单位：毫秒）
  int64 pass_disbursement_at =
      54; // 支款-审核通过时间（多次，仅记录最新一次）（单位：毫秒）

  int64 reject_at =
      60; // 审核驳回审核时间（多次，仅记录最新一次）（单位：毫秒）

  int64 created_by = 61; // 操作人UID（出单成员）
  int64 updated_by = 62; // 最后操作人UID
  int64 closed_by = 63;  // 关闭人UID
  int64 created_at = 64; // 创建时间（单位：毫秒）
  int64 updated_at = 65; // 更新时间（单位：毫秒）
  int64 closed_at = 66;  // 关闭时间（单位：毫秒）

  string workflow_by = 70; // 工单跟进人UID（，分割）
}

// 订单付款信息
message OrderPayEntity {
  int64 id = 1;
  int64 order_id = 2; // 订单ID

  string urgent_times =
      3; // 服务加急倍数（默认1即不加急，1.5表示1.5倍，2表示2倍）
  string discount_rate = 4; // 首期款折扣（默认100即不打折）

  string amount_deposit = 11;     // 定金实际支付金额（已收定金）
  string amount_total_goods = 12; // 订单总额(商品计算金额）
  string amount_total =
      14; // 实际成交金额（实际成交金额 = 定金+实收首款+加急费+实收尾款）
  string amount_contract =
      15; // 合同金额（合同金额 = 定金+实收首款+加急费+预收尾款）
  string amount_first_estimated = 16; // 预估首款（首期款）-金额人民币
  string amount_final_estimated = 17; // 预估尾款-金额人民币
  string amount_first_receivable =
      18; //  应收首款-金额（首期款-已收定金）*折扣+首期款*（加急倍数-1）
  string amount_final_receivable = 19; // 应收尾款-金额
  string amount_first =
      20; // 实收首款-金额（首期款-已收定金）*折扣+首期款*（加急倍数-1）
  string amount_final = 21;        // 实收尾款-金额
  string amount_disbursement = 22; // 实付支款-金额
  string amount_disbursement_review =
      23; // 待审核支款-金额（多次，仅记录最新一次）

  string amount_disbursement_list = 24; // 支款金额（仅列表展示用）

  string amount_liquidated = 25; // 实付违约金-金额
  string amount_liquidated_review =
      26; // 待审核违约金--金额（多次，仅记录最新一次）

  string amount_bursary = 28; // 实付奖学金-金额
  string amount_bursary_review =
      29; // 待审核奖学金-金额（多次，仅记录最新一次）

  string amount_scholarship = 31; // 奖学金-金额
  string amount_urgent = 32;      // 加急费
  string amount_premium = 33;     // 溢价-金额
  string amount_discount = 34;    // 折扣-金额

  string currency_deposit = 41;     // 定金币种
  string currency_total_goods = 42; // 订单总额-币种 人民币
  string currency_total = 44;       // 实际成交金额-币种
  string currency_contract = 45;
  string currency_first_estimated = 46;     // 预估首款（首期款）-币种 人民币
  string currency_final_estimated = 47;     // 预估尾款-币种 人民币
  string currency_first_receivable = 48;    // 应收首款-币种
  string currency_final_receivable = 49;    // 应收尾款-币种
  string currency_first = 50;               // 实收首款-币种
  string currency_final = 51;               // 实收尾款-币种
  string currency_disbursement = 52;        // 实付支款-币种
  string currency_disbursement_review = 53; // 待审核支款-币种

  string currency_disbursement_list = 54; // 支款金额（仅列表展示用）

  string currency_liquidated = 55;        // 实付违约金-币种
  string currency_liquidated_review = 56; // 待审核违约金-币种

  string currency_bursary = 58;        // 实付奖学金-币种
  string currency_bursary_review = 59; // 待审核奖学金-币种

  string currency_scholarship = 61; // 奖学金-币种
  string currency_urgent = 62;      // 加急费-币种
  string currency_premium = 63;     // 溢价金额-币种
  string currency_discount = 64;    // 折扣金额-币种

  StatusYesNo exempt_final = 91;    // 免除尾款
  StatusYesNo urgent_service = 92;  // 服务加急
  StatusYesNo has_scholarship = 94; // 奖学金
  StatusYesNo auto_schedule = 95;   // 提交订单后自动派单
  OrderFinalPaymentType final_payment_type =
      96;                              // 尾款支付方式#1%仅收一次|2%按数量收取
  StatusYesNo is_premium_allowed = 97; // 商品溢价#1%是|2%否
  StatusYesNo is_upgrade_order = 98;   // 升级订单/折扣补差#1%是|2%否

  string exempt_final_reason = 99; // 免除尾款原因
}

// 订单商品
message OrderGoodsEntity {
  int64 id = 1;          // 主键
  int64 customer_id = 2; // 用户UID
  int64 order_id = 3;    // 订单ID
  int64 goods_id = 4;    // 商品ID
  int64 spec_id = 5;     // 商品规格ID
  int64 business_id = 7; // 业务线ID
  int64 service_id = 8;  // 服务项目ID

  OrderGoodsType goods_type = 10; // 商品类型

  string business_name = 11; // 业务线名称
  string service_name = 12;  // 服务项目名称

  string goods_no = 20;   // 商品编号
  string goods_name = 21; // 商品名称
  string goods_spec = 22; // 商品规格

  string goods_price = 23;       // 商品单价
  string goods_first_price = 24; // 商品首款
  string goods_final_price = 25; // 商品尾款

  string goods_currency = 26;       // 商品单价-币种
  string goods_first_currency = 27; // 商品首款-币种
  string goods_final_currency = 28; // 商品尾款-币种

  string goods_redundancy = 29; // 商品冗余信息（JSON）
  string goods_num = 30;        // 购买数量

  string amount_first = 31; // 首期款
  string amount_final = 32; // 尾款
  string amount_total = 33; // 总价

  string currency_first = 41; // 首期款-币种
  string currency_final = 42; // 尾款-币种
  string currency_total = 43; // 总价-币种

  StatusYesNo purchase_num_type = 51; // 商品数量政策#1整数2小数
  StatusYesNo is_discount_first = 52; // 首期款折扣#1%是|2%否
  string discount_first = 53;         // 首期款折扣（默认100即不打折）
  OrderFinalPaymentType final_payment_type =
      54; // 尾款支付方式#1%仅收一次|2%按数量收取
}

// 订单操作日志
message OrderOperationLogEntity {
  int64 id = 1;                // 主键
  int64 order_id = 2;          // 订单ID
  string order_no = 3;         // 订单编号
  OrderOperationType type = 4; // 操作类型
  OrderLogEntity log = 5;      // 操作日志（JSON）
  int64 created_by = 6;        // 创建人UID
  int64 created_at = 7;        // 创建时间（单位：毫秒）
  string msg = 8;              // 操作内容
}

// 操作日志
message OrderLogEntity {
  OrderLogEntityItem old = 1;
  OrderLogEntityItem new = 2;
}

// 操作日志
message OrderLogEntityItem {
  StatusOrder status = 15;                   // 订单状态
  StatusOrderReview status_review = 16;      // 审核状态
  StatusOrderWorkflow status_workflow = 17;  // 工单服务状态
  StatusOrder status_prev = 18;              // 订单状态（撤销审核驳回支款专用）
  StatusOrderReview status_review_prev = 19; // 审核状态（撤销审核驳回支款专用）

  StatusOrderPay status_pay_deposit = 21;      // 定金-支付状态
  StatusOrderPay status_pay_first = 22;        // 首款-支付状态
  StatusOrderPay status_pay_final = 23;        // 尾款-支付状态
  StatusOrderPay status_pay_disbursement = 24; // 支款-支付状态
}

// 客户信息
message CustomerEntity {
  int64 id = 1;    // 客户ID
  string name = 2; // 客户姓名
}

// 操作人信息
message UpdaterEntity {
  int64 id = 1;         // 客户ID
  string name = 2;      // 客户姓名
  string role_name = 3; // 角色
  string dept_name = 4; // 部门
}

// 审核人信息
message ReviewerEntity {
  int64 id = 1;         // 客户ID
  string name = 2;      // 客户姓名
  string role_name = 3; // 角色
  string dept_name = 4; // 部门
}

// 币种信息
message CurrencyEntity {
  int64 id = 1;           // 币种ID
  string code = 2;        // 货币编码（CNY/USD/EUR）
  string name = 3;        // 货币名称
  string symbol = 4;      // 货币标识（¥/$/€）
  int32 rate = 5;         // 货币最小单位比例
  string rate_remark = 6; // 比例备注
  string remark = 7;      // 备注
  StatusYesNo status = 8; // 启用状态
}

// 订单工单节点记录
message OrderWorkflowLogEntity {
  int64 id = 1;               // 日志ID
  int64 customer_id = 2;      // 用户UID
  int64 order_id = 3;         // 订单ID
  string order_no = 4;        // 订单编号
  int64 workflow_id = 5;      // 工单ID
  int64 workflow_node_id = 6; // 工单节点ID

  string business_name = 7; // 业务线名称
  string service_name = 8;  // 服务项目名称
  string product_name = 9;  // 商品名称

  int32 workflow_node_status =
      10; // 节点状态(0:UNKNOWN, 1:未开始,2:进行中,3暂停中,4:已完成,5:已终止)
  int32 status = 11; // 状态#1%正常|2%关闭

  int64 closed_by = 12;  // 关闭人UID
  int64 created_at = 13; // 创建时间（单位：毫秒）
  int64 updated_at = 14; // 更新时间（单位：毫秒）
  int64 closed_at = 15;  // 关闭时间（单位：毫秒）
}
