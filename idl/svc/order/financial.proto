syntax = "proto3";

package order;

option go_package = "server/cmd/order";

import "base.proto";

// FinancialStat represents a statistical value for a period.
message FinancialStat {
  int64 current_period_count = 1;  // 当前周期数量
  int64 previous_period_count = 2; // 上一周期数量
  double percentage_change = 3;    // 环比变化率
  bool is_valid = 4;              // 百分比变化是否有效（当previous_period_count > 0时为true）
}

// CollectionStats contains statistics for receipts.
message CollectionStats {
  FinancialStat new_pending_receipts = 1;    // 新增待审核收款单
  FinancialStat approved_receipts = 2;       // 审核通过收款单
  FinancialStat rejected_receipts = 3;       // 审核驳回收款单
  FinancialStat average_approve_duration_hours = 4; // 平均审核时长(小时)
}

// DisbursementApprovalStats contains statistics for disbursement approvals.
message DisbursementApprovalStats {
  FinancialStat new_pending_disbursements = 1; // 新增待审核支款单
  FinancialStat approved_disbursements = 2;    // 审核通过支款单
  FinancialStat rejected_disbursements = 3;    // 审核驳回支款单
  FinancialStat average_approve_duration_hours = 4;   // 平均审核时长(小时)
}

// DisbursementPaymentStats contains statistics for disbursement payments.
message DisbursementPaymentStats {
  FinancialStat new_pending_payments = 1;    // 新增待打款支款单
  FinancialStat successful_payments = 2;     // 支款成功支款单
  FinancialStat rejected_payments = 3;       // 审核驳回支款单
  FinancialStat overdue_payments = 4;        // 支款超时支款单
  FinancialStat average_payment_duration_hours = 5; // 平均打款时长(小时)
}

// GetFinancialStatsRequest is the request for financial statistics.
message GetFinancialStatsRequest {
  base.BaseReq base = 1;
  int64 start_date = 2;              // 开始日期时间戳
  int64 end_date = 3;                // 结束日期时间戳
  repeated int64 brand_ids = 4;      // 出单品牌ID列表
  repeated int32 account_types = 5;  // 收款账号类型列表
  repeated int64 account_ids = 6;    // 收款账号ID列表
  repeated int32 refund_types = 7;   // 支款类型列表
  repeated int64 staff_ids = 8;      // 员工ID列表（权限过滤）
}

// GetFinancialStatsResponse is the response for financial statistics.
message GetFinancialStatsResponse {
  base.BaseRsp base = 1;
  CollectionStats collection_stats = 2;                      // 收款信息
  DisbursementApprovalStats disbursement_approval_stats = 3; // 支款审核信息
  DisbursementPaymentStats disbursement_payment_stats = 4;   // 支款打款信息
}

message CallStatFinancialFundDailyReq { base.BaseReq base = 255; }
message CallStatFinancialFundDailyRsp {
  bool success = 1;
  string message = 2;
  base.BaseRsp base = 255;
}

// 调用财务支款统计存储过程
message CallStatFinancialRefundDailyReq { base.BaseReq base = 255; }
message CallStatFinancialRefundDailyRsp {
  bool success = 1;
  string message = 2;
  base.BaseRsp base = 255;
}

// 调用历史数据补录存储过程
message CallBackfillFinancialStatsReq {
  bool force = 1; // 是否强制补录
  base.BaseReq base = 255;
}
message CallBackfillFinancialStatsRsp {
  bool success = 1;
  string message = 2;
  int64 affected_rows = 3;
  base.BaseRsp base = 255;
}
