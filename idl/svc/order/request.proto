syntax = "proto3";
package order;
option go_package = "server/cmd/order";

import "base.proto";
import "enum.proto";
import "entity.proto";

// 销售统计项，参考 FinancialStat 设计
message SalesStatItem {
  int64 current_period_count = 1;  // 当前周期数量
  int64 previous_period_count = 2; // 上一周期数量
  double percentage_change = 3;    // 变化百分比
  bool is_valid = 4;               // 数据是否有效（上一周期是否有数据）
}

// 销售金额统计项
message SalesAmountStatItem {
  string current_period_amount = 1;  // 当前周期金额
  string previous_period_amount = 2; // 上一周期金额
  double percentage_change = 3;      // 变化百分比
  bool is_valid = 4;                 // 数据是否有效（上一周期是否有数据）
}

message GetSalesCompareDataReq {
  int64 uid = 1;   // 用户ID
  int64 start = 2; // 开始时间（毫秒级时间戳）
  int64 end = 3;   // 结束时间（毫秒级时间戳）

  base.BaseReq base = 255;
}

message GetSalesCompareDataRsp {
  SalesStatItem num_new = 1;                 // 新建订单数统计
  SalesStatItem num_first_pass = 2;          // 审核通过订单数统计
  SalesAmountStatItem amount_first_pass = 3; // 审核通过销售额统计
  SalesStatItem num_disbursement_apply = 4;  // 申请支款订单数统计

  base.BaseRsp base = 255;
}

// 定时任务-统计昨天订单销售数据
message InsertYesterdaySalesDataReq { base.BaseReq base = 255; }

// 定时任务-统计昨天订单销售数据
message InsertYesterdaySalesDataRsp { base.BaseRsp base = 255; }

// 历史数据补录-统计指定时间范围的订单销售数据
message InsertHistoricalSalesDataReq {
  int64 start_date = 1; // 开始日期时间戳（毫秒）
  int64 end_date = 2;   // 结束日期时间戳（毫秒）

  base.BaseReq base = 255;
}

// 历史数据补录-统计指定时间范围的订单销售数据
message InsertHistoricalSalesDataRsp { base.BaseRsp base = 255; }

// 移除周月统计相关的消息定义，改为前端传入日期范围进行灵活查询

// 获取订单数据（用户侧）
message GetOrderStaticReq {
  int64 customer_id = 1;

  base.BaseReq base = 255;
}

// 审核状态数据
message OrderReviewStatic {
  int64 pass = 1;         // 审核通过
  int64 draft = 2;        // 草稿
  int64 draft_audit = 3;  // 草稿待审核
  int64 reject_audit = 4; // 驳回待审核
  int64 reject = 5;       // 审核驳回
  int64 all = 6;          // 小状态统计
}

// 获取订单数据
message GetOrderStaticRsp {
  OrderReviewStatic deposit = 1;      // 已下订单
  OrderReviewStatic first = 2;        // 首款订单
  OrderReviewStatic final = 3;        // 尾款待支付
  OrderReviewStatic success = 4;      // 支付成功
  OrderReviewStatic disbursement = 5; // 支款订单
  OrderReviewStatic close = 6;        // 订单关闭
  int64 disbursement_paid = 7;        // 支款订单-支款完成
  int64 final_workflow_completed = 8; // 尾款待支付-尾款待提交
  int64 final_workflow_process = 9;   // 尾款待支付-服务未完成

  base.BaseRsp base = 255;
}

// 获取订单统计数据（员工侧）
message GetOrderStaticEmployeeReq {
  repeated int64 customer_ids = 1; // 客户UID
  repeated int64 updater_ids = 2;  // 操作人UID（出单成员）

  base.BaseReq base = 255;
}

// 获取订单数据（员工侧）
message GetOrderStaticEmployeeRsp {
  OrderReviewStatic deposit = 1;      // 已下订单
  OrderReviewStatic first = 2;        // 首款订单
  OrderReviewStatic final = 3;        // 尾款待支付
  OrderReviewStatic success = 4;      // 支付成功
  OrderReviewStatic disbursement = 5; // 支款订单
  OrderReviewStatic close = 6;        // 订单关闭
  int64 disbursement_paid = 7;        // 支款订单-支款完成
  int64 final_workflow_completed = 8; // 尾款待支付-尾款待提交
  int64 final_workflow_process = 9;   // 尾款待支付-服务未完成

  base.BaseRsp base = 255;
}

// 排序
message OrderBy {
  string field = 1;     // 排序字段
  OrderByType type = 2; // 排序类型
}

// 获取订单列表
message GetOrderListReq {
  int32 page_num = 1;  // 页数
  int32 page_size = 2; // 每页数量

  string order_no = 3;    // 订单编号（模糊匹配）
  string contract_no = 4; // 合同编号（模糊匹配）

  repeated int64 customer_ids = 5; // 客户UID
  repeated int64 updater_ids = 6;  // 操作人UID（出单成员）
  repeated int64 reviewer_ids = 7; // 审核人UID（最后审核人UID）
  repeated int64 executor_ids = 8; // 派单对象UID
  repeated int64 closed_ids = 9;   // 关闭人UID

  repeated int64 brand_ids = 10;    // 品牌ID
  repeated int64 business_ids = 11; // 业务线ID
  repeated int64 service_ids = 12;  // 服务项目ID

  OrderInstallmentType installment_type = 13;             // 付款方式：分期方式
  repeated OrderDisbursementType disbursement_types = 14; // 支款类型

  repeated StatusOrder statuses = 15;                 // 订单状态
  repeated StatusOrderReview status_reviews = 16;     // 审核状态
  repeated StatusOrderWorkflow status_workflows = 17; // 工单服务状态

  repeated StatusOrderPay status_pay_deposits = 21;      // 定金支付状态
  repeated StatusOrderPay status_pay_firsts = 22;        // 首款支付状态
  repeated StatusOrderPay status_pay_finals = 23;        // 尾款支付状态
  repeated StatusOrderPay status_pay_disbursements = 24; // 支款支付状态

  repeated int64 pay_deposit_at =
      31; // 定金付款时间（定金实际付款日期，单位：毫秒）（[start, end]）
  repeated int64 pay_first_at =
      32; // 首款付款时间（首款实际付款日期，单位：毫秒）（[start, end]）
  repeated int64 pay_final_at =
      33; // 尾款付款时间（尾款实际付款日期，单位：毫秒）（[start, end]）
  repeated int64 pay_disbursement_at =
      34; // 支款付款时间（支款实际付款日期，单位：毫秒）（多次，仅记录最新一次）（[start,
          // end]）

  repeated int64 apply_deposit_at =
      41; // 定金申请审核时间（单位：毫秒）（[start, end]）
  repeated int64 apply_first_at =
      42; // 首款申请审核时间（单位：毫秒）（[start, end]）
  repeated int64 apply_final_at =
      43; // 尾款申请审核时间（单位：毫秒）（[start, end]）
  repeated int64 apply_disbursement_at =
      44; // 支款申请审核时间（多次，仅记录最新一次）（单位：毫秒）（[start,
          // end]）

  repeated int64 pass_deposit_at =
      51; // 定金审核通过时间（单位：毫秒）（[start, end]）
  repeated int64 pass_first_at =
      52; // 首款审核通过时间（单位：毫秒）（[start, end]）
  repeated int64 pass_final_at =
      53; // 尾款审核通过时间（单位：毫秒）（[start, end]）
  repeated int64 pass_disbursement_at =
      54; // 支款审核通过时间（多次，仅记录最新一次）（单位：毫秒）（[start,
          // end]）

  repeated int64 reject_at =
      60; // 审核驳回审核时间（多次，仅记录最新一次）（单位：毫秒）（[start,
          // end]）

  repeated int64 created_at = 61; // 创建时间（单位：毫秒）（[start, end]）
  repeated int64 updated_at = 62; // 更新时间（单位：毫秒）（[start, end]）
  repeated int64 closed_at = 63;  // 关闭时间（单位：毫秒）（[start, end]）

  string brand_name = 71;    // 品牌名称（模糊匹配）
  string business_name = 72; // 业务线名称（模糊匹配）
  string service_name = 73;  // 服务项目名称（模糊匹配）
  string goods_name = 74;    // 商品名称（模糊匹配）

  StatusYesNo foreign_currency = 81; // 收入外币
  StatusYesNo urgent_service = 82;   // 服务加急
  StatusYesNo exempt_final = 83;     // 免除尾款
  StatusYesNo has_scholarship = 84;  // 奖学金
  StatusYesNo auto_schedule = 85;    // 提交订单后自动排单

  repeated OrderBy order_by = 200; // 排序，越靠前权重越高

  base.BaseReq base = 255;
}

// 订单列表数据Item
message OrderListItem {
  OrderEntity order = 1;                     // 订单信息
  OrderPayEntity order_pay = 2;              // 订单付款信息
  repeated OrderGoodsEntity order_goods = 3; // 商品信息
  repeated string contract_no = 4;           // 合同编号
}

// 获取订单列表
message GetOrderListRsp {
  repeated OrderListItem items = 1; // 订单信息
  int64 total = 2;                  // 订单数量

  base.BaseRsp base = 255;
}

// 获取订单列表
message GetOrderListAppReq {
  int32 page_num = 1;                 // 页数
  int32 page_size = 2;                // 每页数量
  int64 customer_id = 5;              // 客户UID
  repeated StatusOrder statuses = 15; // 订单状态

  base.BaseReq base = 255;
}

// 订单列表数据Item
message OrderListAppItem {
  OrderEntity order = 1;                     // 订单信息
  OrderPayEntity order_pay = 2;              // 订单付款信息
  repeated OrderGoodsEntity order_goods = 3; // 商品信息
  repeated string contract_no = 4;           // 合同编号

  int64 transaction_at = 100; // 交易时间（单位：毫秒）仅 app 端逻辑
}

// 获取订单列表
message GetOrderListAppRsp {
  repeated OrderListAppItem items = 1; // 订单信息
  int64 total = 2;                     // 订单数量

  base.BaseRsp base = 255;
}

// 通过订单id批量获取订单
message GetOrderInfoByIdsReq {
  repeated int64 ids = 1;        // 订单ID
  repeated string order_nos = 2; // 订单编号
  base.BaseReq base = 255;
}

// 订单基础信息
message OrderInfoItem {
  OrderEntity order = 1;                     // 订单信息
  OrderPayEntity order_pay = 2;              // 订单付款信息
  repeated OrderGoodsEntity order_goods = 3; // 商品信息
  string contract_no = 4;                    // 合同编号
}

// 通过订单id批量获取订单
message GetOrderInfoByIdsRsp {
  repeated OrderInfoItem items = 1;

  base.BaseRsp base = 255;
}

// 获取订单详情
message GetOrderInfoReq {
  int64 id = 1;        // 订单ID（优先）
  string order_no = 2; // 订单编号（id=0，order_no!= 0时）

  base.BaseReq base = 255;
}

// 获取订单详情
message GetOrderInfoRsp {
  OrderEntity order = 1;                                    // 订单信息
  OrderPayEntity order_pay = 2;                             // 订单付款信息
  repeated OrderGoodsEntity order_goods = 3;                // 商品信息
  repeated OrderOperationLogEntity order_operation_log = 4; // 操作日志信息
  repeated int64 source_ids = 5;     // 订单来源用户ID集合（员工）
  repeated int64 submission_ids = 6; // 共同提交人ID集合（员工）

  base.BaseRsp base = 255;
}

// 保存订单信息（创建/更新）
message SaveOrderReq {
  OrderEntity order = 1;                     // 订单信息
  OrderPayEntity order_pay = 2;              // 订单付款信息
  repeated OrderGoodsEntity order_goods = 3; // 商品信息
  repeated int64 source_ids = 5;             // 订单来源用户ID集合（员工）
  repeated int64 submission_ids = 6;         // 共同提交人ID集合（员工）

  base.BaseReq base = 255;
}

// 保存订单信息（创建/更新）
message SaveOrderRsp {
  int64 id = 1;        // 订单ID
  string order_no = 2; // 订单编号

  base.BaseRsp base = 255;
}

// 更新订单状态
message UpdateOrderStatusReq {
  int64 id = 1;          // 订单ID
  int64 updated_id = 2;  // 创建人（出单成员）
  int64 reviewer_id = 3; // 审核人UID（最后审核人UID）

  StatusOrder status = 4;                     // 订单状态
  StatusOrderReview status_review = 5;        // 审核状态
  StatusOrderPay status_pay_deposit = 6;      // 定金支付状态
  StatusOrderPay status_pay_first = 7;        // 首款支付状态
  StatusOrderPay status_pay_final = 8;        // 尾款支付状态
  StatusOrderPay status_pay_disbursement = 9; // 支款支付状态
  StatusYesNo revoke_disbursement = 10;       // 撤销支款操作标记

  StatusOrderWorkflow status_workflow = 11; // 工单状态

  base.BaseReq base = 255;
}

// 更新订单状态
message UpdateOrderStatusRsp {
  int64 id = 1; // 订单ID

  base.BaseRsp base = 255;
}

// 获取订单商品列表（通过订单ID）
message GetOrderGoodsListReq {
  int32 page_num = 1;  // 页数
  int32 page_size = 2; // 每页数量

  repeated int64 order_ids = 3;  // 订单ID
  OrderGoodsType goods_type = 4; // 商品类型
  string goods_name = 5;         // 商品名称

  repeated OrderBy order_by = 200; // 排序，越靠前权重越高

  base.BaseReq base = 255;
}

// 获取订单商品列表（通过订单ID）
message GetOrderGoodsListRsp {
  repeated OrderGoodsEntity items = 1; // 订单商品信息
  int64 total = 2;                     // 订单数量

  base.BaseRsp base = 255;
}

// 获取订单操作日志列表（通过订单ID）
message GetOrderOperationLogListReq {
  int32 page_num = 1;  // 页数
  int32 page_size = 2; // 每页数量

  repeated int64 order_ids = 3;  // 订单ID
  repeated string order_nos = 4; // 订单编号

  repeated OrderBy order_by = 200; // 排序，越靠前权重越高

  base.BaseReq base = 255;
}

// 获取订单操作日志列表（通过订单ID）
message GetOrderOperationLogListRsp {
  repeated OrderOperationLogEntity items = 1; // 订单操作日志信息
  int64 total = 2;                            // 订单数量

  base.BaseRsp base = 255;
}

// 获取币种信息
message GetCurrencyListReq { base.BaseReq base = 255; }

// 获取币种信息
message GetCurrencyListRsp {
  repeated CurrencyEntity items = 1;

  base.BaseRsp base = 255;
}

// 奖学金-待发放列表
message GetScholarshipToBeDistributedReq {
  int32 page_num = 1;
  int32 page_size = 2;
  int64 customer_id = 3; // 客户UID

  base.BaseReq base = 255;
}

// 奖学金-待发放列表
message ScholarshipToBeDistributed {
  int64 customer_id = 1;    // 客户ID
  int64 order_id = 2;       // 订单ID
  string order_no = 3;      // 订单编号(时间戳+6位随机数)
  int64 brand_id = 4;       // 品牌ID
  int64 business_id = 5;    // 业务线ID
  int64 service_id = 6;     // 服务项目ID
  string brand_name = 7;    // 品牌名称
  string business_name = 8; // 业务线名称
  string service_name = 9;  // 服务项目名称
  string amount_total =
      10; // 实际成交金额（合同金额 = 定金+首期款+尾款+加急费）
  string amount_scholarship = 11;   // 奖学金-金额
  string currency_total = 12;       // 实际成交金额-币种
  string currency_scholarship = 13; // 奖学金-币种
}

// 奖学金-待发放列表
message GetScholarshipToBeDistributedRsp {
  repeated ScholarshipToBeDistributed items = 1;
  int64 total = 2;           // 总条数
  string amount_total = 3;   // 奖学金总金额
  string currency_total = 4; // 奖学金总金额-币种

  base.BaseRsp base = 255;
}

// 奖学金-待发放金额
message GetScholarshipToBeDistributedAmountReq {
  int64 customer_id = 1; // 客户UID

  base.BaseReq base = 255;
}

// 奖学金-待发放金额
message GetScholarshipToBeDistributedAmountRsp {
  string amount_total = 1;   // 奖学金总金额
  string currency_total = 2; // 奖学金总金额-币种

  base.BaseRsp base = 255;
}

// 更换最后编辑者ID
message UpdateUpdaterIdItem {
  int64 before = 1;
  int64 after = 2;
}

// 批量更换最后编辑者ID
message BatchUpdateUpdaterIdReq {
  repeated UpdateUpdaterIdItem items = 1;
  int64 customer_id = 2;

  base.BaseReq base = 255;
}

// 批量更换最后编辑者ID
message BatchUpdateUpdaterIdRsp { base.BaseRsp base = 255; }

// 资产转移（多用户）
message BatchUpdateOrderOwnIdReq {
  // 客户id列表
  repeated int64 customer_ids = 1;
  // 变更前的员工ID
  int64 before = 2;
  // 变更后的员工ID
  int64 after = 3;
}

// 资产转移（多用户）
message BatchUpdateOrderOwnIdRsp { base.BaseRsp base = 255; }

// 重制订单工单状态为已完成的工单状态
message ResetCompleteWorkflowStatusReq {
  repeated int64 order_ids = 1; // 订单ID

  base.BaseReq base = 255;
}

// 重制订单工单状态为已完成的工单状态
message ResetCompleteWorkflowStatusRsp { base.BaseRsp base = 255; }

// 批量更新订单工单状态
message BatchUpdateWorkflowStatusReq {
  repeated int64 order_ids = 1;            // 订单ID
  StatusOrderWorkflow workflow_status = 2; // 工作流状态

  base.BaseReq base = 255;
}

// 批量更新订单工单状态
message BatchUpdateWorkflowStatusRsp { base.BaseRsp base = 255; }

// 获取退款高危客户列表
message GetRefundHighRiskCustomersReq { base.BaseReq base = 255; }

// 获取退款高危客户列表
message GetRefundHighRiskCustomersRsp {
  repeated int64 add_ids = 1;    // 待添加的用户ID集合
  repeated int64 remove_ids = 2; // 待移除的用户ID集合

  base.BaseRsp base = 255;
}

// 获取红线高危客户列表
message GetRedLineRiskCustomersReq { base.BaseReq base = 255; }

// 获取红线高危客户列表
message GetRedLineRiskCustomersRsp {
  repeated int64 add_ids = 1;    // 待添加的用户ID集合
  repeated int64 remove_ids = 2; // 待移除的用户ID集合

  base.BaseRsp base = 255;
}

// 获取新旧客户列表
message GetOldNewCustomersReq { base.BaseReq base = 255; }

// 获取新旧客户列表
message GetOldNewCustomersRsp {
  repeated int64 old = 1; // 客户ID
  repeated int64 new = 2; // 客户ID

  base.BaseRsp base = 255;
}

// 获取工单全部为筛选状态的订单
message GetWorkflowCompleteOrdersReq { base.BaseReq base = 255; }

// 获取工单全部为筛选状态的订单
message GetWorkflowCompleteOrdersRsp {
  repeated int64 add_ids = 1;    // 待添加的订单ID集合
  repeated int64 remove_ids = 2; // 待移除的订单ID集合

  base.BaseRsp base = 255;
}

// 获取最新订单操作日志
message GetLatestOrderOperationLogByOrderIdReq {
  int64 order_id = 1; // 订单ID

  base.BaseReq base = 255;
}

// 获取最新订单操作日志
message GetLatestOrderOperationLogByOrderIdRsp {
  OrderOperationLogEntity order_operation_log = 1; // 最新订单操作日志

  base.BaseRsp base = 255;
}

// 通过用户ID获取最新订单信息
message GetLatestOrderInfoByCustomerIdsReq {
  repeated int64 customer_id = 1; // 客户ID

  base.BaseReq base = 255;
}

// 通过用户ID获取最新订单信息
message GetLatestOrderInfoByCustomerIdsRsp {
  repeated SingleOrderInfoItem order_info = 1; // 最新订单信息

  base.BaseRsp base = 255;
}

message SingleOrderInfoItem {
  OrderEntity order = 1;            // 订单信息
  OrderPayEntity order_pay = 2;     // 订单支付信息
  OrderGoodsEntity order_goods = 3; // 第一条订单商品信息
}

// 通过用户ID获取用户订单数量
message GetOrderCountByCustomerIdReq {
  int64 customer_id = 1; // 客户ID
  repeated StatusOrder status =
      2; // 订单状态（不传表示获取全部状态，传就是且逻辑）
  repeated StatusOrderReview status_review =
      3; // 订单审核状态（不传表示获取全部状态，传就是且逻辑）

  base.BaseReq base = 255;
}

// 通过用户ID获取用户订单数量
message GetOrderCountByCustomerIdRsp {
  int64 total = 1;

  base.BaseRsp base = 255;
}

// 通过用户ID批量获取用户订单数量
message GetOrderCountByCustomerIdsReq {
  repeated int64 customer_ids = 1; // 客户ID
  repeated StatusOrder status =
      2; // 订单状态（不传表示获取全部状态，传就是且逻辑）
  repeated StatusOrderReview status_review =
      3; // 订单审核状态（不传表示获取全部状态，传就是且逻辑）

  base.BaseReq base = 255;
}

// 通过用户ID批量获取用户订单数量
message GetOrderCountByCustomerIdsItem {
  int64 customer_id = 1;
  int64 total = 2;
}

// 通过用户ID批量获取用户订单数量
message GetOrderCountByCustomerIdsRsp {
  repeated GetOrderCountByCustomerIdsItem items = 1;

  base.BaseRsp base = 255;
}

// 通过订单ID获取订单的操作日志
message GetOrderOperationLogByOrderIdReq {
  int32 page_num = 1;  // 页数
  int32 page_size = 2; // 每页数量

  int64 id = 3;        // 订单ID（优先）
  string order_no = 4; // 订单编号（id=0，order_no!= 0时）

  repeated OrderBy order_by = 200; // 排序，越靠前权重越高

  base.BaseReq base = 255;
}

// 通过订单ID获取订单的操作日志
message GetOrderOperationLogByOrderIdRsp {
  repeated OrderOperationLogEntity items = 1; // 订单操作日志
  int64 total = 2;                            // 订单数量

  base.BaseRsp base = 255;
}
// 更新订单的操作人
message UpdateOrderRelationReq {
  // 订单ID
  int64 order_id = 1;
  // 操作类型
  OrderRelationAction action = 2;
  // 关联ID
  repeated int64 relation_ids = 3;
}
message UpdateOrderRelationRsp { base.BaseRsp base = 255; }

// 订单工单节点记录
message GetOrderWorkflowLogReq {
  int32 page_num = 1;              // 页数
  int32 page_size = 2;             // 每页数量
  repeated int64 customer_ids = 3; // 客户UID
  repeated int64 updater_ids = 4;  // 操作人UID（出单成员）

  repeated OrderBy order_by = 200; // 排序，越靠前权重越高

  base.BaseReq base = 255;
}

// 订单工单节点记录
message GetOrderWorkflowLogRsp {
  repeated OrderWorkflowLogEntity items = 1; // 订单操作日志
  int64 total = 2;                           // 日志数量

  base.BaseRsp base = 255;
}

// 关闭订单工单节点记录
message CloseOrderWorkflowLogReq {
  repeated int64 ids = 1;
  int64 closed_by = 2; // 关闭人UID

  base.BaseReq base = 255;
}

// 关闭订单工单节点记录
message CloseOrderWorkflowLogRsp { base.BaseRsp base = 255; }

// 收款单驳回后更新订单状态请求
message FundRejectUpdateOrderStatusReq {
  int64 order_id = 1; // 订单ID
  int32 fund_type =
      2; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  int64 updated_by = 3;     // 操作人ID
  string reject_reason = 4; // 驳回原因
}

// 收款单驳回后更新订单状态返回
message FundRejectUpdateOrderStatusRsp {
  int64 order_id = 1;          // 订单ID
  int32 new_status = 2;        // 新的订单状态
  int32 new_status_review = 3; // 新的审核状态
  base.BaseRsp base = 255;
}
