syntax = "proto3";
package tools;
option go_package = "server/cmd/tools";
import "base.proto";

enum HomeNoticeType {
  HomeNoticeTypeUnkown = 0;
  HomeNoticeTypeSystem = 1; // 系统通知
  HomeNoticeTypeCompany = 2; // 公司公告
}

message HomeNotice {
  int64 id = 1; //主键
  string title = 2; //标题
  string content = 3; //内容
  HomeNoticeType type = 4; //类型
  int64 updated_at = 6; //更新时间
  int64 updated_by = 7; //编辑人
}

message ListHomeNoticeReq {
  HomeNoticeType type = 1; //类型
  int32 page_num = 2; // <必填> 页码
  int32 page_size = 3; // <必填> 每页数量
}

message ListHomeNoticeRsp {
  repeated HomeNotice items = 1; //列表
  int64 total = 2; //总数

  base.BaseRsp base = 255;
}

message SaveHomeNoticeReq {
  int64 id = 1; //主键
  string title = 2; //标题
  string content = 3; //内容
  HomeNoticeType type = 4; //类型
  int64 updated_by = 5; //编辑人
}

message SaveHomeNoticeRsp {
  int64 id = 1; //主键

  base.BaseRsp base = 255;
}

message DeleteHomeNoticeReq {
  int64 id = 1; //主键
  int64 updated_by = 2; //编辑人
}

message DeleteHomeNoticeRsp {
  base.BaseRsp base = 255;
}

message GetHomeNoticeReq {
  int64 id = 1; //主键
}

message GetHomeNoticeRsp {
  HomeNotice notice = 1; //公告

  base.BaseRsp base = 255;
}