syntax = "proto3";

package customer;
option go_package = "server/cmd/customer";
import "base.proto";

/**
 * 客户托管邮箱账号
 */
message CustomerProvisionedEmail {
  int64 id = 1;           ///< 主键
  int64 customer_id = 2;  ///< 客户id
  string describe = 3;    ///< 描述说明
  string url = 4;         ///< 邮箱地址
  string password = 5;    ///< 密码
  int32 forward = 6;      ///< 是否设置转发（1：否 2：是）
  string forward_url = 7; ///< 转发邮箱地址
  int64 created_at = 8;   ///< 创建时间
  int64 updated_at = 9;   ///< 更新时间
}

/**
 * 创建客户托管邮箱账号请求
 */
message CreateCustomerProvisionedEmailReq {
  int64 customer_id = 1;  ///< 客户id
  string describe = 2;    ///< 描述说明
  string url = 3;         ///< 邮箱地址
  string password = 4;    ///< 密码
  int32 forward = 5;      ///< 是否设置转发（1：否 2：是）
  string forward_url = 6; ///< 转发邮箱地址
}

/**
 * 创建客户托管邮箱账号响应
 */
message CreateCustomerProvisionedEmailRsp {
  CustomerProvisionedEmail email = 1; ///< 新建的邮箱账号信息
  base.BaseRsp base = 255;
}

/**
 * 更新客户托管邮箱账号请求
 */
message UpdateCustomerProvisionedEmailReq {
  int64 id = 1;           ///< 主键
  string describe = 3;    ///< 描述说明
  string url = 4;         ///< 邮箱地址
  string password = 5;    ///< 密码
  int32 forward = 6;      ///< 是否设置转发（1：否 2：是）
  string forward_url = 7; ///< 转发邮箱地址
}

/**
 * 更新客户托管邮箱账号响应
 */
message UpdateCustomerProvisionedEmailRsp {
  CustomerProvisionedEmail email = 1; ///< 更新后的邮箱账号信息
  base.BaseRsp base = 255;
}

/**
 * 删除客户托管邮箱账号请求
 */
message DeleteCustomerProvisionedEmailReq {
  int64 id = 1; ///< 主键
}

/**
 * 删除客户托管邮箱账号响应
 */
message DeleteCustomerProvisionedEmailRsp { base.BaseRsp base = 255; }

/**
 * 查询客户托管邮箱账号列表请求（全量，无分页）
 */
message ListCustomerProvisionedEmailsReq {
  int64 customer_id = 1; ///< 客户id
}

/**
 * 查询客户托管邮箱账号列表响应
 */
message ListCustomerProvisionedEmailsRsp {
  repeated CustomerProvisionedEmail emails = 1; ///< 邮箱账号列表
  base.BaseRsp base = 255;
}

/**
 * 批量更新客户托管邮箱账号请求
 */
message BatchUpdateCustomerProvisionedEmailsReq {
  int64 customer_id = 1; ///< 客户id，用于新增操作
  repeated UpdateCustomerProvisionedEmailReq updates =
      2; ///< 批量更新请求列表，空列表表示删除所有
}

/**
 * 批量更新客户托管邮箱账号响应
 */
message BatchUpdateCustomerProvisionedEmailsRsp {
  repeated CustomerProvisionedEmail emails =
      1; ///< 更新后的邮箱账号列表（全部成功时返回）
  base.BaseRsp base = 255;
}
