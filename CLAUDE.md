# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development Workflow
```bash
# Update database models after schema changes
make update_model

# Update all IDL-generated code
make update

# Update specific service IDL
make update_order          # or update_admin_api, update_auth, etc.

# Build all services
make build

# Build specific service
make build_order          # or build_admin_api, build_auth, etc.
```

### Running Services Locally
```bash
# Run individual services
make auth                 # Authentication service
make order               # Order/financial service
make customer            # Customer management
make admin_api           # Admin HTTP API
make app_api             # Client application API
make workflow            # Workflow service

# Run all-in-one for development
make allinone

# Environment setup
export HOST_ENV=local
export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore
```

### Testing Commands
```bash
# Linting
make lint

# Integration tests (requires nacos + database)
make test_integration

# Integration test for specific module
INTEGRATION_TEST=true go test ./server/cmd/order/internal/services/ -run FinancialFundIntegrationTestSuite -v
```

## Architecture Overview

This is a **microservices education platform** built with ByteDance's CloudWeGo stack, using **IDL-first development** with protobuf definitions driving all code generation.

### Service Architecture

**HTTP API Services (Hertz):**
- `admin_api` - Admin dashboard APIs
- `app_api` - Client application APIs
- `app_employee_api` - Employee mobile APIs
- `websocket` - Real-time communication

**RPC Services (Kitex):**
- `auth` - Authentication & authorization
- `order` - Orders, payments, financial operations
- `customer` - Customer/student management
- `product` - Educational product catalog
- `user` - User & employee management
- `workflow` - Business process workflows
- `message` - Messaging & notifications
- `tools` - Shared utilities & integrations

### Key Frameworks
- **Hertz** - High-performance HTTP framework for APIs
- **Kitex** - RPC framework with protobuf support
- **GORM + GORM Gen** - Database ORM with code generation
- **Nacos** - Service discovery and configuration
- **MySQL** with read/write splitting

### Code Generation Workflow

1. **IDL Definitions**: All APIs defined in `/idl/` directory
2. **Generated Code**: Auto-generated clients/servers in `/kitex_gen/`
3. **Database Models**: Auto-generated from schema in `/pkg/dao/`
4. **Regeneration**: Use `make update` after IDL changes

### Service Structure Pattern
```
server/cmd/{service}/
├── main.go                    # Service entry point
├── handler_*.go              # RPC handlers
├── internal/
│   ├── config/              # Configuration
│   ├── models/              # Business logic
│   ├── services/            # Service layer
│   └── initialize/          # Initialization
└── allinone/                # Single-binary deployment
```

## Development Guidelines

### Code Generation Workflow
1. **Always regenerate** after IDL changes: `make update`
2. **Database models** regenerated with: `make update_model`
3. **Never edit generated files** directly - they will be overwritten

### Service Development
1. **Use service-specific make targets** for running services locally
2. **Environment setup** required for local development (see commands above)
3. **Nacos configuration** needed for service discovery and config management
4. **Follow existing patterns** in service handlers and business logic

### Financial Module Context
The financial module (`order` service) handles complex payment processing with:
- Multi-currency support
- Payment installments (deposit, first payment, final payment)
- Refund processing with approval workflows
- Transaction logging and audit trails

The financial module has been significantly refactored - refer to `/docs/financial/` for detailed documentation on the business logic and architecture patterns.

### Testing Guidelines
- **Unit tests** alongside source files
- **Integration tests** in dedicated files (require real database)
- **Set environment variables** for integration tests:
  - `INTEGRATION_TEST=true`
  - `HOST_ENV=test`
  - `GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore`

### Database Management
- **Migrations** managed in `/migrations/` directory
- **Generated DAO** layer in `/pkg/dao/` provides type-safe database access
- **GORM conventions** used throughout for consistency

### Configuration Management
- **Nacos** provides centralized configuration
- **Environment-specific** namespaces (local, dev, test, prod)
- **Hot reloading** supported for configuration changes

## Important File Locations

- **IDL Definitions**: `/idl/api/` (HTTP APIs), `/idl/svc/` (RPC services)
- **Generated Code**: `/kitex_gen/` (RPC), service `/biz/model/` (HTTP models)
- **Database Models**: `/pkg/dao/` (auto-generated)
- **Business Logic**: `server/cmd/{service}/internal/`
- **Documentation**: `/docs/` (comprehensive project docs)
- **Configuration**: `/config/nacos_config.yaml`
- **Build Scripts**: `/script/make.sh`

## Common Patterns

### Error Handling
Use the structured error system in `pkg/coderror/` for consistent error responses across services.

### Logging
Structured logging is configured in `pkg/logger/` with appropriate context and tracing.

### Database Operations
Always use the generated DAO layer in `pkg/dao/` for database operations. The models include built-in support for soft deletes, timestamps, and transaction handling.

### Service Communication
Inter-service communication uses Kitex RPC with automatic service discovery via Nacos. Generated clients are available in `/kitex_gen/`.