package utils

import "time"

// holidayData 存储了每年的法定节假日。
// Key: 年份 (int)
// Value: map[string]struct{}, 其中 key 是 "YYYY-MM-DD" 格式的日期字符串。
// TODO: 未来可从配置中心（如Nacos）动态加载，以实现无需重启服务的更新。
var holidayData = map[int]map[string]struct{}{
	2025: {
		// 元旦
		"2025-01-01": {},
		// 春节
		"2025-01-28": {}, "2025-01-29": {}, "2025-01-30": {}, "2025-01-31": {},
		"2025-02-01": {}, "2025-02-02": {}, "2025-02-03": {}, "2025-02-04": {},
		// 清明节
		"2025-04-04": {}, "2025-04-05": {}, "2025-04-06": {},
		// 劳动节
		"2025-05-01": {}, "2025-05-02": {}, "2025-05-03": {}, "2025-05-04": {}, "2025-05-05": {},
		// 端午节
		"2025-05-31": {}, "2025-06-01": {}, "2025-06-02": {},
		// 国庆节、中秋节
		"2025-10-01": {}, "2025-10-02": {}, "2025-10-03": {}, "2025-10-04": {},
		"2025-10-05": {}, "2025-10-06": {}, "2025-10-07": {}, "2025-10-08": {},
	},
	// TODO: 在此扩展2026年的法定节假日
	2026: {},
	// TODO: 在此扩展2027年的法定节假日
	2027: {},
}

// makeupWorkdayData 存储了每年的调休工作日。
// 结构同 holidayData
var makeupWorkdayData = map[int]map[string]struct{}{
	2025: {
		"2025-01-26": {}, // 春节调休
		"2025-02-08": {}, // 春节调休
		"2025-04-27": {}, // 劳动节调休
		"2025-09-28": {}, // 国庆节调休
		"2025-10-11": {}, // 国庆节调休
	},
	// TODO: 在此扩展2026年的调休工作日
	2026: {},
	// TODO: 在此扩展2027年的调休工作日
	2027: {},
}

const layout = "2006-01-02"

// IsWorkday 检查给定日期是否为工作日。
// 该函数会根据年份自动查找对应的法定节假日和调休安排。
func IsWorkday(date time.Time) bool {
	year := date.Year()
	dateStr := date.Format(layout)

	// 检查是否为调休上班的周末
	if yearMakeupDays, ok := makeupWorkdayData[year]; ok {
		if _, isMakeup := yearMakeupDays[dateStr]; isMakeup {
			return true
		}
	}

	// 检查是否为法定节假日
	if yearHolidays, ok := holidayData[year]; ok {
		if _, isHoliday := yearHolidays[dateStr]; isHoliday {
			return false
		}
	}

	// 检查是否为普通周末
	weekday := date.Weekday()
	if weekday == time.Saturday || weekday == time.Sunday {
		return false
	}

	return true
}

// AddWorkdays 向给定日期添加或减去n个工作日。
// - n为正数则增加工作日（未来日期）。
// - n为负数则减少工作日（过去日期）。
// 该函数会跳过周末和法定节假日。
func AddWorkdays(date time.Time, n int) time.Time {
	result := date
	if n == 0 {
		for !IsWorkday(result) {
			result = result.AddDate(0, 0, 1)
		}
		return result
	}

	step := 1 // 1天
	if n < 0 {
		step = -1
		n = -n
	}

	// 如果起始日期不是工作日，先移动到下一个工作日
	for !IsWorkday(result) {
		result = result.AddDate(0, 0, step)
	}

	for i := 0; i < n; {
		result = result.AddDate(0, 0, step)
		if IsWorkday(result) {
			i++
		}
	}
	return result
}

// SubWorkdays 是一个方便的函数，用于从日期中减去n个工作日。
// 它等效于使用负数n调用AddWorkdays。
func SubWorkdays(date time.Time, n int) time.Time {
	result := date
	// 如果n为0，并且当天不是工作日，需要找到前一个工作日
	if n == 0 {
		for !IsWorkday(result) {
			result = result.AddDate(0, 0, -1)
		}
		return result
	}

	step := -1

	// 如果起始日期不是工作日，先移动到前一个工作日
	// 这样可以确保计算的起点是一个有效的工作日
	for !IsWorkday(result) {
		result = result.AddDate(0, 0, step)
	}

	// 从有效工作日的起点开始，减去 n 个工作日
	for i := 0; i < n; {
		result = result.AddDate(0, 0, step)
		if IsWorkday(result) {
			i++
		}
	}

	return result
}
