package utils

import (
	"fmt"
	"strconv"
	"testing"
)

/**
 * 测试通用切片转换函数
 */
func TestConvertSlice(t *testing.T) {
	// 测试 int 到 string 的转换
	intSlice := []int{1, 2, 3, 4, 5}
	stringSlice := ConvertSlice(intSlice, func(v int) string {
		return fmt.Sprintf("num_%d", v)
	})

	expected := []string{"num_1", "num_2", "num_3", "num_4", "num_5"}
	if len(stringSlice) != len(expected) {
		t.<PERSON><PERSON>("Length mismatch: got %d, want %d", len(stringSlice), len(expected))
	}

	for i, v := range stringSlice {
		if v != expected[i] {
			t.<PERSON><PERSON><PERSON>("Value mismatch at index %d: got %s, want %s", i, v, expected[i])
		}
	}
}

/**
 * 测试空切片转换
 */
func TestConvertSliceEmpty(t *testing.T) {
	emptySlice := []int{}
	result := ConvertSlice(emptySlice, func(v int) string {
		return strconv.Itoa(v)
	})

	if len(result) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected empty slice, got length %d", len(result))
	}
}

/**
 * 测试转换为 int32
 */
func TestConvertSliceToInt32(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected []int32
	}{
		{
			name:     "int to int32",
			input:    []int{1, 2, 3},
			expected: []int32{1, 2, 3},
		},
		{
			name:     "int64 to int32",
			input:    []int64{10, 20, 30},
			expected: []int32{10, 20, 30},
		},
		{
			name:     "uint32 to int32",
			input:    []uint32{100, 200, 300},
			expected: []int32{100, 200, 300},
		},
		{
			name:     "empty slice",
			input:    []int{},
			expected: []int32{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result []int32

			switch v := tt.input.(type) {
			case []int:
				result = ConvertSliceToInt32(v)
			case []int64:
				result = ConvertSliceToInt32(v)
			case []uint32:
				result = ConvertSliceToInt32(v)
			}

			if len(result) != len(tt.expected) {
				t.Errorf("Length mismatch: got %d, want %d", len(result), len(tt.expected))
			}

			for i, v := range result {
				if v != tt.expected[i] {
					t.Errorf("Value mismatch at index %d: got %d, want %d", i, v, tt.expected[i])
				}
			}
		})
	}
}

/**
 * 测试转换为 int64
 */
func TestConvertSliceToInt64(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected []int64
	}{
		{
			name:     "int32 to int64",
			input:    []int32{1, 2, 3},
			expected: []int64{1, 2, 3},
		},
		{
			name:     "uint to int64",
			input:    []uint{10, 20, 30},
			expected: []int64{10, 20, 30},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result []int64

			switch v := tt.input.(type) {
			case []int32:
				result = ConvertSliceToInt64(v)
			case []uint:
				result = ConvertSliceToInt64(v)
			}

			if len(result) != len(tt.expected) {
				t.Errorf("Length mismatch: got %d, want %d", len(result), len(tt.expected))
			}

			for i, v := range result {
				if v != tt.expected[i] {
					t.Errorf("Value mismatch at index %d: got %d, want %d", i, v, tt.expected[i])
				}
			}
		})
	}
}

/**
 * 测试转换为字符串
 */
func TestConvertSliceToString(t *testing.T) {
	// 测试数字到字符串的转换
	intSlice := []int{1, 2, 3}
	stringSlice := ConvertSliceToString(intSlice, func(v int) string {
		return strconv.Itoa(v)
	})

	expected := []string{"1", "2", "3"}
	if len(stringSlice) != len(expected) {
		t.Errorf("Length mismatch: got %d, want %d", len(stringSlice), len(expected))
	}

	for i, v := range stringSlice {
		if v != expected[i] {
			t.Errorf("Value mismatch at index %d: got %s, want %s", i, v, expected[i])
		}
	}
}

/**
 * 测试实际使用场景 - PaymentAccountTypes 转换
 */
func TestPaymentAccountTypesConversion(t *testing.T) {
	// 模拟实际场景中的枚举类型转换
	type PaymentAccountType int32

	paymentTypes := []PaymentAccountType{1, 2, 3, 4}
	int32Types := ConvertSliceToInt32(paymentTypes)

	expected := []int32{1, 2, 3, 4}
	if len(int32Types) != len(expected) {
		t.Errorf("Length mismatch: got %d, want %d", len(int32Types), len(expected))
	}

	for i, v := range int32Types {
		if v != expected[i] {
			t.Errorf("Value mismatch at index %d: got %d, want %d", i, v, expected[i])
		}
	}
}
