package utils

import (
	"testing"
)

// 创建一个简单的测试用结构体，避免依赖其他包
type testStruct struct {
	ID   int32
	Name string
}

func Test_IsZeroValue(t *testing.T) {
	tests := []struct {
		name  string
		value interface{}
		want  bool
	}{
		// nil 值测试
		{
			name:  "nil value",
			value: nil,
			want:  true,
		},

		// 基础类型测试
		{
			name:  "empty string",
			value: "",
			want:  true,
		},
		{
			name:  "non-empty string",
			value: "hello",
			want:  false,
		},

		// 整数类型测试
		{
			name:  "int zero",
			value: 0,
			want:  true,
		},
		{
			name:  "int non-zero",
			value: 1,
			want:  false,
		},
		{
			name:  "int32 zero",
			value: int32(0),
			want:  true,
		},
		{
			name:  "int32 non-zero",
			value: int32(1),
			want:  false,
		},
		{
			name:  "int64 zero",
			value: int64(0),
			want:  true,
		},
		{
			name:  "int64 non-zero",
			value: int64(1),
			want:  false,
		},

		// 无符号整数测试
		{
			name:  "uint zero",
			value: uint(0),
			want:  true,
		},
		{
			name:  "uint non-zero",
			value: uint(1),
			want:  false,
		},
		{
			name:  "uint32 zero",
			value: uint32(0),
			want:  true,
		},
		{
			name:  "uint32 non-zero",
			value: uint32(1),
			want:  false,
		},

		// 浮点数测试
		{
			name:  "float32 zero",
			value: float32(0),
			want:  true,
		},
		{
			name:  "float32 non-zero",
			value: float32(1.1),
			want:  false,
		},
		{
			name:  "float64 zero",
			value: float64(0),
			want:  true,
		},
		{
			name:  "float64 non-zero",
			value: float64(1.1),
			want:  false,
		},

		// interface{} 包装的基础类型测试
		{
			name:  "interface with int32 zero",
			value: interface{}(int32(0)),
			want:  true,
		},
		{
			name:  "interface with int32 non-zero",
			value: interface{}(int32(1)),
			want:  false,
		},
		{
			name:  "interface with int64 zero",
			value: interface{}(int64(0)),
			want:  true,
		},
		{
			name:  "interface with int64 non-zero",
			value: interface{}(int64(1)),
			want:  false,
		},

		// 切片测试
		{
			name:  "empty int slice",
			value: []int{},
			want:  true,
		},
		{
			name:  "non-empty int slice",
			value: []int{1, 2, 3},
			want:  false,
		},
		{
			name:  "empty string slice",
			value: []string{},
			want:  true,
		},
		{
			name:  "non-empty string slice",
			value: []string{"a", "b"},
			want:  false,
		},

		// 指针测试
		{
			name:  "nil pointer",
			value: (*int)(nil),
			want:  true,
		},
		{
			name:  "pointer to zero value",
			value: func() interface{} { x := 0; return &x }(),
			want:  false, // 指针非nil，所以不是零值
		},

		// 结构体测试
		{
			name:  "struct with zero values",
			value: testStruct{},
			want:  true,
		},
		{
			name:  "struct with non-zero values",
			value: testStruct{ID: 1, Name: "test"},
			want:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isZeroValue(tt.value); got != tt.want {
				t.Errorf("isZeroValue() = %v, want %v", got, tt.want)
			}
		})
	}
}
