package utils

import (
	"fmt"
	"regexp"
	"testing"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestModel 测试用的模型
type TestModel struct {
	ID        int64 `gorm:"primaryKey"`
	Name      string
	CreatedAt int64
}

func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		panic("failed to connect database")
	}

	// 创建测试表
	db.Exec(`CREATE TABLE test_models (
		id INTEGER PRIMARY KEY,
		name TEXT,
		created_at DATETIME
	)`)

	return db
}

func TestTimeBetweenSQLGeneration(t *testing.T) {
	db := setupTestDB()
	qb := NewQueryBuilder()

	startMillis := int64(1672617600000) // 2023-01-02 00:00:00 UTC (毫秒)
	endMillis := int64(1672704000000)   // 2023-01-03 00:00:00 UTC (毫秒)

	qb.TimeBetween("created_at", startMillis, endMillis)

	var results []TestModel
	finalDB := qb.Apply(db.Model(&TestModel{}))

	// 生成SQL但不执行
	sql := finalDB.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Find(&results)
	})

	// 验证SQL包含正确的FROM_UNIXTIME调用和转换后的秒级时间戳
	expectedStartSeconds := startMillis / 1000 // 1672617600
	expectedEndSeconds := endMillis / 1000     // 1672704000

	// 使用正则表达式验证SQL结构
	expectedPattern := fmt.Sprintf(`created_at BETWEEN FROM_UNIXTIME\(%d\) AND FROM_UNIXTIME\(%d\)`,
		expectedStartSeconds, expectedEndSeconds)

	matched, err := regexp.MatchString(expectedPattern, sql)
	if err != nil {
		t.Fatalf("Regex compilation failed: %v", err)
	}

	if !matched {
		t.Errorf("Generated SQL doesn't match expected pattern")
		t.Logf("Generated SQL: %s", sql)
		t.Logf("Expected pattern: %s", expectedPattern)
	}

	// 验证时间戳转换是否正确
	if !contains(sql, fmt.Sprintf("%d", expectedStartSeconds)) {
		t.Errorf("SQL should contain converted start timestamp %d", expectedStartSeconds)
	}

	if !contains(sql, fmt.Sprintf("%d", expectedEndSeconds)) {
		t.Errorf("SQL should contain converted end timestamp %d", expectedEndSeconds)
	}

	// 验证不包含原始的毫秒时间戳（确保转换发生了）
	if contains(sql, fmt.Sprintf("%d", startMillis)) {
		t.Errorf("SQL should not contain original millisecond timestamp %d", startMillis)
	}

	if contains(sql, fmt.Sprintf("%d", endMillis)) {
		t.Errorf("SQL should not contain original millisecond timestamp %d", endMillis)
	}

	t.Logf("✅ Generated SQL: %s", sql)
}

func TestTimeBetweenWithZeroValues(t *testing.T) {
	qb := NewQueryBuilder()

	// 测试零值 - 不应该添加条件
	qb.TimeBetween("created_at", 0, 1672617600000)
	qb.TimeBetween("created_at", 1672617600000, 0)
	qb.TimeBetween("created_at", 0, 0)

	if len(qb.conditions) != 0 {
		t.Errorf("Expected 0 conditions for zero values, got %d", len(qb.conditions))
	}
}

func TestTimeBetweenConditionGeneration(t *testing.T) {
	qb := NewQueryBuilder()
	startMillis := int64(1672617600000) // 毫秒时间戳
	endMillis := int64(1672704000000)

	qb.TimeBetween("created_at", startMillis, endMillis)

	if len(qb.conditions) != 1 {
		t.Fatalf("Expected 1 condition, got %d", len(qb.conditions))
	}

	condition := qb.conditions[0]
	if condition.Type != ConditionTypeSimple {
		t.Errorf("Expected ConditionTypeSimple, got %s", condition.Type)
	}

	if condition.Operator != "TIME_BETWEEN" {
		t.Errorf("Expected TIME_BETWEEN operator, got %s", condition.Operator)
	}

	if condition.Field != "created_at" {
		t.Errorf("Expected created_at field, got %s", condition.Field)
	}

	values, ok := condition.Value.([]interface{})
	if !ok {
		t.Fatalf("Expected []interface{} value type")
	}

	if len(values) != 2 {
		t.Fatalf("Expected 2 values, got %d", len(values))
	}

	if values[0] != startMillis {
		t.Errorf("Expected start value %d, got %v", startMillis, values[0])
	}

	if values[1] != endMillis {
		t.Errorf("Expected end value %d, got %v", endMillis, values[1])
	}
}

func TestQueryBuilderChaining(t *testing.T) {
	qb := NewQueryBuilder()

	qb.Equal("status", 1).
		In("type", []int{1, 2, 3}).
		TimeBetween("created_at", 1672617600000, 1672704000000).
		Like("name", "test")

	if len(qb.conditions) != 4 {
		t.Errorf("Expected 4 conditions, got %d", len(qb.conditions))
	}

	// 验证 TimeBetween 条件
	var timeBetweenFound bool
	for _, condition := range qb.conditions {
		if condition.Operator == "TIME_BETWEEN" {
			timeBetweenFound = true
			break
		}
	}

	if !timeBetweenFound {
		t.Error("TimeBetween condition not found in chained query")
	}
}

func TestTimeBetweenComplexQuery(t *testing.T) {
	db := setupTestDB()
	qb := NewQueryBuilder()

	// 测试复杂查询中的 TimeBetween
	qb.Equal("status", 1).
		In("type", []int{1, 2, 3}).
		TimeBetween("created_at", 1672617600000, 1672704000000).
		Like("name", "test")

	var results []TestModel
	finalDB := qb.Apply(db.Model(&TestModel{}))

	sql := finalDB.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Find(&results)
	})

	// 验证所有条件都存在
	if !contains(sql, "status = 1") {
		t.Error("SQL should contain status condition")
	}

	if !contains(sql, "type IN") {
		t.Error("SQL should contain type IN condition")
	}

	if !contains(sql, "name LIKE") {
		t.Error("SQL should contain name LIKE condition")
	}

	if !contains(sql, "FROM_UNIXTIME(1672617600)") {
		t.Error("SQL should contain converted start timestamp")
	}

	if !contains(sql, "FROM_UNIXTIME(1672704000)") {
		t.Error("SQL should contain converted end timestamp")
	}

	t.Logf("✅ Complex query SQL: %s", sql)
}

func TestTimeBetweenEdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		start       int64
		end         int64
		shouldExist bool
		description string
	}{
		{
			name:        "both_zero",
			start:       0,
			end:         0,
			shouldExist: false,
			description: "Both zero values should not create condition",
		},
		{
			name:        "start_zero",
			start:       0,
			end:         1672617600000,
			shouldExist: false,
			description: "Start zero should not create condition",
		},
		{
			name:        "end_zero",
			start:       1672617600000,
			end:         0,
			shouldExist: false,
			description: "End zero should not create condition",
		},
		{
			name:        "both_valid",
			start:       1672617600000,
			end:         1672704000000,
			shouldExist: true,
			description: "Both valid values should create condition",
		},
		{
			name:        "same_values",
			start:       1672617600000,
			end:         1672617600000,
			shouldExist: true,
			description: "Same start and end values should create condition",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			qb := NewQueryBuilder()
			qb.TimeBetween("created_at", tt.start, tt.end)

			conditionExists := len(qb.conditions) > 0
			if conditionExists != tt.shouldExist {
				t.Errorf("Test %s: %s. Expected condition exists: %v, got: %v",
					tt.name, tt.description, tt.shouldExist, conditionExists)
			}

			if tt.shouldExist {
				db := setupTestDB()
				var results []TestModel
				finalDB := qb.Apply(db.Model(&TestModel{}))

				sql := finalDB.ToSQL(func(tx *gorm.DB) *gorm.DB {
					return tx.Find(&results)
				})

				expectedStartSeconds := tt.start / 1000
				expectedEndSeconds := tt.end / 1000

				if !contains(sql, fmt.Sprintf("FROM_UNIXTIME(%d)", expectedStartSeconds)) {
					t.Errorf("SQL should contain converted start timestamp %d", expectedStartSeconds)
				}

				if !contains(sql, fmt.Sprintf("FROM_UNIXTIME(%d)", expectedEndSeconds)) {
					t.Errorf("SQL should contain converted end timestamp %d", expectedEndSeconds)
				}

				t.Logf("✅ %s SQL: %s", tt.name, sql)
			}
		})
	}
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && findSubstring(s, substr) != -1
}

func findSubstring(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}
