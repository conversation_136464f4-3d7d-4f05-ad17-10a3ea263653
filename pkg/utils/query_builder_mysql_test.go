//go:build integration
// +build integration

package utils

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/mysql"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestModelMySQL 用于 MySQL 测试的模型
type TestModelMySQL struct {
	ID        int64     `gorm:"primaryKey"`
	Name      string    `gorm:"type:varchar(255)"`
	CreatedAt time.Time `gorm:"type:datetime(3);autoCreateTime:milli"`
}

func (TestModelMySQL) TableName() string {
	return "test_models"
}

func setupMySQLTestDB(t *testing.T) (*gorm.DB, func()) {
	ctx := context.Background()

	// 启动 MySQL 容器
	mysqlContainer, err := mysql.RunContainer(ctx,
		testcontainers.WithImage("mysql:8.0"),
		mysql.WithDatabase("testdb"),
		mysql.WithUsername("testuser"),
		mysql.WithPassword("testpass"),
		mysql.WithScripts("init.sql"), // 可选：初始化脚本
	)
	if err != nil {
		t.Fatalf("Failed to start MySQL container: %v", err)
	}

	// 获取连接字符串
	connectionString, err := mysqlContainer.ConnectionString(ctx)
	if err != nil {
		t.Fatalf("Failed to get connection string: %v", err)
	}

	// 连接到数据库
	db, err := gorm.Open(mysql.Open(connectionString), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		t.Fatalf("Failed to connect to MySQL: %v", err)
	}

	// 自动迁移表结构
	err = db.AutoMigrate(&TestModelMySQL{})
	if err != nil {
		t.Fatalf("Failed to migrate table: %v", err)
	}

	// 返回清理函数
	cleanup := func() {
		if err := mysqlContainer.Terminate(ctx); err != nil {
			t.Logf("Failed to terminate MySQL container: %v", err)
		}
	}

	return db, cleanup
}

func TestTimeBetweenWithRealMySQL(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	db, cleanup := setupMySQLTestDB(t)
	defer cleanup()

	// 插入测试数据
	testData := []TestModelMySQL{
		{ID: 1, Name: "record1", CreatedAt: time.Unix(1672531200, 0)}, // 2023-01-01 00:00:00 UTC
		{ID: 2, Name: "record2", CreatedAt: time.Unix(1672617600, 0)}, // 2023-01-02 00:00:00 UTC
		{ID: 3, Name: "record3", CreatedAt: time.Unix(1672704000, 0)}, // 2023-01-03 00:00:00 UTC
		{ID: 4, Name: "record4", CreatedAt: time.Unix(1672790400, 0)}, // 2023-01-04 00:00:00 UTC
	}

	for _, data := range testData {
		if err := db.Create(&data).Error; err != nil {
			t.Fatalf("Failed to insert test data: %v", err)
		}
	}

	// 测试 TimeBetween 查询
	// 查询 2023-01-02 到 2023-01-03 之间的记录（包含边界）
	startMillis := int64(1672617600000) // 2023-01-02 00:00:00 UTC (毫秒)
	endMillis := int64(1672704000000)   // 2023-01-03 00:00:00 UTC (毫秒)

	qb := NewQueryBuilder()
	qb.TimeBetween("created_at", startMillis, endMillis)

	var results []TestModelMySQL
	finalDB := qb.Apply(db.Model(&TestModelMySQL{}))

	// 打印生成的SQL用于调试
	sql := finalDB.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Find(&results)
	})
	t.Logf("Generated SQL: %s", sql)

	// 执行查询
	err := finalDB.Find(&results).Error
	if err != nil {
		t.Fatalf("Query failed: %v", err)
	}

	// 验证结果 - 应该包含 record2 和 record3
	expectedCount := 2
	if len(results) != expectedCount {
		t.Errorf("Expected %d results, got %d", expectedCount, len(results))
		for _, result := range results {
			t.Logf("Found record: %s, created_at: %v", result.Name, result.CreatedAt)
		}
	}

	// 验证具体的记录
	expectedNames := map[string]bool{"record2": true, "record3": true}
	foundNames := make(map[string]bool)

	for _, result := range results {
		foundNames[result.Name] = true
		if !expectedNames[result.Name] {
			t.Errorf("Unexpected record found: %s", result.Name)
		}
	}

	for expectedName := range expectedNames {
		if !foundNames[expectedName] {
			t.Errorf("Missing expected record: %s", expectedName)
		}
	}
}

func TestTimeBetweenMillisecondPrecision(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	db, cleanup := setupMySQLTestDB(t)
	defer cleanup()

	// 测试毫秒精度的查询
	baseTime := time.Unix(1672617600, 0) // 2023-01-02 00:00:00 UTC

	testData := []TestModelMySQL{
		{ID: 1, Name: "ms000", CreatedAt: baseTime.Add(0 * time.Millisecond)},
		{ID: 2, Name: "ms500", CreatedAt: baseTime.Add(500 * time.Millisecond)},
		{ID: 3, Name: "ms999", CreatedAt: baseTime.Add(999 * time.Millisecond)},
		{ID: 4, Name: "next_sec", CreatedAt: baseTime.Add(1000 * time.Millisecond)},
	}

	for _, data := range testData {
		if err := db.Create(&data).Error; err != nil {
			t.Fatalf("Failed to insert test data: %v", err)
		}
	}

	// 查询同一秒内的所有记录
	startMillis := baseTime.UnixMilli()                           // 开始时间（毫秒）
	endMillis := baseTime.Add(999 * time.Millisecond).UnixMilli() // 结束时间（不包含下一秒）

	qb := NewQueryBuilder()
	qb.TimeBetween("created_at", startMillis, endMillis)

	var results []TestModelMySQL
	finalDB := qb.Apply(db.Model(&TestModelMySQL{}))

	sql := finalDB.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Find(&results)
	})
	t.Logf("Millisecond precision SQL: %s", sql)

	err := finalDB.Find(&results).Error
	if err != nil {
		t.Fatalf("Query failed: %v", err)
	}

	// 验证结果 - 应该包含前3条记录（同一秒内）
	expectedCount := 3
	if len(results) != expectedCount {
		t.Errorf("Expected %d results for millisecond precision test, got %d", expectedCount, len(results))
		for _, result := range results {
			t.Logf("Found record: %s, created_at: %v", result.Name, result.CreatedAt)
		}
	}
}

func TestTimeBetweenWithOtherConditions(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	db, cleanup := setupMySQLTestDB(t)
	defer cleanup()

	// 插入测试数据
	testData := []TestModelMySQL{
		{ID: 1, Name: "active_old", CreatedAt: time.Unix(1672531200, 0)},
		{ID: 2, Name: "active_new", CreatedAt: time.Unix(1672617600, 0)},
		{ID: 3, Name: "inactive_new", CreatedAt: time.Unix(1672617600, 0)},
		{ID: 4, Name: "active_future", CreatedAt: time.Unix(1672790400, 0)},
	}

	for _, data := range testData {
		if err := db.Create(&data).Error; err != nil {
			t.Fatalf("Failed to insert test data: %v", err)
		}
	}

	// 复合查询：时间范围 + 名称模式匹配
	startMillis := int64(1672617600000) // 2023-01-02 00:00:00 UTC
	endMillis := int64(1672704000000)   // 2023-01-03 00:00:00 UTC

	qb := NewQueryBuilder()
	qb.TimeBetween("created_at", startMillis, endMillis).
		Like("name", "active")

	var results []TestModelMySQL
	finalDB := qb.Apply(db.Model(&TestModelMySQL{}))

	sql := finalDB.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Find(&results)
	})
	t.Logf("Complex query SQL: %s", sql)

	err := finalDB.Find(&results).Error
	if err != nil {
		t.Fatalf("Query failed: %v", err)
	}

	// 验证结果 - 应该只包含 "active_new"
	expectedCount := 1
	if len(results) != expectedCount {
		t.Errorf("Expected %d results for complex query, got %d", expectedCount, len(results))
	}

	if len(results) > 0 && results[0].Name != "active_new" {
		t.Errorf("Expected 'active_new', got '%s'", results[0].Name)
	}
}

func TestTimeBetweenPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	db, cleanup := setupMySQLTestDB(t)
	defer cleanup()

	// 插入大量测试数据
	const recordCount = 1000
	baseTime := time.Unix(1672531200, 0) // 2023-01-01 00:00:00 UTC

	var testData []TestModelMySQL
	for i := 0; i < recordCount; i++ {
		testData = append(testData, TestModelMySQL{
			ID:        int64(i + 1),
			Name:      fmt.Sprintf("record_%d", i),
			CreatedAt: baseTime.Add(time.Duration(i) * time.Minute),
		})
	}

	// 批量插入
	batchSize := 100
	for i := 0; i < len(testData); i += batchSize {
		end := i + batchSize
		if end > len(testData) {
			end = len(testData)
		}
		if err := db.Create(testData[i:end]).Error; err != nil {
			t.Fatalf("Failed to batch insert test data: %v", err)
		}
	}

	// 测试查询性能
	startMillis := baseTime.Add(100 * time.Minute).UnixMilli()
	endMillis := baseTime.Add(200 * time.Minute).UnixMilli()

	qb := NewQueryBuilder()
	qb.TimeBetween("created_at", startMillis, endMillis)

	start := time.Now()
	var results []TestModelMySQL
	err := qb.Apply(db.Model(&TestModelMySQL{})).Find(&results).Error
	duration := time.Since(start)

	if err != nil {
		t.Fatalf("Performance test query failed: %v", err)
	}

	t.Logf("Query completed in %v, found %d records", duration, len(results))

	// 验证结果数量是否合理（应该找到约100条记录）
	expectedRange := []int{90, 110} // 允许一些误差
	if len(results) < expectedRange[0] || len(results) > expectedRange[1] {
		t.Errorf("Expected around 100 results, got %d", len(results))
	}
}

// BenchmarkTimeBetween 性能基准测试
func BenchmarkTimeBetween(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping benchmark in short mode")
	}

	db, cleanup := setupMySQLTestDB(b.(*testing.T))
	defer cleanup()

	// 插入基础测试数据
	baseTime := time.Unix(1672531200, 0)
	for i := 0; i < 100; i++ {
		data := TestModelMySQL{
			ID:        int64(i + 1),
			Name:      fmt.Sprintf("bench_record_%d", i),
			CreatedAt: baseTime.Add(time.Duration(i) * time.Minute),
		}
		if err := db.Create(&data).Error; err != nil {
			b.Fatalf("Failed to insert benchmark data: %v", err)
		}
	}

	startMillis := baseTime.Add(10 * time.Minute).UnixMilli()
	endMillis := baseTime.Add(90 * time.Minute).UnixMilli()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			qb := NewQueryBuilder()
			qb.TimeBetween("created_at", startMillis, endMillis)

			var results []TestModelMySQL
			err := qb.Apply(db.Model(&TestModelMySQL{})).Find(&results).Error
			if err != nil {
				b.Fatalf("Benchmark query failed: %v", err)
			}
		}
	})
}
