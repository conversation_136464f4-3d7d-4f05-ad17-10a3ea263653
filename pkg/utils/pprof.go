package utils

import (
	"fmt"
	"net/http"
	"runtime/debug"

	"uofferv2/pkg/logger"
)

func StartPprof(port int) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("StartPprof panic: %v\n%s", r, debug.Stack())
			}
		}()

		// 只监听内网地址
		internalAddr := fmt.Sprintf(":%d", port)
		logger.Infof("start pprof addr: %s", internalAddr)
		if err := http.ListenAndServe(internalAddr, nil); err != nil {
			logger.Errorf("start pprof failed err: %s", err.Error())
		}
	}()
}
