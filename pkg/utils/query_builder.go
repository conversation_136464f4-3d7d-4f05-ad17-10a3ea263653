package utils

import (
	"fmt"
	"reflect"

	"gorm.io/gorm"
)

// QueryConditionType 定义查询条件的类型
type QueryConditionType string

const (
	// ConditionTypeSimple 代表简单的条件，如 =, IN, LIKE, BETWEEN
	ConditionTypeSimple QueryConditionType = "SIMPLE"
	// ConditionTypeOr 代表一组用 OR 连接的条件
	ConditionTypeOr QueryConditionType = "OR"
	// ConditionTypeJoin 代表 JOIN 条件
	ConditionTypeJoin QueryConditionType = "JOIN"
)

// QueryCondition 查询条件结构
type QueryCondition struct {
	Type     QueryConditionType // 条件的类型
	Field    string             // 字段名 (用于 ConditionTypeSimple)
	Operator string             // 操作符：=, IN, LIKE, BETWEEN等 (用于 ConditionTypeSimple)
	Value    interface{}        // 值 (用于 ConditionTypeSimple)

	OrConditions []*QueryBuilder // OR 条件组中的子查询构建器 (用于 ConditionTypeOr)

	// JOIN 相关字段 (用于 ConditionTypeJoin)
	JoinType      string // JOIN 类型：LEFT JOIN, RIGHT JOIN, INNER JOIN 等
	JoinTable     string // 要 JOIN 的表名
	JoinCondition string // JOIN 的条件
}

// QueryBuilder 查询构建器
type QueryBuilder struct {
	conditions []QueryCondition
}

// NewQueryBuilder 创建新的查询构建器
func NewQueryBuilder() *QueryBuilder {
	return &QueryBuilder{
		conditions: make([]QueryCondition, 0),
	}
}

// addSimpleCondition 是一个辅助函数，用于添加简单的查询条件
func (qb *QueryBuilder) addSimpleCondition(field string, operator string, value interface{}) *QueryBuilder {
	qb.conditions = append(qb.conditions, QueryCondition{
		Type:     ConditionTypeSimple,
		Field:    field,
		Operator: operator,
		Value:    value,
	})
	return qb
}

/**
 * 添加等值查询条件
 * @param field 字段名
 * @param value 值
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) Equal(field string, value interface{}) *QueryBuilder {
	if !isZeroValue(value) {
		qb.addSimpleCondition(field, "=", value)
	}
	return qb
}

/**
 * 添加IN查询条件
 * @param field 字段名
 * @param values 值列表
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) In(field string, values interface{}) *QueryBuilder {
	if !IsEmptySlice(values) {
		qb.addSimpleCondition(field, "IN", values)
	}
	return qb
}

/**
 * 添加LIKE查询条件
 * @param field 字段名
 * @param value 值
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) Like(field string, value interface{}) *QueryBuilder {
	if !isZeroValue(value) {
		qb.addSimpleCondition(field, "LIKE", fmt.Sprintf("%%%v%%", value))
	}
	return qb
}

/**
 * 添加BETWEEN查询条件
 * @param field 字段名
 * @param start 开始值
 * @param end 结束值
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) Between(field string, start, end interface{}) *QueryBuilder {
	if !isZeroValue(start) && !isZeroValue(end) {
		qb.addSimpleCondition(field, "BETWEEN", []interface{}{start, end})
	}
	return qb
}

/**
 * 添加时间范围查询条件（使用FROM_UNIXTIME）
 * @param field 字段名
 * @param start 开始时间戳
 * @param end 结束时间戳
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) TimeBetween(field string, start, end int64) *QueryBuilder {
	if start != 0 && end != 0 {
		qb.addSimpleCondition(field, "TIME_BETWEEN", []interface{}{start, end})
	}
	return qb
}

/**
 * 添加一组用 OR 连接的条件。
 * 每个参数都是一个函数，该函数接收一个新的 QueryBuilder 实例并对其进行配置。
 * 这些配置好的 QueryBuilder 实例将构成 OR 条件组。
 * 示例:
 * qb.Or(
 *     func(orQb *QueryBuilder) { orQb.Equal("status", 1) },
 *     func(orQb *QueryBuilder) { orQb.In("type", []int{2,3}) },
 * )
 * 这将生成类似 SQL: ... AND (status = 1 OR type IN (2,3))
 * @param conditionBuilders 一个或多个配置 OR 子条件的函数
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) Or(conditionBuilders ...func(orQb *QueryBuilder)) *QueryBuilder {
	if len(conditionBuilders) == 0 {
		return qb
	}

	var orQueryBuilders []*QueryBuilder
	for _, buildFunc := range conditionBuilders {
		orQb := NewQueryBuilder()
		buildFunc(orQb)
		// 只有当子 QueryBuilder 确实包含条件时才添加
		if len(orQb.conditions) > 0 {
			orQueryBuilders = append(orQueryBuilders, orQb)
		}
	}

	if len(orQueryBuilders) > 0 {
		qb.conditions = append(qb.conditions, QueryCondition{
			Type:         ConditionTypeOr,
			OrConditions: orQueryBuilders,
		})
	}
	return qb
}

/**
 * 添加 JOIN 条件
 * @param joinType JOIN 类型 (LEFT JOIN, RIGHT JOIN, INNER JOIN 等)
 * @param table 要 JOIN 的表名
 * @param condition JOIN 的条件
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) Join(joinType, table, condition string) *QueryBuilder {
	if table != "" && condition != "" {
		qb.conditions = append(qb.conditions, QueryCondition{
			Type:          ConditionTypeJoin,
			JoinType:      joinType,
			JoinTable:     table,
			JoinCondition: condition,
		})
	}
	return qb
}

/**
 * 添加 LEFT JOIN 条件
 * @param table 要 JOIN 的表名
 * @param condition JOIN 的条件
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) LeftJoin(table, condition string) *QueryBuilder {
	return qb.Join("LEFT JOIN", table, condition)
}

/**
 * 添加 RIGHT JOIN 条件
 * @param table 要 JOIN 的表名
 * @param condition JOIN 的条件
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) RightJoin(table, condition string) *QueryBuilder {
	return qb.Join("RIGHT JOIN", table, condition)
}

/**
 * 添加 INNER JOIN 条件
 * @param table 要 JOIN 的表名
 * @param condition JOIN 的条件
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) InnerJoin(table, condition string) *QueryBuilder {
	return qb.Join("INNER JOIN", table, condition)
}

/**
 * 应用查询条件到GORM查询
 * @param db GORM数据库实例
 * @return 应用条件后的GORM实例
 */
func (qb *QueryBuilder) Apply(db *gorm.DB) *gorm.DB {
	for _, condition := range qb.conditions {
		switch condition.Type {
		case ConditionTypeJoin:
			db = db.Joins(fmt.Sprintf("%s %s ON %s", condition.JoinType, condition.JoinTable, condition.JoinCondition))
		case ConditionTypeSimple:
			switch condition.Operator {
			case "=":
				db = db.Where(fmt.Sprintf("%s = ?", condition.Field), condition.Value)
			case "IN":
				db = db.Where(fmt.Sprintf("%s IN (?)", condition.Field), condition.Value)
			case "LIKE":
				db = db.Where(fmt.Sprintf("%s LIKE ?", condition.Field), condition.Value)
			case "BETWEEN":
				values := condition.Value.([]interface{})
				db = db.Where(fmt.Sprintf("%s BETWEEN ? AND ?", condition.Field), values[0], values[1])
			case "TIME_BETWEEN":
				values := condition.Value.([]interface{})
				// 将毫秒时间戳转换为秒时间戳
				startSeconds := values[0].(int64) / 1000
				endSeconds := values[1].(int64) / 1000
				db = db.Where(fmt.Sprintf("%s BETWEEN FROM_UNIXTIME(?) AND FROM_UNIXTIME(?)", condition.Field), startSeconds, endSeconds)
			}
		case ConditionTypeOr:
			if len(condition.OrConditions) > 0 {
				// 为 OR 条件组创建一个新的 GORM 会话，以隔离其构建过程
				// 最终的 OR 条件组将作为一个单独的 .Where() 条件添加到主查询 db 上
				orGroupQuery := db.Session(&gorm.Session{NewDB: true, SkipDefaultTransaction: true})

				for i, orBuilder := range condition.OrConditions {
					// orBuilder 代表 OR 组中的一个条件块 (其内部条件是 AND 连接的)
					// 例如: ( (condA1 AND condA2) OR (condB1) OR (condC1 AND condC2) )
					// 我们需要为 (condA1 AND condA2) 创建一个查询部分

					// 为当前 AND 条件块创建一个临时的 DB 会话
					currentAndBlockDb := db.Session(&gorm.Session{NewDB: true, SkipDefaultTransaction: true})
					currentAndBlockDb = orBuilder.Apply(currentAndBlockDb) // 应用此块内的 AND 条件

					if i == 0 {
						// 对于 OR 组中的第一个条件块，直接使用 Where 应用
						// 如果 currentAndBlockDb 包含 "WHERE a=1 AND b=2",
						// orGroupQuery.Where(currentAndBlockDb) 会使 orGroupQuery 变为 "WHERE (a=1 AND b=2)"
						orGroupQuery = orGroupQuery.Where(currentAndBlockDb)
					} else {
						// 对于后续的条件块，使用 Or 连接
						// orGroupQuery.Or(currentAndBlockDb) 会使 orGroupQuery 变为 "WHERE (previous_or_conditions) OR (a=1 AND b=2)"
						orGroupQuery = orGroupQuery.Or(currentAndBlockDb)
					}
				}
				// 现在 orGroupQuery 包含了完整的 OR 条件组，例如 "WHERE ((condA) OR (condB))"
				// 将这个 OR 条件组作为一个整体 AND 条件添加到主查询 db
				db = db.Where(orGroupQuery)
			}
		}
	}
	return db
}

// IsZeroValue 检查值是否为零值
func isZeroValue(value interface{}) bool {
	if value == nil {
		return true
	}

	// 先尝试获取 interface{} 包装的具体类型值
	if v, ok := value.(int32); ok {
		return v == 0
	}
	if v, ok := value.(int64); ok {
		return v == 0
	}
	if v, ok := value.(int); ok {
		return v == 0
	}
	if v, ok := value.(uint); ok {
		return v == 0
	}
	if v, ok := value.(uint32); ok {
		return v == 0
	}
	if v, ok := value.(float32); ok {
		return v == 0
	}
	if v, ok := value.(float64); ok {
		return v == 0
	}

	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.String:
		return v.String() == ""
	case reflect.Slice:
		return v.Len() == 0 // 对于空切片，isZeroValue 也认为是零值
	case reflect.Ptr:
		return v.IsNil()
	case reflect.Struct:
		// 遍历结构体的所有字段
		for i := 0; i < v.NumField(); i++ {
			if !isZeroValue(v.Field(i).Interface()) {
				return false
			}
		}
		return true
	case reflect.Interface:
		if v.IsNil() {
			return true
		}
		return isZeroValue(v.Elem().Interface())
	default:
		// 对于其他类型，如果它们是其类型的零值，则返回 true
		// 注意：这对于某些自定义类型可能不完全准确，但对于基本类型和标准库类型通常有效
		return reflect.DeepEqual(value, reflect.Zero(reflect.TypeOf(value)).Interface())
	}
}

// IsEmptySlice 检查切片是否为空 (建议使用反射以支持更多类型)
func IsEmptySlice(value interface{}) bool {
	if value == nil {
		return true // nil 切片被视为空
	}
	val := reflect.ValueOf(value)
	if val.Kind() == reflect.Slice {
		return val.Len() == 0
	}
	// 如果不是切片类型，则不符合“空切片”的定义
	return false
}

// GetConditionsForTest 返回查询条件列表，仅用于测试
func (qb *QueryBuilder) GetConditionsForTest() []QueryCondition {
	return qb.conditions
}

// IsZero 使用反射检查任意类型的值是否为零值
func IsZero(v interface{}) bool {
	// 如果是 nil，直接返回 true
	if v == nil {
		return true
	}

	// 如果是空接口，检查其底层值
	if vi, ok := v.(interface{}); ok && vi == nil {
		return true
	}

	// 对于其他情况，可以考虑使用 reflect.DeepEqual 或特定类型的检查
	// 这里的实现比较简单，可能需要根据具体需求完善
	// 例如，可以像 isZeroValue 那样使用 reflect.ValueOf(v).IsZero() (但要注意 IsZero 的适用范围)
	// 或者，如果这个函数主要用于指针或接口，则当前实现可能足够
	// 为了安全起见，如果 v 不是指针或接口，可以默认它不是零值，除非有更具体的检查
	val := reflect.ValueOf(v)
	switch val.Kind() {
	case reflect.Ptr, reflect.Interface, reflect.Map, reflect.Slice, reflect.Chan, reflect.Func:
		return val.IsNil()
	}

	// 对于非引用类型，可以尝试与类型的零值比较
	// 但这可能不适用于所有情况，特别是自定义结构体
	// isZeroValue 中的处理更为全面
	// 如果 IsZero 的目的是一个快速的 nil 或空接口检查，那么当前实现可能已经满足
	// 如果需要更通用的零值检查，应参考 isZeroValue
	return false // 默认非以上可判断为 nil 的类型不是零值
}
