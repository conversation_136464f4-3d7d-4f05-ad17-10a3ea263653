package utils

import (
	"flag"
	"os"
	"path/filepath"
)

/**
 * GetConfigPath 自适应获取配置文件路径
 * 优先级：环境变量 > 命令行参数 > 自动检测 > 默认值
 * @param defaultPath 默认配置文件路径
 * @param envKey 环境变量键名（可选）
 * @return 配置文件路径
 */
func GetConfigPath(defaultPath string, envKey ...string) string {
	// 1. 检查环境变量
	if len(envKey) > 0 && envKey[0] != "" {
		if envPath := os.Getenv(envKey[0]); envPath != "" {
			return envPath
		}
	}

	// 2. 检查命令行参数
	if configFromFlag := getConfigFromFlag(); configFromFlag != "" {
		return configFromFlag
	}

	// 3. 自动检测项目根目录
	if detectedPath := detectConfigPath(defaultPath); detectedPath != "" {
		return detectedPath
	}

	// 4. 返回默认路径
	return defaultPath
}

/**
 * getConfigFromFlag 从命令行参数获取配置路径
 * 支持 -config 和 -c 参数
 */
func getConfigFromFlag() string {
	var configPath string

	// 检查并定义命令行参数（如果还没有定义的话）
	if flag.Lookup("config") == nil {
		flag.StringVar(&configPath, "config", "", "配置文件路径")
	}
	if flag.Lookup("c") == nil {
		flag.StringVar(&configPath, "c", "", "配置文件路径（简写）")
	}

	// 解析命令行参数（如果还没有解析的话）
	if !flag.Parsed() {
		flag.Parse()
	}

	return configPath
}

/**
 * detectConfigPath 自动检测配置文件路径
 * 从当前目录开始向上查找，直到找到 config 目录或到达根目录
 */
func detectConfigPath(configFileName string) string {
	// 获取当前工作目录
	currentDir, err := os.Getwd()
	if err != nil {
		return ""
	}

	// 向上查找配置文件
	for {
		// 构造可能的配置文件路径
		configPath := filepath.Join(currentDir, configFileName)

		// 检查文件是否存在
		if _, err := os.Stat(configPath); err == nil {
			return configPath
		}

		// 获得父目录
		parentDir := filepath.Dir(currentDir)

		// 到达根目录，停止查找
		if parentDir == currentDir {
			break
		}

		currentDir = parentDir
	}

	return ""
}

/**
 * GetProjectRoot 获取项目根目录
 * 通过查找 go.mod 文件来确定项目根目录
 */
func GetProjectRoot() string {
	currentDir, err := os.Getwd()
	if err != nil {
		return ""
	}

	for {
		// 检查是否存在 go.mod 文件
		goModPath := filepath.Join(currentDir, "go.mod")
		if _, err := os.Stat(goModPath); err == nil {
			return currentDir
		}

		// 获得父目录
		parentDir := filepath.Dir(currentDir)

		// 到达根目录，停止查找
		if parentDir == currentDir {
			break
		}

		currentDir = parentDir
	}

	return ""
}

/**
 * GetConfigPathWithProjectRoot 基于项目根目录获取配置路径
 * 推荐用于测试环境
 */
func GetConfigPathWithProjectRoot(relativePath string) string {
	projectRoot := GetProjectRoot()
	if projectRoot == "" {
		return relativePath
	}
	return filepath.Join(projectRoot, relativePath)
}
