package utils

import (
	"os"
	"path/filepath"
	"testing"
)

/**
 * TestGetConfigPath 测试配置路径获取功能
 */
func TestGetConfigPath(t *testing.T) {
	// 测试用例
	tests := []struct {
		name        string
		defaultPath string
		envKey      string
		envValue    string
		expected    string
		setup       func()
		cleanup     func()
	}{
		{
			name:        "使用环境变量路径",
			defaultPath: "config/default.yaml",
			envKey:      "TEST_CONFIG_PATH",
			envValue:    "/custom/config.yaml",
			expected:    "/custom/config.yaml",
			setup: func() {
				os.Setenv("TEST_CONFIG_PATH", "/custom/config.yaml")
			},
			cleanup: func() {
				os.Unsetenv("TEST_CONFIG_PATH")
			},
		},
		{
			name:        "环境变量为空，使用默认路径",
			defaultPath: "config/default.yaml",
			envKey:      "EMPTY_CONFIG_PATH",
			envValue:    "",
			expected:    "config/default.yaml",
			setup:       func() {},
			cleanup:     func() {},
		},
		{
			name:        "无环境变量键，使用默认路径",
			defaultPath: "config/default.yaml",
			envKey:      "",
			envValue:    "",
			expected:    "config/default.yaml",
			setup:       func() {},
			cleanup:     func() {},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置
			tt.setup()
			defer tt.cleanup()

			// 执行
			var result string
			if tt.envKey != "" {
				result = GetConfigPath(tt.defaultPath, tt.envKey)
			} else {
				result = GetConfigPath(tt.defaultPath)
			}

			// 验证
			if result != tt.expected {
				t.Errorf("GetConfigPath() = %v, 期望 %v", result, tt.expected)
			}
		})
	}
}

/**
 * TestGetProjectRoot 测试项目根目录检测
 */
func TestGetProjectRoot(t *testing.T) {
	// 获取当前项目根目录
	projectRoot := GetProjectRoot()

	if projectRoot == "" {
		t.Error("GetProjectRoot() 返回空路径")
		return
	}

	// 验证项目根目录是否包含 go.mod 文件
	goModPath := filepath.Join(projectRoot, "go.mod")
	if _, err := os.Stat(goModPath); os.IsNotExist(err) {
		t.Errorf("项目根目录 %s 不包含 go.mod 文件", projectRoot)
	}

	t.Logf("检测到的项目根目录: %s", projectRoot)
}

/**
 * TestGetConfigPathWithProjectRoot 测试基于项目根目录的配置路径
 */
func TestGetConfigPathWithProjectRoot(t *testing.T) {
	// 测试相对路径
	relativePath := "config/nacos_config.yaml"
	fullPath := GetConfigPathWithProjectRoot(relativePath)

	// 验证路径不为空
	if fullPath == "" {
		t.Error("GetConfigPathWithProjectRoot() 返回空路径")
		return
	}

	// 验证是绝对路径
	if !filepath.IsAbs(fullPath) {
		// 如果项目根目录未找到，应该返回相对路径
		if fullPath != relativePath {
			t.Errorf("期望返回相对路径 %s，实际返回 %s", relativePath, fullPath)
		}
	} else {
		// 验证路径包含项目根目录
		projectRoot := GetProjectRoot()
		if projectRoot != "" {
			expectedPath := filepath.Join(projectRoot, relativePath)
			if fullPath != expectedPath {
				t.Errorf("期望路径 %s，实际路径 %s", expectedPath, fullPath)
			}
		}
	}

	t.Logf("配置文件完整路径: %s", fullPath)
}

/**
 * TestDetectConfigPath 测试配置文件检测
 */
func TestDetectConfigPath(t *testing.T) {
	// 测试检测已存在的文件
	projectRoot := GetProjectRoot()
	if projectRoot == "" {
		t.Skip("跳过测试：未找到项目根目录")
		return
	}

	// 测试检测 go.mod 文件（肯定存在）
	goModPath := detectConfigPath("go.mod")
	if goModPath == "" {
		t.Error("detectConfigPath('go.mod') 应该找到 go.mod 文件")
	} else {
		t.Logf("检测到 go.mod 路径: %s", goModPath)
	}

	// 测试检测不存在的文件
	nonExistentPath := detectConfigPath("non_existent_file.yaml")
	if nonExistentPath != "" {
		t.Errorf("detectConfigPath('non_existent_file.yaml') 应该返回空字符串，实际返回: %s", nonExistentPath)
	}
}
