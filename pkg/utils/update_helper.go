package utils

import (
	"reflect"
	"strings"
)

/**
 * BuildUpdateFields 构建更新字段映射，只包含非零值字段
 * @param req 请求结构体
 * @param fieldMapping 字段名映射关系 (结构体字段名 -> 数据库字段名)
 * @return 更新字段映射
 */
func BuildUpdateFields(req interface{}, fieldMapping map[string]string) map[string]interface{} {
	updateFields := make(map[string]interface{})

	v := reflect.ValueOf(req)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)
		fieldName := fieldType.Name

		// 跳过不可导出的字段
		if !field.CanInterface() {
			continue
		}

		// 检查是否为零值
		if !field.IsZero() {
			// 获取数据库字段名
			dbFieldName := getDBFieldName(fieldName, fieldMapping)
			if dbFieldName != "" {
				updateFields[dbFieldName] = field.Interface()
			}
		}
	}

	return updateFields
}

/**
 * getDBFieldName 获取数据库字段名
 * @param structFieldName 结构体字段名
 * @param fieldMapping 字段映射关系
 * @return 数据库字段名
 */
func getDBFieldName(structFieldName string, fieldMapping map[string]string) string {
	// 如果有自定义映射，使用映射
	if dbName, exists := fieldMapping[structFieldName]; exists {
		return dbName
	}

	// 默认转换：驼峰转下划线
	return camelToSnake(structFieldName)
}

/**
 * camelToSnake 驼峰命名转下划线命名
 * @param s 驼峰命名字符串
 * @return 下划线命名字符串
 */
func camelToSnake(s string) string {
	var result strings.Builder
	for i, r := range s {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

/**
 * BuildUpdateFieldsWithExcludes 构建更新字段映射，排除指定字段
 * @param req 请求结构体
 * @param fieldMapping 字段名映射关系
 * @param excludeFields 要排除的字段名列表
 * @return 更新字段映射
 */
func BuildUpdateFieldsWithExcludes(req interface{}, fieldMapping map[string]string, excludeFields []string) map[string]interface{} {
	excludeSet := make(map[string]bool)
	for _, field := range excludeFields {
		excludeSet[field] = true
	}

	updateFields := make(map[string]interface{})

	v := reflect.ValueOf(req)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)
		fieldName := fieldType.Name

		// 跳过排除的字段
		if excludeSet[fieldName] {
			continue
		}

		// 跳过不可导出的字段
		if !field.CanInterface() {
			continue
		}

		// 检查是否为零值
		if !field.IsZero() {
			// 获取数据库字段名
			dbFieldName := getDBFieldName(fieldName, fieldMapping)
			if dbFieldName != "" {
				updateFields[dbFieldName] = field.Interface()
			}
		}
	}

	return updateFields
}

/**
 * BuildUpdateFieldsByTag 基于结构体标签构建更新字段映射
 * @param req 请求结构体（需要有 db 标签）
 * @return 更新字段映射
 */
func BuildUpdateFieldsByTag(req interface{}) map[string]interface{} {
	updateFields := make(map[string]interface{})

	v := reflect.ValueOf(req)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)

		// 跳过不可导出的字段
		if !field.CanInterface() {
			continue
		}

		// 获取 db 标签
		dbTag := fieldType.Tag.Get("db")
		if dbTag == "" || dbTag == "-" {
			continue
		}

		// 检查是否为零值
		if !field.IsZero() {
			updateFields[dbTag] = field.Interface()
		}
	}

	return updateFields
}

/**
 * BuildUpdateFieldsByTagWithExcludes 基于结构体标签构建更新字段映射，排除指定字段
 * @param req 请求结构体（需要有 db 标签）
 * @param excludeTags 要排除的数据库字段名列表
 * @return 更新字段映射
 */
func BuildUpdateFieldsByTagWithExcludes(req interface{}, excludeTags []string) map[string]interface{} {
	excludeSet := make(map[string]bool)
	for _, tag := range excludeTags {
		excludeSet[tag] = true
	}

	updateFields := make(map[string]interface{})

	v := reflect.ValueOf(req)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)

		// 跳过不可导出的字段
		if !field.CanInterface() {
			continue
		}

		// 获取 db 标签
		dbTag := fieldType.Tag.Get("db")
		if dbTag == "" || dbTag == "-" {
			continue
		}

		// 跳过排除的字段
		if excludeSet[dbTag] {
			continue
		}

		// 检查是否为零值
		if !field.IsZero() {
			updateFields[dbTag] = field.Interface()
		}
	}

	return updateFields
}
