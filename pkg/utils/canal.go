package utils

import (
	"context"
	"net"
	"runtime/debug"
	"time"

	"uofferv2/pkg/logger"
)

func CheckCanalAlive(addr string) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("%s panic: %v\n%s", "CheckCanalAlive", r, debug.Stack())
			}
		}()

		logger.Infof("[start canal heartbeat]")
		if addr == "" {
			logger.Errorf("canal_addr is empty")
			return
		}

		timer := time.NewTicker(time.Minute)
		defer timer.Stop()
		for range timer.C {
			checkCanalAlive(addr)
		}
	}()
}

// 检查Canal服务是否可达
func checkCanalAlive(addr string) {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	// 尝试建立TCP连接
	var d net.Dialer
	conn, err := d.Dial<PERSON>ontext(ctx, "tcp", addr)
	if err != nil {
		logger.CtxErrorf(ctx, "[canal heartbeat failed] error: %v", err)
		return
	}
	// 随便发送一个请求
	conn.SetWriteDeadline(time.Now().Add(time.Second * 5))
	_, err = conn.Write([]byte("ping"))
	if err != nil {
		logger.CtxErrorf(ctx, "[canal heartbeat failed] error: %v", err)
		return
	}
	// 测试能不能读到数据，很玄学的一个方法，别的端口都不行，但是11110可以
	conn.SetReadDeadline(time.Now().Add(time.Second * 5))
	buf := make([]byte, 1024)
	_, err = conn.Read(buf)
	if err != nil {
		logger.CtxErrorf(ctx, "[canal heartbeat failed] error: %v", err)
		return
	}
	//logger.CtxDebugf(ctx, "[canal heartbeat success]")
	// 关闭连接
	defer func() {
		if err := conn.Close(); err != nil {
			logger.CtxErrorf(ctx, "[canal heartbeat failed] error: %v", err)
		}
	}()
	logger.Infof("[canal heartbeat success]")
}
