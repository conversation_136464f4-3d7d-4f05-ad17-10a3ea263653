package utils

import (
	"testing"
	"time"
)

// parseTime 是一个辅助函数，用于解析日期字符串，简化测试代码
func parseTime(dateStr string) time.Time {
	t, err := time.Parse(layout, dateStr)
	if err != nil {
		panic(err)
	}
	return t
}

func TestIsWorkday_WithHolidays_2025(t *testing.T) {
	testCases := []struct {
		name     string
		date     time.Time
		expected bool
	}{
		// 法定节假日
		{"元旦", parseTime("2025-01-01"), false},
		{"春节", parseTime("2025-01-28"), false},
		{"劳动节", parseTime("2025-05-01"), false},
		{"国庆节", parseTime("2025-10-01"), false},

		// 调休上班
		{"春节调休", parseTime("2025-01-26"), true},  // 周日
		{"劳动节调休", parseTime("2025-04-27"), true}, // 周日
		{"国庆节调休", parseTime("2025-09-28"), true}, // 周日

		// 普通工作日和周末
		{"普通工作日", parseTime("2025-07-28"), true}, // 周一
		{"普通周末", parseTime("2025-07-27"), false}, // 周日
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if got := IsWorkday(tc.date); got != tc.expected {
				t.Errorf("IsWorkday(%v) = %v; want %v", tc.date.Format(layout), got, tc.expected)
			}
		})
	}
}

func TestAddWorkdays_WithHolidays_2025(t *testing.T) {
	testCases := []struct {
		name     string
		start    time.Time
		days     int
		expected time.Time
	}{
		{
			name:     "从劳动节前开始，跨过劳动节假期",
			start:    parseTime("2025-04-30"), // 周三
			days:     3,
			expected: parseTime("2025-05-08"), // 劳动节后第3个工作日：5.6, 5.7, 5.8
		},
		{
			name:     "从春节前开始，跨过春节假期",
			start:    parseTime("2025-01-27"), // 周一
			days:     2,
			expected: parseTime("2025-02-06"), // 节后第一个工作日是2.5, 第二个是2.6
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			got := AddWorkdays(tc.start, tc.days)
			if !got.Equal(tc.expected) {
				t.Errorf("AddWorkdays(%v, %d) = %v; want %v", tc.start.Format(layout), tc.days, got.Format(layout), tc.expected.Format(layout))
			}
		})
	}
}

func TestSubWorkdays_WithHolidays_2025(t *testing.T) {
	testCases := []struct {
		name     string
		start    time.Time
		days     int
		expected time.Time
	}{
		{
			name:     "从劳动节后开始，跨过劳动节假期",
			start:    parseTime("2025-05-06"), // 周二
			days:     3,
			expected: parseTime("2025-04-28"), // 节前工作日: 4.30, 4.29, 4.28
		},
		{
			name:     "从国庆节后开始，跨过国庆节假期",
			start:    parseTime("2025-10-09"), // 周四
			days:     2,
			expected: parseTime("2025-09-29"), // 节前工作日: 9.30, 9.29
		},
		{
			name:     "从一个非工作日开始减去工作日",
			start:    parseTime("2025-01-01"), // 元旦
			days:     1,
			expected: parseTime("2024-12-30"), // 往前找到的第一个工作日是2024.12.31,再减一天是2024.12.30
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			got := SubWorkdays(tc.start, tc.days)
			if !got.Equal(tc.expected) {
				t.Errorf("SubWorkdays(%v, %d) = %v; want %v", tc.start.Format(layout), tc.days, got.Format(layout), tc.expected.Format(layout))
			}
		})
	}
}

// TestWorkdayLogicForFutureYears_NoHolidayData 验证在没有提供节假日数据的情况下，
// 工作日计算是否能回退到标准的周一至周五的逻辑。
func TestWorkdayLogicForFutureYears_NoHolidayData(t *testing.T) {
	t.Run("IsWorkday for 2026", func(t *testing.T) {
		// 2026-01-01 是周四，应该是工作日（因为没有节假日数据）
		if !IsWorkday(parseTime("2026-01-01")) {
			t.Error("2026-01-01 should be a workday as no holiday data is provided")
		}
		// 2026-01-03 是周六，应该是休息日
		if IsWorkday(parseTime("2026-01-03")) {
			t.Error("2026-01-03 should be a weekend")
		}
	})

	t.Run("AddWorkdays for 2026", func(t *testing.T) {
		start := parseTime("2026-01-01")    // 周四
		expected := parseTime("2026-01-05") // 周四 + 2个工作日 = 下周一
		got := AddWorkdays(start, 2)
		if !got.Equal(expected) {
			t.Errorf("AddWorkdays for 2026 failed: got %v, want %v", got.Format(layout), expected.Format(layout))
		}
	})

	t.Run("SubWorkdays for 2028 (undefined year)", func(t *testing.T) {
		start := parseTime("2028-01-03")    // 周一
		expected := parseTime("2027-12-31") // 周一 - 1个工作日 = 上周五
		got := SubWorkdays(start, 1)
		if !got.Equal(expected) {
			t.Errorf("SubWorkdays for 2028 failed: got %v, want %v", got.Format(layout), expected.Format(layout))
		}
	})
}
