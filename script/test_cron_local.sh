#!/bin/bash

# 销售数据统计RPC服务集成测试脚本
# 用于在本地开发环境手动测试销售数据统计功能

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在项目根目录
check_project_root() {
    if [[ ! -f "go.mod" ]] || [[ ! -d "server/cmd/admin_api" ]]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
}

# 设置环境变量
setup_env() {
    export INTEGRATION_TEST=true
    export HOST_ENV=local
    export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore

    print_info "环境变量设置完成:"
    print_info "  INTEGRATION_TEST=$INTEGRATION_TEST"
    print_info "  HOST_ENV=$HOST_ENV"
    print_info "  GOLANG_PROTOBUF_REGISTRATION_CONFLICT=$GOLANG_PROTOBUF_REGISTRATION_CONFLICT"
}

# 运行集成测试
run_test() {
    print_info "运行销售数据统计集成测试..."
    go test ./server/cmd/order/internal/services/ -run TestOrderStaticSalesDataIntegration -v
    if [[ $? -eq 0 ]]; then
        print_success "集成测试通过"
    else
        print_error "集成测试失败"
        exit 1
    fi
}

# 运行所有集成测试
run_all_tests() {
    print_info "运行所有销售数据统计集成测试..."
    go test ./server/cmd/order/internal/services/ -run "TestOrderStaticSalesData.*" -v
    if [[ $? -eq 0 ]]; then
        print_success "所有集成测试通过"
    else
        print_error "集成测试失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "销售数据统计RPC服务集成测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  (无参数)  运行集成测试 (默认)"
    echo "  all       运行所有集成测试"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0            # 运行集成测试"
    echo "  $0 all        # 运行所有测试"
    echo ""
    echo "注意:"
    echo "  - 确保本地数据库和Nacos服务正在运行"
    echo "  - 测试直接调用order服务的RPC方法，无需启动HTTP服务"
    echo "  - 测试可能会修改数据库数据，请在测试环境运行"
}

# 主函数
main() {
    print_info "销售数据统计RPC服务集成测试启动"
    print_info "============================================"

    # 检查项目根目录
    check_project_root

    # 设置环境变量
    setup_env

    echo ""

    # 根据参数执行相应操作
    case "${1:-}" in
        ""|"test")
            run_test
            ;;
        "all")
            run_all_tests
            ;;
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac

    echo ""
    print_success "测试执行完成!"
}

# 执行主函数
main "$@"
