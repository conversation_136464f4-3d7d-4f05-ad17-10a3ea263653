package cross_models

import (
	"context"
	"uofferv2/server/cmd/user/internal/global"

	"uofferv2/pkg/dao"
)

var SmsNameMap = map[string]string{
	"录多":   "",
	"北京菁程": "_bjjc",
}

// 获取短信配置
func GetSmsConfig(ctx context.Context) (map[string]string, error) {
	q := dao.Use(global.DB)
	list, err := q.WithContext(ctx).SmsConfig.Find()
	if err != nil {
		return nil, err
	}
	var smsConfigMap = make(map[string]string, 0)
	for _, config := range list {
		smsConfigMap[config.ConfigKey] = config.ConfigValue
	}
	return smsConfigMap, nil
}
