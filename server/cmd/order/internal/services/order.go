package services

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"time"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/i18n"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/order/internal/global"
	"uofferv2/server/cmd/order/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// OrderService implements the last service interface defined in the IDL.
type OrderService struct{}

// GetOrderList implements the ServiceImpl interface.
func (o *OrderService) GetOrderList(ctx context.Context, req *order.GetOrderListReq) (resp *order.GetOrderListRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetOrderListRsp{
		Items: make([]*order.OrderListItem, 0),
		Total: 0,
		Base:  coderror.MakeSuccessBaseRsp(),
	}

	// 订单列表
	query := GetOrderListReq(req)
	orderListItems, total, err := (&models.Order{}).GetOrderList(ctx, query)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxWarnf(ctx, "%s GetOrderList err: %v, query: %v", funcName, err, query)
		return resp, nil
	}
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderList db err: %v, query: %v", funcName, err, query)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}
	resp.Total = total

	lens := len(orderListItems)
	if lens == 0 {
		return
	}

	orderIDs := make([]int64, 0, lens)
	for _, item := range orderListItems {
		orderIDs = append(orderIDs, item.Order.ID)
	}

	// 获取订单最新的已经支款完成的财务支款信息
	refundList, err := models.RefundListByOrder(ctx, orderIDs)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxWarnf(ctx, "%s RefundListByOrder err: %v, orderIDs: %v", funcName, err, orderIDs)
		return resp, nil
	}
	refundModMap := make(map[int64]*model.FinancialRefund)
	for _, refund := range refundList {
		refundModMap[refund.OrderID] = refund
	}

	// 订单关联的所有商品
	orderGoodsMods, err := (&models.OrderGoods{}).GetAllOrderGoods(ctx, orderIDs)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxErrorf(ctx, "%s GetAllOrderGoods db err: %v, orderIDs: %v", funcName, err, orderIDs)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	orderGoodsModMap := make(map[int64][]*model.OrderGood)
	for _, orderGoodsMod := range orderGoodsMods {
		orderGoodsModMap[orderGoodsMod.OrderID] = append(orderGoodsModMap[orderGoodsMod.OrderID], orderGoodsMod)
	}

	items := make([]*order.OrderListItem, 0, lens)
	for _, orderListItem := range orderListItems {
		orderItem := orderModToPb(orderListItem.Order)
		orderPay := orderPayModToPb(orderListItem.OrderPay, models.DisbursementType(orderListItem.Order.DisbursementType))
		// 支款订单-支款完成时
		// 订单状态：status 5
		// 订单审核状态： status 1
		// 支款支付状态：status_disbursement 1
		if orderItem.GetStatus() == order.StatusOrder_STATUS_ORDER_DISBURSEMENT &&
			orderItem.GetStatusReview() == order.StatusOrderReview_STATUS_REVIEW_PASS &&
			orderItem.GetStatusPayDisbursement() == order.StatusOrderPay_STATUS_PAY_PAID {

			// 订单的金额展示关联财务最新的财务支款信息
			if refund, ok := refundModMap[orderListItem.Order.ID]; ok {
				// disbursement_type 支款类型#1%退定金|2%退服务费|3%奖学金|4%退差价|5%支付违约金
				// refund_type 款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
				orderItem.DisbursementType = order.OrderDisbursementType(refund.RefundType)
				orderPay.AmountDisbursementList = refund.RealAmountRmb
				orderPay.CurrencyDisbursementList = models.CurrencyChina
			}

		}

		item := &order.OrderListItem{
			Order:      orderItem,
			OrderPay:   orderPay,
			OrderGoods: make([]*order.OrderGoodsEntity, 0),
			ContractNo: orderListItem.ContractNo,
		}

		if orderGoodsModSli, ok := orderGoodsModMap[orderListItem.Order.ID]; ok {
			item.OrderGoods = make([]*order.OrderGoodsEntity, 0, len(orderGoodsModSli))
			for _, orderGoodsMod := range orderGoodsModSli {
				item.OrderGoods = append(item.OrderGoods, orderGoodsModToPb(orderGoodsMod))
			}
		}

		items = append(items, item)
	}

	resp.Items = items

	return
}

// GetOrderInfoByIds implements the ServiceImpl interface.
func (o *OrderService) GetOrderInfoByIds(ctx context.Context, req *order.GetOrderInfoByIdsReq) (resp *order.GetOrderInfoByIdsRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetOrderInfoByIdsRsp{
		Items: make([]*order.OrderInfoItem, 0),
		Base:  coderror.MakeSuccessBaseRsp(),
	}

	// 订单信息
	orderListItems, err := (&models.Order{}).GetOrderInfoByIds(ctx, req.GetIds(), req.GetOrderNos())
	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxWarnf(ctx, "%s GetOrderInfoByIds err: %v, orderIDs: %v, orderNos: %v", funcName, err, req.GetIds(), req.GetOrderNos())
		return resp, nil
	}

	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderInfoByIds db err: %v, orderIDs: %v, orderNos: %v", funcName, err, req.GetIds(), req.GetOrderNos())
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	lens := len(orderListItems)
	if lens == 0 {
		return
	}

	orderIDs := make([]int64, 0, lens)
	for _, item := range orderListItems {
		orderIDs = append(orderIDs, item.Order.ID)
	}

	// 订单关联的所有商品
	orderGoodsMods, err := (&models.OrderGoods{}).GetAllOrderGoods(ctx, orderIDs)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxWarnf(ctx, "%s GetAllOrderGoods err: %v, orderIDs: %v", funcName, err, orderIDs)
		return resp, nil
	}

	if err != nil {
		logger.CtxErrorf(ctx, "%s GetAllOrderGoods db err: %v, orderIDs: %v", funcName, err, orderIDs)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	orderGoodsModMap := make(map[int64][]*model.OrderGood)
	for _, orderGoodsMod := range orderGoodsMods {
		orderGoodsModMap[orderGoodsMod.OrderID] = append(orderGoodsModMap[orderGoodsMod.OrderID], orderGoodsMod)
	}

	items := make([]*order.OrderInfoItem, 0, len(orderListItems))
	for _, orderListItem := range orderListItems {
		item := &order.OrderInfoItem{
			Order:      orderModToPb(orderListItem.Order),
			OrderPay:   orderPayModToPb(orderListItem.OrderPay, models.DisbursementType(orderListItem.Order.DisbursementType)),
			OrderGoods: make([]*order.OrderGoodsEntity, 0),
		}

		if orderGoodsModSli, ok := orderGoodsModMap[orderListItem.Order.ID]; ok {
			item.OrderGoods = make([]*order.OrderGoodsEntity, 0, len(orderGoodsModSli))
			for _, orderGoodsMod := range orderGoodsModSli {
				item.OrderGoods = append(item.OrderGoods, orderGoodsModToPb(orderGoodsMod))
			}
		}

		items = append(items, item)
	}

	resp.Items = items

	return
}

// GetOrderInfo implements the ServiceImpl interface.
func (o *OrderService) GetOrderInfo(ctx context.Context, req *order.GetOrderInfoReq) (resp *order.GetOrderInfoRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetOrderInfoRsp{
		Order:             &order.OrderEntity{},
		OrderPay:          &order.OrderPayEntity{},
		OrderGoods:        make([]*order.OrderGoodsEntity, 0),
		OrderOperationLog: make([]*order.OrderOperationLogEntity, 0),
		SourceIds:         make([]int64, 0),
		SubmissionIds:     make([]int64, 0),
		Base:              coderror.MakeSuccessBaseRsp(),
	}

	orderId := req.GetId()
	orderNo := req.GetOrderNo()

	if orderId <= 0 && strings.TrimSpace(orderNo) == "" {
		logger.CtxErrorf(ctx, "%s GetOrderById db err: %v, orderId: %v:, orderNo: %v: ", funcName, err, orderId, orderNo)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	orderWithOrderPay := &models.OrderWithOrderPay{}

	if orderId > 0 {
		// 订单
		orderWithOrderPay, err = (&models.Order{}).GetOrderById(ctx, orderId)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxWarnf(ctx, "%s GetOrderById err: %v, orderId: %v: ", funcName, err, orderId)
			return resp, nil
		}
		if err != nil {
			logger.CtxErrorf(ctx, "%s GetOrderById db err: %v, orderId: %v: ", funcName, err, orderId)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, err
		}

		resp.Order = orderModToPb(orderWithOrderPay.Order)
	}

	if orderId <= 0 {
		// 订单
		orderWithOrderPay, err = (&models.Order{}).GetOrderByOrderNo(ctx, orderNo)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxWarnf(ctx, "%s GetOrderById err: %v, orderId: %v: ", funcName, err, orderId)
			return resp, nil
		}
		if err != nil {
			logger.CtxErrorf(ctx, "%s GetOrderById db err: %v, orderId: %v: ", funcName, err, orderId)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, err
		}

		resp.Order = orderModToPb(orderWithOrderPay.Order)
	}

	resp.OrderPay = orderPayModToPb(orderWithOrderPay.OrderPay, models.DisbursementType(orderWithOrderPay.Order.DisbursementType))
	orderId = resp.Order.GetId()

	// 订单商品
	orderGoodsMods, err := (&models.OrderGoods{}).GetOrderGoodsByOrderId(ctx, orderId)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxWarnf(ctx, "%s GetOrderGoodsByOrderId err: %v orderId: %v: ", funcName, err, orderId)
		return resp, nil
	}
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderGoodsByOrderId db err: %v orderId: %v: ", funcName, err, orderId)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, err
	}

	orderGoodsEntities := make([]*order.OrderGoodsEntity, 0, len(orderGoodsMods))
	for _, orderGoodsMod := range orderGoodsMods {
		orderGoodsEntities = append(orderGoodsEntities, orderGoodsModToPb(orderGoodsMod))
	}
	resp.OrderGoods = orderGoodsEntities

	// 订单操作日志
	orderOperationLogMods, err := (&models.OrderOperationLog{}).GetOrderOperationLogByOrderId(ctx, orderId)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxWarnf(ctx, "%s GetOrderOperationLogByOrderId err: %v, orderId: %v: ", funcName, err, orderId)
		return resp, nil
	}
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderOperationLogByOrderId db err: %v, orderId: %v: ", funcName, err, orderId)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, err
	}

	operationLogs := make([]*order.OrderOperationLogEntity, 0, len(orderOperationLogMods))
	for _, orderOperationLogMod := range orderOperationLogMods {
		operationLogs = append(operationLogs, orderOperationLogModToPb(orderOperationLogMod))
	}
	resp.OrderOperationLog = operationLogs

	// 共同提交+订单来源
	relationMods, err := (&models.Order{}).GetOrderRelationsByOrderId(ctx, orderId)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxWarnf(ctx, "%s GetOrderRelationsByOrderId err: %v, orderId: %v: ", funcName, err, orderId)
		return resp, nil
	}
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderRelationsByOrderId db err: %v, orderId: %v: ", funcName, err, orderId)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, err
	}

	sourceIds := make([]int64, 0, len(relationMods))
	submissionIds := make([]int64, 0, len(relationMods))

	for _, relationMod := range relationMods {
		// 共同提交
		if relationMod.Action == int32(models.OrderRelationActionSubmission) {
			submissionIds = append(submissionIds, relationMod.UserID)
		}

		// 订单来源
		if relationMod.Action == int32(models.OrderRelationActionSource) {
			sourceIds = append(sourceIds, relationMod.UserID)
		}
	}

	resp.SourceIds = sourceIds
	resp.SubmissionIds = submissionIds

	return
}

// UpdateOrder 更新订单
func (o *OrderService) UpdateOrder(ctx context.Context, req *order.SaveOrderReq) (resp *order.SaveOrderRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.SaveOrderRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	orderId := req.GetOrder().GetId()
	if orderId == 0 {
		logger.CtxErrorf(ctx, "%s order_id: %d zero", funcName, orderId)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return
	}

	timeNow := time.Now()
	loginUserId := ctxmeta.MustGetAuth(ctx).EmployeeId()

	var orderWithOrderPay *models.OrderWithOrderPay
	orderWithOrderPay, err = (&models.Order{}).GetOrderById(ctx, orderId)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxErrorf(ctx, "%s GetOrderById err: %v, orderId: %v", funcName, err, orderId)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_OrderNotFound)
		return resp, nil
	}

	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderById db err: %v, orderId: %v", funcName, err, orderId)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	// 订单状态：6%已关闭 不允许编辑订单信息
	if orderWithOrderPay.Order.Status == int32(models.StatusOrderClose) {
		logger.CtxInfof(ctx, "%s GetOrderById OrderStatusNotPermission order_id: %d"+
			"old_order_status: %d, old_order_status_review: %d", funcName, orderId, orderWithOrderPay.Order.Status, orderWithOrderPay.Order.StatusReview)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_OrderStatusNotPermission)
		return
	}

	// 审核状态：3%草稿待审核|4%驳回待审核 不允许编辑订单信息
	if orderWithOrderPay.Order.StatusReview == int32(models.StatusReviewDraftAudit) ||
		orderWithOrderPay.Order.StatusReview == int32(models.StatusReviewRejectAudit) {
		logger.CtxInfof(ctx, "%s GetOrderById OrderStatusNotPermission order_id: %d"+
			"old_order_status: %d, old_order_status_review: %d", funcName, orderId, orderWithOrderPay.Order.Status, orderWithOrderPay.Order.StatusReview)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_OrderStatusNotPermission)
		return
	}

	orderNo := orderWithOrderPay.Order.OrderNo
	resp.Id = orderId
	resp.OrderNo = orderNo

	mod := orderPbToMod(req.GetOrder(), orderWithOrderPay.Order)

	mod.OrderNo = orderNo
	mod.CreatedBy = orderWithOrderPay.Order.CreatedBy
	mod.UpdatedBy = loginUserId
	mod.CreatedAt = orderWithOrderPay.Order.CreatedAt
	mod.UpdatedAt = timeNow

	modPay := orderPayPbToModel(req.GetOrderPay(), orderWithOrderPay.OrderPay, orderWithOrderPay.Order.Status)

	goodsMods := make([]*model.OrderGood, 0)
	for _, pb := range req.GetOrderGoods() {
		goodMod := orderGoodsPbToMod(pb)
		goodMod.ID = 0 // 订单商品数据，先删除后新增，防止出现：duplicated key not allowed
		goodMod.OrderID = orderId
		goodsMods = append(goodsMods, goodMod)
	}

	logEntity := &order.OrderLogEntity{
		Old: &order.OrderLogEntityItem{
			Status:                order.StatusOrder(orderWithOrderPay.Order.Status),
			StatusReview:          order.StatusOrderReview(orderWithOrderPay.Order.StatusReview),
			StatusWorkflow:        order.StatusOrderWorkflow(orderWithOrderPay.Order.StatusWorkflow),
			StatusPrev:            order.StatusOrder(orderWithOrderPay.Order.StatusPrev),
			StatusReviewPrev:      order.StatusOrderReview(orderWithOrderPay.Order.StatusReviewPrev),
			StatusPayDeposit:      order.StatusOrderPay(orderWithOrderPay.Order.StatusPayDeposit),
			StatusPayFirst:        order.StatusOrderPay(orderWithOrderPay.Order.StatusPayFirst),
			StatusPayFinal:        order.StatusOrderPay(orderWithOrderPay.Order.StatusPayFinal),
			StatusPayDisbursement: order.StatusOrderPay(orderWithOrderPay.Order.StatusPayDisbursement),
		},
		New: &order.OrderLogEntityItem{
			Status:                order.StatusOrder(mod.Status),
			StatusReview:          order.StatusOrderReview(mod.StatusReview),
			StatusWorkflow:        order.StatusOrderWorkflow(mod.StatusWorkflow),
			StatusPrev:            order.StatusOrder(mod.StatusPrev),
			StatusReviewPrev:      order.StatusOrderReview(mod.StatusReviewPrev),
			StatusPayDeposit:      order.StatusOrderPay(mod.StatusPayDeposit),
			StatusPayFirst:        order.StatusOrderPay(mod.StatusPayFirst),
			StatusPayFinal:        order.StatusOrderPay(mod.StatusPayFinal),
			StatusPayDisbursement: order.StatusOrderPay(mod.StatusPayDisbursement),
		},
	}

	logByte, _ := json.Marshal(logEntity)

	operationLogMods := make([]*model.OrderOperationLog, 0)
	// 订单操作日志 status
	if mod.Status != orderWithOrderPay.Order.Status {
		operationLogMod := model.OrderOperationLog{
			ID:        0,
			OrderID:   orderId,
			OrderNo:   orderNo,
			Type:      int32(order.OrderOperationType_ORDER_OPERATION_TYPE_ORDER),
			Log:       string(logByte),
			CreatedBy: loginUserId,
			CreatedAt: timeNow,
		}
		operationLogMods = append(operationLogMods, &operationLogMod)
	}

	// 订单操作日志 status_review
	if mod.StatusReview != orderWithOrderPay.Order.StatusReview {
		operationLogMod := model.OrderOperationLog{
			ID:        0,
			OrderID:   orderId,
			OrderNo:   orderNo,
			Type:      int32(order.OrderOperationType_ORDER_OPERATION_TYPE_REVIEW),
			Log:       string(logByte),
			CreatedBy: loginUserId,
			CreatedAt: timeNow,
		}
		operationLogMods = append(operationLogMods, &operationLogMod)
	}

	// order_relation
	relationMods := make([]*model.OrderRelation, 0)
	relationAction := models.OrderRelationActionUpdate // 编辑
	relation, _ := (&models.Order{}).GetOrderRelationByActionUserId(ctx, orderId, loginUserId, relationAction)

	// 如果 编辑订单关联关系 已存在，则不需要创建 编辑订单关联关系
	if relation.ID == 0 {
		// 订单关系，编辑订单时，创建编辑订单关联关系
		relationMods = []*model.OrderRelation{
			{
				ID:        0,
				OrderID:   orderId,
				UserID:    loginUserId,
				Action:    int32(relationAction), // 编辑
				Status:    int32(models.StatusYesNoYes),
				CreatedAt: timeNow,
				UpdatedAt: timeNow,
				DeletedAt: 0,
			},
		}
	}

	// 根据订单状态设置订单相关时间
	mod = o.setOrderTimeAt(mod, timeNow)

	// 定金阶段，实际成交金额 = 定金金额
	if mod.Status == int32(models.StatusOrderDeposit) {
		modPay.AmountTotal = modPay.AmountDeposit
	}

	// 仅首款涉及金额计算
	// 定金阶段，不涉及金额相关的逻辑
	// 支款阶段，已经计算完毕，仅涉及支款及支款待审核
	if mod.Status == int32(models.StatusOrderFirst) {
		// TODO LIUSHUANG 金额相关的逻辑
		modPay, goodsMods = calculateOrderAmount(modPay, goodsMods)
	}

	// 尾款待支付
	// 涉及字段：免除尾款，尾款实收金额
	// 影响字段：尾款实收金额，实际成交金额
	if mod.Status == int32(models.StatusOrderFinal) {
		// 定金
		amountDeposit := stringsToDecimal(orderWithOrderPay.OrderPay.AmountDeposit)
		// 首款
		amountFirst := stringsToDecimal(orderWithOrderPay.OrderPay.AmountFirst)
		// 尾款实收金额
		amountFinal := stringsToDecimal(req.GetOrderPay().GetAmountFinal())
		// 免除尾款#1%是|2%否
		if modPay.ExemptFinal == int32(models.StatusYesNoYes) {
			// 尾款实收为0
			amountFinal = decimal.Zero
		}

		// 实际成交金额  = 定金 + 实收首款 + 实收尾款
		amountTotal := amountDeposit.Add(amountFirst).Add(amountFinal)

		// 尾款
		modPay.AmountFinal = amountFinal.String()
		// 实际成交金额
		modPay.AmountTotal = amountTotal.String()
	}

	err = (&models.OrderTransaction{}).UpdateOrder(ctx, orderId, mod, modPay, goodsMods, operationLogMods, relationMods, req.GetSourceIds(), req.GetSubmissionIds())
	if err != nil {
		logger.CtxErrorf(ctx, "%s UpdateOrder db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	return
}

// CreateOrder 创建订单
func (o *OrderService) CreateOrder(ctx context.Context, req *order.SaveOrderReq) (resp *order.SaveOrderRsp, err error) {
	funcName := getFuncName(1)

	orderId := req.GetOrder().GetId()
	resp = &order.SaveOrderRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	timeNow := time.Now()
	loginUserId := ctxmeta.MustGetAuth(ctx).EmployeeId()

	mod := orderPbToMod(req.GetOrder(), nil)

	orderNo := generateOrderNo()
	mod.OrderNo = orderNo
	mod.CreatedBy = loginUserId
	mod.UpdatedBy = loginUserId
	mod.CreatedAt = timeNow
	mod.UpdatedAt = timeNow
	mod.OwnedBy = loginUserId

	if mod.Status <= 0 {
		mod.Status = int32(models.StatusOrderDeposit)
	}
	if mod.StatusReview <= 0 {
		mod.StatusReview = int32(models.StatusReviewDraft)
	}

	if mod.StatusPayDeposit <= 0 {
		mod.StatusPayDeposit = int32(models.StatusPayPending)
	}
	if mod.StatusPayFirst <= 0 {
		mod.StatusPayFirst = int32(models.StatusPayPending)
	}
	if mod.StatusPayFinal <= 0 {
		mod.StatusPayFinal = int32(models.StatusPayPending)
	}
	if mod.StatusPayDisbursement <= 0 {
		mod.StatusPayDisbursement = int32(models.StatusPayPending)
	}

	// bug 208 未选择商品存草稿，此时列表页付款方式应为-
	//if mod.InstallmentType <= 0 {
	//	mod.InstallmentType = int32(models.InstallmentTypeByStages)
	//}

	if mod.DisbursementType <= 0 {
		mod.DisbursementType = int32(models.DisbursementTypeRefundDeposit)
	}

	modPay := orderPayPbToModel(req.GetOrderPay(), nil, 0)

	if modPay.ExemptFinal <= 0 {
		modPay.ExemptFinal = int32(models.StatusYesNoNo)
	}
	if modPay.UrgentService <= 0 {
		modPay.UrgentService = int32(models.StatusYesNoNo)
	}

	// 加急倍数（默认值：1）
	modPay.UrgentTimes = setDefaultDecimalField(modPay.UrgentTimes, strconv.Itoa(models.OrderUrgentTimesDefault))
	// 首期款折扣（默认值：100）
	modPay.DiscountRate = setDefaultDecimalField(modPay.DiscountRate, strconv.Itoa(models.OrderDiscountRateDefault))

	logEntity := &order.OrderLogEntity{
		Old: &order.OrderLogEntityItem{
			Status:                0,
			StatusReview:          0,
			StatusWorkflow:        0,
			StatusPrev:            0,
			StatusReviewPrev:      0,
			StatusPayDeposit:      0,
			StatusPayFirst:        0,
			StatusPayFinal:        0,
			StatusPayDisbursement: 0,
		},
		New: &order.OrderLogEntityItem{
			Status:                order.StatusOrder(mod.Status),
			StatusReview:          order.StatusOrderReview(mod.StatusReview),
			StatusWorkflow:        order.StatusOrderWorkflow(mod.StatusWorkflow),
			StatusPrev:            order.StatusOrder(mod.StatusPrev),
			StatusReviewPrev:      order.StatusOrderReview(mod.StatusReviewPrev),
			StatusPayDeposit:      order.StatusOrderPay(mod.StatusPayDeposit),
			StatusPayFirst:        order.StatusOrderPay(mod.StatusPayFirst),
			StatusPayFinal:        order.StatusOrderPay(mod.StatusPayFinal),
			StatusPayDisbursement: order.StatusOrderPay(mod.StatusPayDisbursement),
		},
	}

	goodsMods := make([]*model.OrderGood, 0)
	for _, pb := range req.GetOrderGoods() {
		goodMod := orderGoodsPbToMod(pb)
		goodMod.ID = 0 // 订单商品数据，先删除后新增，防止出现：duplicated key not allowed
		goodsMods = append(goodsMods, goodMod)
	}

	// 定金阶段，实际成交金额 = 定金金额，付款方式应为-
	if mod.Status == int32(models.StatusOrderDeposit) {
		modPay.AmountTotal = modPay.AmountDeposit
		mod.InstallmentType = 0
	}

	// 仅首款尾款阶段涉及金额计算
	// 定金阶段，不涉及金额相关的逻辑
	// 支款阶段，已经计算完毕，仅涉及支款及支款待审核
	if mod.Status == int32(models.StatusOrderFirst) || mod.Status == int32(models.StatusOrderFinal) {
		// TODO LIUSHUANG 金额相关的逻辑
		modPay, goodsMods = calculateOrderAmount(modPay, goodsMods)
	}

	// 首款订单：未选择商品存草稿，此时列表页付款方式应为-
	if mod.Status == int32(models.StatusOrderFirst) && len(goodsMods) == 0 {
		mod.InstallmentType = 0
	}

	logByte, _ := json.Marshal(logEntity)
	operationLogMods := []*model.OrderOperationLog{
		{
			// 订单操作日志 status
			ID:        0,
			OrderID:   orderId,
			OrderNo:   orderNo,
			Type:      int32(order.OrderOperationType_ORDER_OPERATION_TYPE_ORDER),
			Log:       string(logByte),
			CreatedBy: loginUserId,
			CreatedAt: timeNow,
		},
		{
			// 订单操作日志 status_review
			ID:        0,
			OrderID:   orderId,
			OrderNo:   orderNo,
			Type:      int32(order.OrderOperationType_ORDER_OPERATION_TYPE_REVIEW),
			Log:       string(logByte),
			CreatedBy: loginUserId,
			CreatedAt: timeNow,
		},
	}

	// order_relation
	// 订单创建时（创建关系，资产归属关系都是创建人）
	relationMods := []*model.OrderRelation{
		{
			ID:        0,
			OrderID:   orderId,
			UserID:    loginUserId,
			Action:    int32(models.OrderRelationActionCreate), // 创建
			Status:    int32(models.StatusYesNoYes),
			CreatedAt: timeNow,
			UpdatedAt: timeNow,
			DeletedAt: 0,
		},
		{
			ID:        0,
			OrderID:   orderId,
			UserID:    loginUserId,
			Action:    int32(models.OrderRelationActionOwn), // 资产归属
			Status:    int32(models.StatusYesNoYes),
			CreatedAt: timeNow,
			UpdatedAt: timeNow,
			DeletedAt: 0,
		},
	}

	// 用于去重的两个 map
	sourceProcessed := make(map[int64]struct{}) // 记录已处理的 Source employeeId
	ownProcessed := make(map[int64]struct{})    // 记录已处理的 Own employeeId

	// 订单来源（Source）
	if len(req.GetSourceIds()) > 0 {
		for _, employeeId := range req.GetSourceIds() {
			if _, exists := sourceProcessed[employeeId]; !exists {
				source := &model.OrderRelation{
					ID:        0,
					OrderID:   orderId,
					UserID:    employeeId,
					Action:    int32(models.OrderRelationActionSource), // 订单来源
					Status:    int32(models.StatusYesNoYes),
					CreatedAt: timeNow,
					UpdatedAt: timeNow,
					DeletedAt: 0,
				}
				relationMods = append(relationMods, source)
				sourceProcessed[employeeId] = struct{}{} // 标记已处理
			}
		}
	}

	// 共同提交（Submission）
	if len(req.GetSubmissionIds()) > 0 {
		for _, employeeId := range req.GetSubmissionIds() {
			if _, exists := ownProcessed[employeeId]; !exists {
				own := &model.OrderRelation{
					ID:        0,
					OrderID:   orderId,
					UserID:    employeeId,
					Action:    int32(models.OrderRelationActionSubmission), // 共同提交
					Status:    int32(models.StatusYesNoYes),
					CreatedAt: timeNow,
					UpdatedAt: timeNow,
					DeletedAt: 0,
				}
				relationMods = append(relationMods, own)
				ownProcessed[employeeId] = struct{}{} // 标记已处理
			}
		}
	}

	// 根据订单状态设置订单相关时间
	mod = o.setOrderTimeAt(mod, timeNow)

	logger.CtxInfof(ctx, "%s InstallmentType: %v, status: %v, len(goodsMods): %v, len(relationMods): %v", funcName, mod.InstallmentType, mod.Status, len(goodsMods), len(relationMods))

	orderId, err = (&models.OrderTransaction{}).CreateOrder(ctx, mod, modPay, goodsMods, operationLogMods, relationMods)
	if err != nil {
		logger.CtxErrorf(ctx, "%s CreateOrder db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	resp.Id = orderId
	resp.OrderNo = orderNo

	return
}

// SaveOrder implements the ServiceImpl interface.
func (o *OrderService) SaveOrder(ctx context.Context, req *order.SaveOrderReq) (resp *order.SaveOrderRsp, err error) {
	if req.GetOrder().GetId() > 0 {
		return o.UpdateOrder(ctx, req)
	}

	return o.CreateOrder(ctx, req)
}

// UpdateOrderStatus implements the ServiceImpl interface.
func (o *OrderService) UpdateOrderStatus(ctx context.Context, req *order.UpdateOrderStatusReq) (resp *order.UpdateOrderStatusRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.UpdateOrderStatusRsp{
		Id:   req.Id,
		Base: coderror.MakeSuccessBaseRsp(),
	}

	timeNow := time.Now()
	orderId := req.GetId()
	loginUserId := ctxmeta.MustGetAuth(ctx).EmployeeId()

	// 订单
	orderWithOrderPay, errGetOrder := (&models.Order{}).GetOrderById(ctx, orderId)
	if errors.Is(errGetOrder, gorm.ErrRecordNotFound) {
		logger.CtxErrorf(ctx, "%s GetOrderById err: %v, orderId: %v: ", funcName, errGetOrder, orderId)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_OrderNotFound)
		return resp, nil
	}
	if errGetOrder != nil {
		logger.CtxErrorf(ctx, "%s GetOrderById db err: %v, orderId: %v: ", funcName, errGetOrder, orderId)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	// update order
	updatesMap := make(map[string]any)
	// operation log
	lStatusMap := make(map[string]int32)
	// 默认不更新订单支付信息
	updatesOrderPay := models.StatusYesNoNo
	// update order_pay
	orderPayUpdatesMap := make(map[string]any)

	// 更新时间
	updatesMap[model.OrderColumns.UpdatedAt()] = timeNow

	// TODO: 处理审核意见和交易单号 - 需要重新生成protobuf代码后启用
	// if req.ReviewComment != "" {
	//	updatesMap[model.OrderColumns.ReviewComment()] = req.ReviewComment
	// }
	// if req.TransactionNo != "" {
	//	updatesMap[model.OrderColumns.TransactionNo()] = req.TransactionNo
	// }

	// 更新有变化的字段
	lStatusMap[model.OrderColumns.Status()] = orderWithOrderPay.Order.Status
	lStatusMap[model.OrderColumns.StatusReview()] = orderWithOrderPay.Order.StatusReview
	lStatusMap[model.OrderColumns.StatusWorkflow()] = orderWithOrderPay.Order.StatusWorkflow
	lStatusMap[model.OrderColumns.StatusPayDeposit()] = orderWithOrderPay.Order.StatusPayDeposit
	lStatusMap[model.OrderColumns.StatusPayFirst()] = orderWithOrderPay.Order.StatusPayFirst
	lStatusMap[model.OrderColumns.StatusPayFinal()] = orderWithOrderPay.Order.StatusPayFinal
	lStatusMap[model.OrderColumns.StatusPayDisbursement()] = orderWithOrderPay.Order.StatusPayDisbursement

	// order_relation
	relationMods := make([]*model.OrderRelation, 0)
	if req.GetUpdatedId() > 0 {
		updatesMap[model.OrderColumns.UpdatedBy()] = int32(req.GetUpdatedId())
		//lStatusMap[model.OrderColumns.UpdatedBy()] = int32(req.GetUpdatedId())

		// order_relation
		relationAction := models.OrderRelationActionUpdate // 编辑
		relation, _ := (&models.Order{}).GetOrderRelationByActionUserId(ctx, orderId, loginUserId, relationAction)
		// 如果编辑关系已存在，则不需要记录编辑关系
		if relation == nil || relation.ID == 0 {
			// 订单关系，更新订单时，最后更新人关联订单
			relationMods = []*model.OrderRelation{
				{
					ID:        0,
					OrderID:   orderId,
					UserID:    loginUserId,
					Action:    int32(relationAction), // 2%编辑
					Status:    int32(models.StatusYesNoYes),
					CreatedAt: timeNow,
					UpdatedAt: timeNow,
					DeletedAt: 0,
				},
			}
		}
	}

	if req.GetReviewerId() > 0 {
		updatesMap[model.OrderColumns.ReviewerID()] = int32(req.GetReviewerId())
		//lStatusMap[model.OrderColumns.ReviewerID()] = int32(req.GetReviewerId())

		// order_relation
		relationAction := models.OrderRelationActionReview // 审核
		relation, _ := (&models.Order{}).GetOrderRelationByActionUserId(ctx, orderId, loginUserId, relationAction)
		// 如果编辑关系已存在，则不需要记录编辑关系
		if relation == nil || relation.ID == 0 {
			// 订单关系，更新订单时，最后更新人关联订单
			relationMods = []*model.OrderRelation{
				{
					ID:        0,
					OrderID:   orderId,
					UserID:    loginUserId,
					Action:    int32(relationAction), // 编辑
					Status:    int32(models.StatusYesNoYes),
					CreatedAt: timeNow,
					UpdatedAt: timeNow,
					DeletedAt: 0,
				},
			}
		}
	}

	if req.GetStatus() > 0 {
		updatesMap[model.OrderColumns.Status()] = int32(req.GetStatus())
		lStatusMap[model.OrderColumns.Status()] = int32(req.GetStatus())
	}

	if req.GetStatusReview() > 0 {
		updatesMap[model.OrderColumns.StatusReview()] = int32(req.GetStatusReview())
		lStatusMap[model.OrderColumns.StatusReview()] = int32(req.GetStatusReview())
	}

	if req.GetStatusWorkflow() > 0 {
		updatesMap[model.OrderColumns.StatusWorkflow()] = int32(req.GetStatusWorkflow())
		lStatusMap[model.OrderColumns.StatusWorkflow()] = int32(req.GetStatusWorkflow())
	}

	if req.GetStatusPayDeposit() > 0 {
		updatesMap[model.OrderColumns.StatusPayDeposit()] = int32(req.GetStatusPayDeposit())
		lStatusMap[model.OrderColumns.StatusPayDeposit()] = int32(req.GetStatusPayDeposit())
	}

	if req.GetStatusPayFirst() > 0 {
		updatesMap[model.OrderColumns.StatusPayFirst()] = int32(req.GetStatusPayFirst())
		lStatusMap[model.OrderColumns.StatusPayFirst()] = int32(req.GetStatusPayFirst())
	}

	if req.GetStatusPayFinal() > 0 {
		updatesMap[model.OrderColumns.StatusPayFinal()] = int32(req.GetStatusPayFinal())
		lStatusMap[model.OrderColumns.StatusPayFinal()] = int32(req.GetStatusPayFinal())
	}

	// 支款流程
	// 1、发起支款后，就是支款待审核状态
	// 2、财务操作审核通过，订单就是待打款状态
	// 3、财务操作完成支款，订单就是支款完成状态
	// 4、财务操作审核驳回，订单就是支款审核驳回状态
	// 5、财务操作回退呢，订单就回到支款待审核状态
	if req.GetStatusPayDisbursement() > 0 {
		updatesMap[model.OrderColumns.StatusPayDisbursement()] = int32(req.GetStatusPayDisbursement())
		lStatusMap[model.OrderColumns.StatusPayDisbursement()] = int32(req.GetStatusPayDisbursement())

		// 财务支款审核通过后 - 操作完成打款
		// 支款支付状态 status_pay_disbursement -> 已支付
		// 更新支款付款时间
		// 避免撤销支款订单，恢复到支款订单-支款完成，恢复订单状态，与上一次财务审核通过一致，因此要做区分
		if req.GetStatusPayDisbursement() == order.StatusOrderPay_STATUS_PAY_PAID && req.GetRevokeDisbursement() != order.StatusYesNo_STATUS_YES {
			updatesMap[model.OrderColumns.PayDisbursementAt()] = timeNow
		}
	}

	// 已下定金-待审核->审核不通过->审核驳回（审核状态+审核驳回时间）
	//			    ->审核通过->待下单
	//
	// 支付待确认-待审核->审核不通过->审核驳回（审核状态+审核驳回时间）
	//                ->审核通过->一次性付款->支付成功
	//                ->审核通过->分期->尾款待支付-服务未完成
	//
	// 尾款待支付-待审核->审核不通过->审核驳回（审核状态+审核驳回时间）
	//                ->审核通过->支付成功
	//
	// 支款订单-待审核->审核不通过->审核驳回（审核状态+审核驳回时间）
	//              ->审核通过->待打款

	// 无论任何订单状态
	// 审核驳回
	// 更新审核状态
	// 更新审核驳回时间
	if req.GetStatusReview() == order.StatusOrderReview_STATUS_REVIEW_REJECT {
		// 审核不通过 -> 审核驳回
		updatesMap[model.OrderColumns.StatusReview()] = int32(order.StatusOrderReview_STATUS_REVIEW_REJECT)
		lStatusMap[model.OrderColumns.StatusReview()] = int32(order.StatusOrderReview_STATUS_REVIEW_REJECT)

		// 审核驳回时间
		updatesMap[model.OrderColumns.RejectAt()] = timeNow

		// 需要更新订单财务信息
		updatesOrderPay = models.StatusYesNoYes
	}

	// 已下定金
	// 待审核（草稿待审核+审核驳回待审核）
	//   -> 审核通过->待下单
	// 更新审核状态
	// 更新定金审核通过时间
	if orderWithOrderPay.Order.Status == int32(order.StatusOrder_STATUS_ORDER_DEPOSIT) &&
		(orderWithOrderPay.Order.StatusReview == int32(order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT) ||
			orderWithOrderPay.Order.StatusReview == int32(order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT)) &&
		req.GetStatusReview() == order.StatusOrderReview_STATUS_REVIEW_PASS {

		// status_review -> 审核通过
		updatesMap[model.OrderColumns.StatusReview()] = int32(order.StatusOrderReview_STATUS_REVIEW_PASS)
		lStatusMap[model.OrderColumns.StatusReview()] = int32(order.StatusOrderReview_STATUS_REVIEW_PASS)

		// 定金审核通过时间
		updatesMap[model.OrderColumns.PassDepositAt()] = timeNow
		// 定金支付状态 status_pay_deposit -> 已支付
		updatesMap[model.OrderColumns.StatusPayDeposit()] = int32(order.StatusOrderPay_STATUS_PAY_PAID)
	}

	// 支付待确认
	// 待审核（草稿待审核+审核驳回待审核）
	//   -> 审核通过
	//   -> 一次性付款 -> 支付成功
	// 更新订单状态
	// 更新审核状态
	// 更新首款审核通过时间
	if orderWithOrderPay.Order.Status == int32(order.StatusOrder_STATUS_ORDER_FIRST) &&
		(orderWithOrderPay.Order.StatusReview == int32(order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT) ||
			orderWithOrderPay.Order.StatusReview == int32(order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT)) &&
		req.GetStatusReview() == order.StatusOrderReview_STATUS_REVIEW_PASS {

		// 一次性付款（全款）->支付成功 (status -> 支付成功，status_review -> 审核通过)
		if orderWithOrderPay.Order.InstallmentType == int32(order.OrderInstallmentType_INSTALLMENT_TYPE_FULL_PAYMENT) {
			// status -> 支付成功
			updatesMap[model.OrderColumns.Status()] = int32(order.StatusOrder_STATUS_ORDER_SUCCESS)
			lStatusMap[model.OrderColumns.Status()] = int32(order.StatusOrder_STATUS_ORDER_SUCCESS)

			// status_review -> 审核通过
			updatesMap[model.OrderColumns.StatusReview()] = int32(order.StatusOrderReview_STATUS_REVIEW_PASS)
			lStatusMap[model.OrderColumns.StatusReview()] = int32(order.StatusOrderReview_STATUS_REVIEW_PASS)
		}

		// 分期 -> 尾款待支付-服务未完成 (status -> 尾款待支付，status_review -> 草稿)
		if orderWithOrderPay.Order.InstallmentType == int32(order.OrderInstallmentType_INSTALLMENT_TYPE_BY_STAGES) {
			// status -> 尾款待支付
			updatesMap[model.OrderColumns.Status()] = int32(order.StatusOrder_STATUS_ORDER_FINAL)
			lStatusMap[model.OrderColumns.Status()] = int32(order.StatusOrder_STATUS_ORDER_FINAL)

			// status_review -> 草稿
			updatesMap[model.OrderColumns.StatusReview()] = int32(order.StatusOrderReview_STATUS_REVIEW_DRAFT)
			lStatusMap[model.OrderColumns.StatusReview()] = int32(order.StatusOrderReview_STATUS_REVIEW_DRAFT)
		}

		// 首款审核通过时间
		updatesMap[model.OrderColumns.PassFirstAt()] = timeNow
		// 首款支付状态 status_pay_first -> 已支付
		updatesMap[model.OrderColumns.StatusPayFirst()] = int32(order.StatusOrderPay_STATUS_PAY_PAID)
	}

	// 尾款待支付
	// 待审核（草稿待审核+审核驳回待审核）
	//   -> 审核通过
	//   -> 支付成功
	// 更新审核状态
	// 更新尾款审核通过时间
	if orderWithOrderPay.Order.Status == int32(order.StatusOrder_STATUS_ORDER_FINAL) &&
		(orderWithOrderPay.Order.StatusReview == int32(order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT) ||
			orderWithOrderPay.Order.StatusReview == int32(order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT)) &&
		req.GetStatusReview() == order.StatusOrderReview_STATUS_REVIEW_PASS {
		// status -> 支付成功
		updatesMap[model.OrderColumns.Status()] = int32(order.StatusOrder_STATUS_ORDER_SUCCESS)
		lStatusMap[model.OrderColumns.Status()] = int32(order.StatusOrder_STATUS_ORDER_SUCCESS)

		// status_review -> 审核通过
		updatesMap[model.OrderColumns.StatusReview()] = int32(order.StatusOrderReview_STATUS_REVIEW_PASS)
		lStatusMap[model.OrderColumns.StatusReview()] = int32(order.StatusOrderReview_STATUS_REVIEW_PASS)

		// 尾款审核通过时间
		updatesMap[model.OrderColumns.PassFinalAt()] = timeNow
		// 尾款支付状态 status_pay_final -> 已支付
		updatesMap[model.OrderColumns.StatusPayFinal()] = int32(order.StatusOrderPay_STATUS_PAY_PAID)
	}

	// 支款订单
	// 待审核（草稿待审核+审核驳回待审核）
	// 更新订单支付信息
	// 财务审核通过 status_review = pass, status_pay = pending
	// 财务审核驳回 status_review = reject, status_pay = pending
	// 财务审核通过后-完成支款 status_review = pass, status_pay = paid
	// 财务审核通过后-回退 status_review = audit, status_pay = pending
	if orderWithOrderPay.Order.Status == int32(order.StatusOrder_STATUS_ORDER_DISBURSEMENT) &&
		req.GetStatusReview() == order.StatusOrderReview_STATUS_REVIEW_PASS {
		updatesMap[model.OrderColumns.StatusReview()] = int32(req.GetStatusReview())
		lStatusMap[model.OrderColumns.StatusReview()] = int32(req.GetStatusReview())

		// 财务审核通过后
		if req.GetStatusPayDisbursement() == order.StatusOrderPay_STATUS_PAY_PENDING {
			// 支款审核通过时间
			updatesMap[model.OrderColumns.PassDisbursementAt()] = timeNow
		}

		// 财务审核通过后-完成支款
		// 避免撤销支款订单，恢复到支款订单-支款完成，恢复订单状态，与上一次财务审核通过一致，因此要做区分
		if req.GetStatusPayDisbursement() == order.StatusOrderPay_STATUS_PAY_PAID && req.GetRevokeDisbursement() != order.StatusYesNo_STATUS_YES {
			// 需要更新订单财务信息
			updatesOrderPay = models.StatusYesNoYes

			// 支款类型#1%退定金|2%退服务费|3%奖学金|4%退差价|5%支付违约金
			// 1%退定金|2%退服务费|4%退差价
			if orderWithOrderPay.Order.DisbursementType == int32(models.DisbursementTypeRefundDeposit) ||
				orderWithOrderPay.Order.DisbursementType == int32(models.DisbursementTypeRefundService) ||
				orderWithOrderPay.Order.DisbursementType == int32(models.DisbursementTypeRefundDifference) {
				// 已支款金额
				amountDisbursement := stringsToDecimal(orderWithOrderPay.OrderPay.AmountDisbursement)
				// 待审核支付金额
				amountDisbursementReview := stringsToDecimal(orderWithOrderPay.OrderPay.AmountDisbursementReview)
				// 已支款金额 = 已支款金额 + 待审核支款金额
				orderPayUpdatesMap[model.OrderPayColumns.AmountDisbursement()] = amountDisbursement.Add(amountDisbursementReview)
			}

			// 3%奖学金
			if orderWithOrderPay.Order.DisbursementType == int32(models.DisbursementTypeScholarship) {
				// 已支款奖学金
				amountBursary := stringsToDecimal(orderWithOrderPay.OrderPay.AmountBursary)
				// 待审核支付奖学金
				amountBursaryReview := stringsToDecimal(orderWithOrderPay.OrderPay.AmountBursaryReview)
				// 已支款奖学金 = 已支款奖学金 + 待审核奖学金
				orderPayUpdatesMap[model.OrderPayColumns.AmountBursary()] = amountBursary.Add(amountBursaryReview)
			}

			// 5%支付违约金
			if orderWithOrderPay.Order.DisbursementType == int32(models.DisbursementTypeLiquidatedDamages) {
				// 已支付违约金
				amountLiquidated := stringsToDecimal(orderWithOrderPay.OrderPay.AmountLiquidated)
				// 待审核违约金
				amountLiquidatedReview := stringsToDecimal(orderWithOrderPay.OrderPay.AmountLiquidatedReview)
				// 已支付违约金 = 已支付违约金 + 待审核违约金
				orderPayUpdatesMap[model.OrderPayColumns.AmountLiquidated()] = amountLiquidated.Add(amountLiquidatedReview)
			}

		}
	}

	// 关闭订单
	// 更新关闭时间
	// 更新操作关闭的员工UID
	if req.GetStatus() == order.StatusOrder_STATUS_ORDER_CLOSE {
		updatesMap[model.OrderColumns.ClosedBy()] = loginUserId
		updatesMap[model.OrderColumns.ClosedAt()] = timeNow

		// order_relation
		// 订单关系，关闭订单时，创建关闭订单关系
		relationMods = []*model.OrderRelation{
			{
				ID:        0,
				OrderID:   orderId,
				UserID:    loginUserId,
				Action:    int32(models.OrderRelationActionClose), // 关闭
				Status:    int32(models.StatusYesNoYes),
				CreatedAt: timeNow,
				UpdatedAt: timeNow,
				DeletedAt: 0,
			},
		}
	}

	logEntity := &order.OrderLogEntity{
		Old: &order.OrderLogEntityItem{
			Status:                order.StatusOrder(orderWithOrderPay.Order.Status),
			StatusReview:          order.StatusOrderReview(orderWithOrderPay.Order.StatusReview),
			StatusWorkflow:        order.StatusOrderWorkflow(orderWithOrderPay.Order.StatusWorkflow),
			StatusPrev:            order.StatusOrder(orderWithOrderPay.Order.StatusPrev),
			StatusReviewPrev:      order.StatusOrderReview(orderWithOrderPay.Order.StatusReviewPrev),
			StatusPayDeposit:      order.StatusOrderPay(orderWithOrderPay.Order.StatusPayDeposit),
			StatusPayFirst:        order.StatusOrderPay(orderWithOrderPay.Order.StatusPayFirst),
			StatusPayFinal:        order.StatusOrderPay(orderWithOrderPay.Order.StatusPayFinal),
			StatusPayDisbursement: order.StatusOrderPay(orderWithOrderPay.Order.StatusPayDisbursement),
		},
		New: &order.OrderLogEntityItem{
			Status:                order.StatusOrder(lStatusMap[model.OrderColumns.Status()]),
			StatusReview:          order.StatusOrderReview(lStatusMap[model.OrderColumns.StatusReview()]),
			StatusWorkflow:        order.StatusOrderWorkflow(lStatusMap[model.OrderColumns.StatusWorkflow()]),
			StatusPrev:            order.StatusOrder(lStatusMap[model.OrderColumns.StatusPrev()]),
			StatusReviewPrev:      order.StatusOrderReview(lStatusMap[model.OrderColumns.StatusReviewPrev()]),
			StatusPayDeposit:      order.StatusOrderPay(lStatusMap[model.OrderColumns.StatusPayDeposit()]),
			StatusPayFirst:        order.StatusOrderPay(lStatusMap[model.OrderColumns.StatusPayFirst()]),
			StatusPayFinal:        order.StatusOrderPay(lStatusMap[model.OrderColumns.StatusPayFinal()]),
			StatusPayDisbursement: order.StatusOrderPay(lStatusMap[model.OrderColumns.StatusPayDisbursement()]),
		},
	}
	logByte, _ := json.Marshal(logEntity)

	operationLogMods := make([]*model.OrderOperationLog, 0)
	// 订单操作日志 status
	if lStatusMap[model.OrderColumns.Status()] != orderWithOrderPay.Order.Status {
		operationLogMod := model.OrderOperationLog{
			ID:        0,
			OrderID:   req.GetId(),
			OrderNo:   orderWithOrderPay.Order.OrderNo,
			Type:      int32(order.OrderOperationType_ORDER_OPERATION_TYPE_ORDER),
			Log:       string(logByte),
			CreatedBy: loginUserId,
			CreatedAt: timeNow,
		}
		operationLogMods = append(operationLogMods, &operationLogMod)
	}

	// 订单操作日志 status_review
	if lStatusMap[model.OrderColumns.StatusReview()] != orderWithOrderPay.Order.StatusReview {
		// 大状态变更为 支付成功 || 关闭订单时，不记录小状态变化
		// 变跟后的状态，不是支付成功且不是关闭订单时，才记录小状态的变化日志
		// 避免出现，小状态变更为支付成功，关闭订单日志
		if lStatusMap[model.OrderColumns.Status()] != int32(StatusOrderSuccess) && lStatusMap[model.OrderColumns.Status()] != int32(StatusOrderClose) {
			operationLogMod := model.OrderOperationLog{
				ID:        0,
				OrderID:   req.GetId(),
				OrderNo:   orderWithOrderPay.Order.OrderNo,
				Type:      int32(order.OrderOperationType_ORDER_OPERATION_TYPE_REVIEW),
				Log:       string(logByte),
				CreatedBy: loginUserId,
				CreatedAt: timeNow,
			}
			operationLogMods = append(operationLogMods, &operationLogMod)
		}
	}

	// 支款审核通过 待打卡 -> 支款完成
	if orderWithOrderPay.Order.Status == int32(StatusOrderDisbursement) &&
		orderWithOrderPay.Order.StatusReview == int32(StatusReviewPass) {
		if lStatusMap[model.OrderColumns.StatusPayDisbursement()] != orderWithOrderPay.Order.StatusPayDisbursement {
			operationLogMod := model.OrderOperationLog{
				ID:        0,
				OrderID:   req.GetId(),
				OrderNo:   orderWithOrderPay.Order.OrderNo,
				Type:      int32(order.OrderOperationType_ORDER_OPERATION_TYPE_REVIEW),
				Log:       string(logByte),
				CreatedBy: loginUserId,
				CreatedAt: timeNow,
			}
			operationLogMods = append(operationLogMods, &operationLogMod)
		}
	}

	err = (&models.OrderTransaction{}).UpdateOrderStatus(ctx, orderId, updatesMap, operationLogMods, updatesOrderPay, orderPayUpdatesMap, relationMods)
	if err != nil {
		logger.CtxErrorf(ctx, "%s UpdateOrderStatus db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	return
}

func (o *OrderService) GetOrderStatic(ctx context.Context, req *order.GetOrderStaticReq) (resp *order.GetOrderStaticRsp, err error) {
	funcName := getFuncName(1)

	deposit := &order.OrderReviewStatic{}
	first := &order.OrderReviewStatic{}
	final := &order.OrderReviewStatic{}
	success := &order.OrderReviewStatic{}
	disbursement := &order.OrderReviewStatic{}
	closed := &order.OrderReviewStatic{}

	resp = &order.GetOrderStaticRsp{
		Deposit:                deposit,
		First:                  first,
		Final:                  final,
		Success:                success,
		Disbursement:           disbursement,
		Close:                  closed,
		DisbursementPaid:       0,
		FinalWorkflowCompleted: 0,
		FinalWorkflowProcess:   0,
		Base:                   coderror.MakeSuccessBaseRsp(),
	}

	mods, err := (&models.Order{}).GetOrderStatic(ctx, req.GetCustomerId())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderStatic db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	disbursementPaid, err := (&models.Order{}).GetOrderDisbursementCount(ctx, req.GetCustomerId())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderDisbursementCount db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	finalWorkflowProcess, err := (&models.Order{}).GetFinalWorkflowProcess(ctx, req.GetCustomerId())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetFinalWorkflowProcess db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	finalWorkflowCompleted, err := (&models.Order{}).GetFinalWorkflowCompleted(ctx, req.GetCustomerId())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetFinalWorkflowCompleted db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	// 创建状态映射表，避免重复代码
	statusMap := map[models.StatusOrder]*order.OrderReviewStatic{
		models.StatusOrderDeposit:      deposit,
		models.StatusOrderFirst:        first,
		models.StatusOrderFinal:        final,
		models.StatusOrderSuccess:      success,
		models.StatusOrderDisbursement: disbursement,
		models.StatusOrderClose:        closed,
	}

	// 辅助函数：根据审核状态更新统计数据
	updateReviewStatus := func(stat *order.OrderReviewStatic, reviewStatus models.StatusReview, num int64) {
		switch reviewStatus {
		case models.StatusReviewPass:
			stat.Pass = num
		case models.StatusReviewDraft:
			stat.Draft = num
		case models.StatusReviewDraftAudit:
			stat.DraftAudit = num
		case models.StatusReviewRejectAudit:
			stat.RejectAudit = num
		case models.StatusReviewReject:
			stat.Reject = num
		}
	}

	for _, mod := range mods {
		if stat, exists := statusMap[mod.Status]; exists {
			stat.All += mod.Num
			updateReviewStatus(stat, mod.StatusReview, mod.Num)
		}
	}

	resp.Deposit = deposit
	resp.First = first
	resp.Final = final
	resp.Success = success
	resp.Disbursement = disbursement
	resp.Close = closed
	resp.DisbursementPaid = disbursementPaid
	resp.FinalWorkflowCompleted = finalWorkflowCompleted
	resp.FinalWorkflowProcess = finalWorkflowProcess

	return
}

func (o *OrderService) GetOrderStaticEmployee(ctx context.Context, req *order.GetOrderStaticEmployeeReq) (resp *order.GetOrderStaticEmployeeRsp, err error) {
	funcName := getFuncName(1)

	deposit := &order.OrderReviewStatic{}
	first := &order.OrderReviewStatic{}
	final := &order.OrderReviewStatic{}
	success := &order.OrderReviewStatic{}
	disbursement := &order.OrderReviewStatic{}
	closed := &order.OrderReviewStatic{}

	resp = &order.GetOrderStaticEmployeeRsp{
		Deposit:                deposit,
		First:                  first,
		Final:                  final,
		Success:                success,
		Disbursement:           disbursement,
		Close:                  closed,
		DisbursementPaid:       0,
		FinalWorkflowCompleted: 0,
		FinalWorkflowProcess:   0,
		Base:                   coderror.MakeSuccessBaseRsp(),
	}

	mods, err := (&models.Order{}).GetOrderStaticEmployee(ctx, req.GetCustomerIds(), req.GetUpdaterIds())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderStaticEmployee db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	disbursementPaid, err := (&models.Order{}).GetOrderDisbursementCountEmployee(ctx, req.GetCustomerIds(), req.GetUpdaterIds())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderDisbursementCountEmployee db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	finalWorkflowProcess, err := (&models.Order{}).GetFinalWorkflowProcessEmployee(ctx, req.GetCustomerIds(), req.GetUpdaterIds())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetFinalWorkflowProcessEmployee db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	finalWorkflowCompleted, err := (&models.Order{}).GetFinalWorkflowCompletedEmployee(ctx, req.GetCustomerIds(), req.GetUpdaterIds())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetFinalWorkflowCompletedEmployee db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	// 创建状态映射表，避免重复代码
	statusMap := map[models.StatusOrder]*order.OrderReviewStatic{
		models.StatusOrderDeposit:      deposit,
		models.StatusOrderFirst:        first,
		models.StatusOrderFinal:        final,
		models.StatusOrderSuccess:      success,
		models.StatusOrderDisbursement: disbursement,
		models.StatusOrderClose:        closed,
	}

	// 辅助函数：根据审核状态更新统计数据
	updateReviewStatus := func(stat *order.OrderReviewStatic, reviewStatus models.StatusReview, num int64) {
		switch reviewStatus {
		case models.StatusReviewPass:
			stat.Pass = num
		case models.StatusReviewDraft:
			stat.Draft = num
		case models.StatusReviewDraftAudit:
			stat.DraftAudit = num
		case models.StatusReviewRejectAudit:
			stat.RejectAudit = num
		case models.StatusReviewReject:
			stat.Reject = num
		}
	}

	for _, mod := range mods {
		if stat, exists := statusMap[mod.Status]; exists {
			stat.All += mod.Num
			updateReviewStatus(stat, mod.StatusReview, mod.Num)
		}
	}

	resp.Deposit = deposit
	resp.First = first
	resp.Final = final
	resp.Success = success
	resp.Disbursement = disbursement
	resp.Close = closed
	resp.DisbursementPaid = disbursementPaid
	resp.FinalWorkflowCompleted = finalWorkflowCompleted
	resp.FinalWorkflowProcess = finalWorkflowProcess

	return
}

// GetCurrencyList implements the ServiceImpl interface.
func (o *OrderService) GetCurrencyList(ctx context.Context, req *order.GetCurrencyListReq) (resp *order.GetCurrencyListRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetCurrencyListRsp{
		Items: make([]*order.CurrencyEntity, 0),
		Base:  coderror.MakeSuccessBaseRsp(),
	}

	mods, err := (&models.Currency{}).GetCurrencyList(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetCurrencyList db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	items := make([]*order.CurrencyEntity, 0, len(mods))
	for _, mod := range mods {
		items = append(items, currencyModToPb(mod))
	}
	resp.Items = items

	return
}

// GetScholarshipToBeDistributed implements the ServiceImpl interface.
func (o *OrderService) GetScholarshipToBeDistributed(ctx context.Context, req *order.GetScholarshipToBeDistributedReq) (resp *order.GetScholarshipToBeDistributedRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetScholarshipToBeDistributedRsp{
		Items:         make([]*order.ScholarshipToBeDistributed, 0),
		Total:         0,
		AmountTotal:   "0",
		CurrencyTotal: "",
		Base:          coderror.MakeSuccessBaseRsp(),
	}

	mods, total, err := (&models.Order{}).GetScholarshipToBeDistributed(ctx, req.GetCustomerId(), req.GetPageNum(), req.GetPageSize())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetScholarshipToBeDistributed db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	amountTotal, currencyTotal, err := (&models.Order{}).GetScholarshipToBeDistributedAmount(ctx, req.GetCustomerId())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetScholarshipToBeDistributedAmount db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	items := make([]*order.ScholarshipToBeDistributed, 0)
	for _, mod := range mods {
		items = append(items, scholarshipModToPb(mod))
	}

	resp.Items = items
	resp.Total = total
	resp.AmountTotal = amountTotal
	resp.CurrencyTotal = currencyTotal

	return
}

// GetScholarshipToBeDistributedAmount implements the ServiceImpl interface.
func (o *OrderService) GetScholarshipToBeDistributedAmount(ctx context.Context, req *order.GetScholarshipToBeDistributedAmountReq) (resp *order.GetScholarshipToBeDistributedAmountRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetScholarshipToBeDistributedAmountRsp{
		AmountTotal:   "0",
		CurrencyTotal: "",
		Base:          coderror.MakeSuccessBaseRsp(),
	}

	amountTotal, currencyTotal, err := (&models.Order{}).GetScholarshipToBeDistributedAmount(ctx, req.GetCustomerId())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetScholarshipToBeDistributedAmount db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	resp.AmountTotal = amountTotal
	resp.CurrencyTotal = currencyTotal

	return
}

// BatchUpdateUpdaterId implements the ServiceImpl interface.
func (o *OrderService) BatchUpdateUpdaterId(ctx context.Context, req *order.BatchUpdateUpdaterIdReq) (resp *order.BatchUpdateUpdaterIdRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.BatchUpdateUpdaterIdRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	if req.GetCustomerId() <= 0 || len(req.GetItems()) == 0 {
		logger.CtxErrorf(ctx, "%s Asset-Transfer: invalid params err: %+v, customer_id: %+v, items: %+v", funcName, i18n.InvalidParams, req.GetCustomerId(), req.GetItems())
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return
	}

	err = (&models.OrderTransaction{}).BatchUpdateUpdaterId(ctx, req.GetCustomerId(), updateUpdaterIdPbToMod(req.GetItems()))
	if err != nil {
		logger.CtxErrorf(ctx, "%s Asset-Transfer: db err: %+v, customer_id: %+v, items: %+v", funcName, err, req.GetCustomerId(), req.GetItems())
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	// 资产转移
	logger.CtxInfof(ctx, "%s Asset-Transfer: customer_id: %+v, items: %+v", funcName, req.GetCustomerId(), req.GetItems())

	return
}

// BatchUpdateWorkflowStatus implements the ServiceImpl interface.
func (o *OrderService) BatchUpdateWorkflowStatus(ctx context.Context, req *order.BatchUpdateWorkflowStatusReq) (resp *order.BatchUpdateWorkflowStatusRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.BatchUpdateWorkflowStatusRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	err = (&models.OrderTransaction{}).BatchUpdateWorkflowStatus(ctx, req.GetOrderIds(), models.StatusOrderWorkflow(req.GetWorkflowStatus()))
	if err != nil {
		logger.CtxErrorf(ctx, "%s BatchUpdateWorkflowStatus db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	return
}

// GetRefundHighRiskCustomers implements the ServiceImpl interface.
// 用户退款高危标签更新
func (o *OrderService) GetRefundHighRiskCustomers(ctx context.Context, req *order.GetRefundHighRiskCustomersReq) (resp *order.GetRefundHighRiskCustomersRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetRefundHighRiskCustomersRsp{
		AddIds:    make([]int64, 0),
		RemoveIds: make([]int64, 0),
		Base:      coderror.MakeSuccessBaseRsp(),
	}

	// 所有符合 refund_high_risk 标签的用户
	addIds, err := (&models.Order{}).GetRefundHighRiskCustomers(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetRefundHighRiskCustomer db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	// 获取所有已打上 refund_high_risk 标签的用户
	taggedIds, err := (&models.Order{}).GetTaggedRefundHighRiskCustomers(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetRefundHighRiskCustomer db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	// 需要打的 = 符合的 - 已经打上的
	resp.AddIds = models.FindDifference(addIds, taggedIds)
	// 需要移除的 = 已经打上的 - 符合的
	resp.RemoveIds = models.FindDifference(taggedIds, addIds)

	return
}

// GetWorkflowCompleteOrders implements the ServiceImpl interface.
func (o *OrderService) GetWorkflowCompleteOrders(ctx context.Context, req *order.GetWorkflowCompleteOrdersReq) (resp *order.GetWorkflowCompleteOrdersRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetWorkflowCompleteOrdersRsp{
		AddIds:    make([]int64, 0),
		RemoveIds: make([]int64, 0),
		Base:      coderror.MakeSuccessBaseRsp(),
	}

	// 根据工单获取
	addIds, err := (&models.Order{}).GetWorkflowCompleteOrders(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetWorkflowCompleteOrders db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	// 根据订单获取
	addedIds, err := (&models.Order{}).GetOrderWorkFlowComplete(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetWorkflowCompleteOrders db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	// 需要更新完成状态的 = 所有符合的订单ID - 已经添加的订单ID
	resp.AddIds = models.FindDifference(addIds, addedIds)
	// 需要移除完成状态的 = 已经添加的订单ID - 待添加的订单ID
	resp.RemoveIds = models.FindDifference(addedIds, addIds)

	return
}

// GetLatestOrderOperationLogByOrderId implements the ServiceImpl interface.
func (o *OrderService) GetLatestOrderOperationLogByOrderId(ctx context.Context, req *order.GetLatestOrderOperationLogByOrderIdReq) (resp *order.GetLatestOrderOperationLogByOrderIdRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetLatestOrderOperationLogByOrderIdRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	orderOperationLog, err := (&models.OrderOperationLog{}).GetLatestOrderOperationLogByOrderId(ctx, req.GetOrderId())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetLatestOrderOperationLogByOrderId db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	if orderOperationLog == nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_OrderNotFound)
		return
	}

	resp.OrderOperationLog = orderOperationLogModToPb(orderOperationLog)

	return
}

// GetLatestOrderInfoByCustomerIds implements the ServiceImpl interface.
func (o *OrderService) GetLatestOrderInfoByCustomerIds(ctx context.Context, req *order.GetLatestOrderInfoByCustomerIdsReq) (resp *order.GetLatestOrderInfoByCustomerIdsRsp, err error) {

	funcName := getFuncName(1)

	resp = &order.GetLatestOrderInfoByCustomerIdsRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	orderIds, err := (&models.Order{}).GetLatestOrderIdsByCustomerIds(ctx, req.GetCustomerId())
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetLatestOrderInfoByCustomerIds db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	if len(orderIds) == 0 {
		return
	}

	orderList, err := (&models.Order{}).GetOrderInfoByIds(ctx, orderIds, []string{})
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetLatestOrderInfoByCustomerIds db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	orderGoodsList, err := (&models.OrderGoods{}).GetMinimalOrderGoods(ctx, orderIds)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetLatestOrderInfoByCustomerIds db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	orderGoodsMap := make(map[int64]*order.OrderGoodsEntity)
	for _, v := range orderGoodsList {
		orderGoodsMap[v.OrderID] = orderGoodsModToPb(v)
	}

	orderItems := make([]*order.SingleOrderInfoItem, 0, len(orderList))
	for _, orderItem := range orderList {
		orderInfo := orderModToPb(orderItem.Order)
		orderPay := orderPayModToPb(orderItem.OrderPay, models.DisbursementType(orderItem.Order.DisbursementType))
		orderGoods := &order.OrderGoodsEntity{}
		if orderGoodsMap[orderItem.Order.ID] != nil {
			orderGoods = orderGoodsMap[orderItem.Order.ID]
		}

		orderItems = append(orderItems, &order.SingleOrderInfoItem{
			Order:      orderInfo,
			OrderPay:   orderPay,
			OrderGoods: orderGoods,
		})
	}

	resp.OrderInfo = orderItems

	return
}

// GetRedLineRiskCustomers implements the ServiceImpl interface.
func (o *OrderService) GetRedLineRiskCustomers(ctx context.Context, req *order.GetRedLineRiskCustomersReq) (resp *order.GetRedLineRiskCustomersRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetRedLineRiskCustomersRsp{
		AddIds:    make([]int64, 0),
		RemoveIds: make([]int64, 0),
		Base:      coderror.MakeSuccessBaseRsp(),
	}

	// 获取所有符合 redLine 标签的用户集合
	addIds, err := (&models.Order{}).GetRedLineRiskCustomers(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetRefundHighRiskCustomer db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	// 获取所有已打上 redLine 标签的用户集合
	taggedIds, err := (&models.Order{}).GetTaggedRedLineRiskCustomers(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetRefundHighRiskCustomer db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	// 需要加标签的 = 所有符合标签 - 已经加标签的
	resp.AddIds = models.FindDifference(addIds, taggedIds)
	// 需要移除标签的 = 已经加标签的 - 待加标签的
	resp.RemoveIds = models.FindDifference(taggedIds, addIds)

	return
}

// GetOldNewCustomers implements the ServiceImpl interface.
func (o *OrderService) GetOldNewCustomers(ctx context.Context, req *order.GetOldNewCustomersReq) (resp *order.GetOldNewCustomersRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetOldNewCustomersRsp{
		Old:  make([]int64, 0),
		New:  make([]int64, 0),
		Base: coderror.MakeSuccessBaseRsp(),
	}

	//// 根据订单判断需要打新用户标签的用户
	//newIds, err := (&models.Order{}).GetNewTagCustomers(ctx)
	//if err != nil {
	//	logger.CtxErrorf(ctx, "%s GetNewTagCustomers db err: %+v", funcName, err)
	//	resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
	//
	//	return
	//}

	// 根据订单判断需要打老用户标签的用户
	oldIds, err := (&models.Order{}).GetOldTagCustomers(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOldTagCustomers db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	// 已打上老用户标签的用户
	taggedOldIds, err := (&models.Order{}).GetTaggedOldCustomers(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOldTagCustomers db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	// 待打标签的 = 所有需要打标签的 - 已打上标签的
	resp.Old = models.FindDifference(oldIds, taggedOldIds)
	//resp.New = newIds

	return
}

// GetOrderCountByCustomerId implements the ServiceImpl interface.
func (o *OrderService) GetOrderCountByCustomerId(ctx context.Context, req *order.GetOrderCountByCustomerIdReq) (resp *order.GetOrderCountByCustomerIdRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetOrderCountByCustomerIdRsp{
		Total: 0,
		Base:  coderror.MakeSuccessBaseRsp(),
	}

	statusSli := make([]models.StatusOrder, 0, len(req.GetStatus()))
	statusReviewSli := make([]models.StatusReview, 0, len(req.GetStatusReview()))
	if len(req.GetStatus()) > 0 {
		for _, status := range req.GetStatus() {
			statusSli = append(statusSli, models.StatusOrder(status))
		}
	}

	if len(req.GetStatusReview()) > 0 {
		for _, status := range req.GetStatusReview() {
			statusReviewSli = append(statusReviewSli, models.StatusReview(status))
		}
	}

	total, err := (&models.Order{}).GetOrderCountByCustomerId(ctx, req.GetCustomerId(), statusSli, statusReviewSli)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderCountByCustomerId db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	resp.Total = total

	return
}

// ResetCompleteWorkflowStatus implements the ServiceImpl interface.
func (o *OrderService) ResetCompleteWorkflowStatus(ctx context.Context, req *order.ResetCompleteWorkflowStatusReq) (resp *order.ResetCompleteWorkflowStatusRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.ResetCompleteWorkflowStatusRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	err = (&models.Order{}).ResetCompleteWorkflowStatus(ctx, req.GetOrderIds())
	if err != nil {
		logger.CtxErrorf(ctx, "%s ResetCompleteWorkflowStatus db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	return
}

// GetOrderCountByCustomerIds implements the ServiceImpl interface.
func (o *OrderService) GetOrderCountByCustomerIds(ctx context.Context, req *order.GetOrderCountByCustomerIdsReq) (resp *order.GetOrderCountByCustomerIdsRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetOrderCountByCustomerIdsRsp{
		Items: make([]*order.GetOrderCountByCustomerIdsItem, 0),
		Base:  coderror.MakeSuccessBaseRsp(),
	}

	statusSli := make([]models.StatusOrder, 0, len(req.GetStatus()))
	statusReviewSli := make([]models.StatusReview, 0, len(req.GetStatusReview()))
	if len(req.GetStatus()) > 0 {
		for _, status := range req.GetStatus() {
			statusSli = append(statusSli, models.StatusOrder(status))
		}
	}

	if len(req.GetStatusReview()) > 0 {
		for _, status := range req.GetStatusReview() {
			statusReviewSli = append(statusReviewSli, models.StatusReview(status))
		}
	}

	itemSli, err := (&models.Order{}).GetOrderCountByCustomerIds(ctx, req.GetCustomerIds(), statusSli, statusReviewSli)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderCountByCustomerId db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	items := make([]*order.GetOrderCountByCustomerIdsItem, 0, len(itemSli))
	for _, item := range itemSli {
		items = append(items, &order.GetOrderCountByCustomerIdsItem{
			CustomerId: item.CustomerID,
			Total:      item.Count,
		})
	}

	resp.Items = items

	return
}

// UpdateOrderRelation implements the ServiceImpl interface.
func (o *OrderService) UpdateOrderRelation(ctx context.Context, req *order.UpdateOrderRelationReq) (resp *order.UpdateOrderRelationRsp, err error) {
	funcName := getFuncName(1)
	// 开启事务
	err = global.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if _, err := models.UpdateOrderRelation(ctx, tx, funcName, req.OrderId, int32(req.Action), req.RelationIds); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &order.UpdateOrderRelationRsp{}, nil

}

// FundRejectUpdateOrderStatus 收款单驳回后更新订单状态
// 注意：调用此方法前应该已经通过 GetOrderStatusByOrderId 验证了权限
// （status, status_review, installment_type）
// 1、已下定金-待下单(1,1, -)      -> 已下订单-审核驳回   （1，5）
// 2、尾款待支付-服务未完成(3,2, -) -> 支付待确认-审核驳回 （2，5）
// 3、尾款待支付-待提交(3,2, -)    -> 支付待确认-审核驳回 （2，5）
// 4、支付成功(4, -, 1)           -> 支付待确认-审核驳回 （2，5）仅一次性付款（全款）可以驳回
func (o *OrderService) FundRejectUpdateOrderStatus(ctx context.Context, req *order.FundRejectUpdateOrderStatusReq) (*order.FundRejectUpdateOrderStatusRsp, error) {
	// 1. 参数验证
	if req.OrderId <= 0 {
		logger.CtxErrorf(ctx, "订单ID不能为空: %d", req.OrderId)
		return &order.FundRejectUpdateOrderStatusRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams),
		}, nil
	}

	// 2. 获取订单详细信息
	orderInfo, err := (&models.Order{}).GetOrderById(ctx, req.OrderId)
	if err != nil {
		logger.CtxErrorf(ctx, "获取订单信息失败: order_id=%d, error=%v", req.OrderId, err)
		return nil, err
	}

	// 3. 根据当前订单状态确定驳回后的状态
	var newStatus order.StatusOrder
	var newStatusReview order.StatusOrderReview

	currentStatus := models.StatusOrder(orderInfo.Order.Status)
	currentStatusReview := models.StatusReview(orderInfo.Order.StatusReview)
	currentInstallmentType := models.InstallmentType(orderInfo.Order.InstallmentType)

	switch {
	case currentStatus == models.StatusOrderDeposit && currentStatusReview == models.StatusReviewPass:
		// 待下单状态 -> 已下定金 + 审核驳回
		newStatus = order.StatusOrder_STATUS_ORDER_DEPOSIT
		newStatusReview = order.StatusOrderReview_STATUS_REVIEW_REJECT

	case currentStatus == models.StatusOrderFinal && currentStatusReview == models.StatusReviewDraft:
		// 服务未完成/待提交状态 -> 支付待确认 + 审核驳回
		newStatus = order.StatusOrder_STATUS_ORDER_FIRST
		newStatusReview = order.StatusOrderReview_STATUS_REVIEW_REJECT

	case currentStatus == models.StatusOrderSuccess && currentInstallmentType == models.InstallmentTypeFullPayment:
		// 支付成功状态 -> 支付待确认 + 审核驳回
		newStatus = order.StatusOrder_STATUS_ORDER_FIRST
		newStatusReview = order.StatusOrderReview_STATUS_REVIEW_REJECT

	default:
		logger.CtxErrorf(ctx, "不支持的订单状态进行驳回: order_id=%d, status=%d, status_review=%d, installment_type=%d",
			req.OrderId, currentStatus, currentStatusReview, currentInstallmentType)
		return &order.FundRejectUpdateOrderStatusRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_OrderStatusNotPermission),
		}, nil
	}

	// 4. 更新订单状态
	orderTransaction := &models.OrderTransaction{}
	updatesMap := map[string]any{
		"status":        int32(newStatus),
		"status_review": int32(newStatusReview),
		"updated_at":    time.Now(),
	}

	logEntity := &order.OrderLogEntity{
		Old: &order.OrderLogEntityItem{
			Status:                order.StatusOrder(currentStatus),
			StatusReview:          order.StatusOrderReview(currentStatusReview),
			StatusWorkflow:        order.StatusOrderWorkflow(orderInfo.Order.StatusWorkflow),
			StatusPrev:            order.StatusOrder(orderInfo.Order.StatusPrev),
			StatusReviewPrev:      order.StatusOrderReview(orderInfo.Order.StatusReviewPrev),
			StatusPayDeposit:      order.StatusOrderPay(orderInfo.Order.StatusPayDeposit),
			StatusPayFirst:        order.StatusOrderPay(orderInfo.Order.StatusPayFirst),
			StatusPayFinal:        order.StatusOrderPay(orderInfo.Order.StatusPayFinal),
			StatusPayDisbursement: order.StatusOrderPay(orderInfo.Order.StatusPayDisbursement),
		},
		New: &order.OrderLogEntityItem{
			Status:                newStatus,
			StatusReview:          newStatusReview,
			StatusWorkflow:        order.StatusOrderWorkflow(orderInfo.Order.StatusWorkflow),
			StatusPrev:            order.StatusOrder(orderInfo.Order.StatusPrev),
			StatusReviewPrev:      order.StatusOrderReview(orderInfo.Order.StatusReviewPrev),
			StatusPayDeposit:      order.StatusOrderPay(orderInfo.Order.StatusPayDeposit),
			StatusPayFirst:        order.StatusOrderPay(orderInfo.Order.StatusPayFirst),
			StatusPayFinal:        order.StatusOrderPay(orderInfo.Order.StatusPayFinal),
			StatusPayDisbursement: order.StatusOrderPay(orderInfo.Order.StatusPayDisbursement),
		},
	}

	logByte, _ := json.Marshal(logEntity)
	operationLogMods := make([]*model.OrderOperationLog, 0)
	operationLogMod := model.OrderOperationLog{}
	timeNow := time.Now()

	// 已下定金 -> status_review 小状态变更
	// 尾款待支付 -> status/status_review 大/小状态变更
	// 支付成功 -> status/status_review 大/小状态变更

	// 创建操作日志
	switch models.StatusOrder(orderInfo.Order.Status) {
	case models.StatusOrderFinal:
		// status 变更记录
		operationLogMod = model.OrderOperationLog{
			ID:        0,
			OrderID:   req.OrderId,
			OrderNo:   orderInfo.Order.OrderNo,
			Type:      int32(order.OrderOperationType_ORDER_OPERATION_TYPE_ORDER),
			Log:       string(logByte),
			CreatedBy: req.UpdatedBy,
			CreatedAt: timeNow,
		}
		operationLogMods = append(operationLogMods, &operationLogMod)
	case models.StatusOrderSuccess:
		// status
		operationLogMod = model.OrderOperationLog{
			ID:        0,
			OrderID:   req.OrderId,
			OrderNo:   orderInfo.Order.OrderNo,
			Type:      int32(order.OrderOperationType_ORDER_OPERATION_TYPE_ORDER),
			Log:       string(logByte),
			CreatedBy: req.UpdatedBy,
			CreatedAt: timeNow,
		}
		operationLogMods = append(operationLogMods, &operationLogMod)
	}

	// status_review 变更记录
	operationLogMod = model.OrderOperationLog{
		ID:        0,
		OrderID:   req.OrderId,
		OrderNo:   orderInfo.Order.OrderNo,
		Type:      int32(order.OrderOperationType_ORDER_OPERATION_TYPE_REVIEW),
		Log:       string(logByte),
		CreatedBy: req.UpdatedBy,
		CreatedAt: timeNow,
	}
	operationLogMods = append(operationLogMods, &operationLogMod)

	err = orderTransaction.UpdateOrderStatus(ctx, req.OrderId, updatesMap, operationLogMods, models.StatusYesNoNo, nil, nil)
	if err != nil {
		logger.CtxErrorf(ctx, "更新订单状态失败: order_id=%d, error=%v", req.OrderId, err)
		return nil, err
	}

	logger.CtxInfof(ctx, "收款单驳回订单状态更新成功: order_id=%d, fund_type=%d, new_status=%d, new_status_review=%d",
		req.OrderId, req.FundType, newStatus, newStatusReview)

	return &order.FundRejectUpdateOrderStatusRsp{
		OrderId:         req.OrderId,
		NewStatus:       int32(newStatus),
		NewStatusReview: int32(newStatusReview),
		Base:            coderror.MakeBaseRsp(ctx, errno.Errno_SUCCESS),
	}, nil
}
