package services

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/order/internal/models"
)

// GetOrderWorkflowLog implements the ServiceImpl interface.
func (o *OrderService) GetOrderWorkflowLog(ctx context.Context, req *order.GetOrderWorkflowLogReq) (resp *order.GetOrderWorkflowLogRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetOrderWorkflowLogRsp{
		Items: make([]*order.OrderWorkflowLogEntity, 0),
		Total: 0,
		Base:  coderror.MakeSuccessBaseRsp(),
	}

	query := &models.GetOrderWorkflowLogList{
		PageNum:    req.GetPageNum(),
		PageSize:   req.GetPageSize(),
		UpdaterIds: req.GetUpdaterIds(),
		OrderBy:    getOrderBy(req.GetOrderBy()),
	}
	mods, total, err := (&models.Order{}).GetOrderWorkflowLogList(ctx, query)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxWarnf(ctx, "%s GetOrderWorkflowLogList err: %v, query: %v", funcName, err, query)
		return resp, nil
	}
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetOrderWorkflowLogList db err: %v, query: %v", funcName, err, query)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}
	resp.Total = total

	lens := len(mods)
	if lens == 0 {
		return
	}

	items := make([]*order.OrderWorkflowLogEntity, 0, lens)
	for _, mod := range mods {
		items = append(items, orderWorkflowLogModToPb(mod))
	}
	resp.Items = items

	return
}

// CloseOrderWorkflowLog implements the ServiceImpl interface.
func (o *OrderService) CloseOrderWorkflowLog(ctx context.Context, req *order.CloseOrderWorkflowLogReq) (resp *order.CloseOrderWorkflowLogRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.CloseOrderWorkflowLogRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	err = (&models.Order{}).CloseOrderWorkflowLog(ctx, req.GetClosedBy(), req.GetIds())
	if err != nil {
		logger.CtxErrorf(ctx, "%s CloseOrderWorkflowLog db err: %v, GetClosedBy: %v, GetIds: %v", funcName, err, req.GetClosedBy(), req.GetIds())
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	return
}
