package services

import (
	"context"
	"time"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/order/internal/models"

	"github.com/shopspring/decimal"
)

// convertSalesDataToFinancialStat 将销售数据转换为 FinancialStat 结构
func convertSalesDataToFinancialStat(data *models.StaticSalesData) *order.FinancialStat {
	if data == nil {
		return &order.FinancialStat{}
	}

	return &order.FinancialStat{
		CurrentPeriodCount: int64(data.Total),
	}
}

// calculateAmountPercentageChange 计算金额变化百分比
func calculateAmountPercentageChange(currentAmount, previousAmount string) float64 {
	currentAmountDecimal, _ := decimal.NewFromString(currentAmount)
	previousAmountDecimal, _ := decimal.NewFromString(previousAmount)

	if previousAmountDecimal.IsZero() {
		if currentAmountDecimal.IsZero() {
			return 0
		}
		return 100 // 从0增长到有数据，视为100%增长
	}

	changeAmount := currentAmountDecimal.Sub(previousAmountDecimal)
	percentageChange, _ := changeAmount.Div(previousAmountDecimal).Mul(decimal.NewFromInt(100)).Float64()
	return percentageChange
}

// GetSalesCompareData implements the ServiceImpl interface.
// 获取销售对比数据，支持环比分析
func (o *OrderService) GetSalesCompareData(ctx context.Context, req *order.GetSalesCompareDataReq) (resp *order.GetSalesCompareDataRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.GetSalesCompareDataRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	// 获取当前期间时间范围
	currentPeriodStart := time.UnixMilli(req.GetStart())
	currentPeriodEnd := time.UnixMilli(req.GetEnd())

	// 计算时间差，用于获取上一期间（环比）
	duration := currentPeriodEnd.Sub(currentPeriodStart)
	previousPeriodStart := currentPeriodStart.Add(-duration)
	previousPeriodEnd := currentPeriodStart.Add(-time.Nanosecond)

	// 获取当前期间数据
	currentItems, err := (&models.Order{}).GetSalesStaticByUidDayStartEnd(ctx, req.GetUid(), currentPeriodStart, currentPeriodEnd)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetSalesStaticByUidDayStartEnd current period db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	// 获取上一期间数据（环比）
	previousItems, err := (&models.Order{}).GetSalesStaticByUidDayStartEnd(ctx, req.GetUid(), previousPeriodStart, previousPeriodEnd)
	if err != nil {
		logger.CtxErrorf(ctx, "%s GetSalesStaticByUidDayStartEnd previous period db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	// 将数据转换为map便于查找
	currentDataMap := make(map[int32]*models.StaticSalesData)
	for i := range currentItems {
		currentDataMap[currentItems[i].Cate] = &currentItems[i]
	}

	previousDataMap := make(map[int32]*models.StaticSalesData)
	for i := range previousItems {
		previousDataMap[previousItems[i].Cate] = &previousItems[i]
	}

	// 计算新增订单统计
	currentNewStat := convertSalesDataToFinancialStat(currentDataMap[models.CateNew])
	previousNewStat := convertSalesDataToFinancialStat(previousDataMap[models.CateNew])
	newOrderStat := calculateFinancialStat(currentNewStat, previousNewStat)
	resp.NumNew = &order.SalesStatItem{
		CurrentPeriodCount:  newOrderStat.CurrentPeriodCount,
		PreviousPeriodCount: newOrderStat.PreviousPeriodCount,
		PercentageChange:    newOrderStat.PercentageChange,
		IsValid:             newOrderStat.IsValid,
	}

	// 计算审核通过统计
	currentFirstStat := convertSalesDataToFinancialStat(currentDataMap[models.CateFirst])
	previousFirstStat := convertSalesDataToFinancialStat(previousDataMap[models.CateFirst])
	firstPassStat := calculateFinancialStat(currentFirstStat, previousFirstStat)
	resp.NumFirstPass = &order.SalesStatItem{
		CurrentPeriodCount:  firstPassStat.CurrentPeriodCount,
		PreviousPeriodCount: firstPassStat.PreviousPeriodCount,
		PercentageChange:    firstPassStat.PercentageChange,
		IsValid:             firstPassStat.IsValid,
	}

	// 处理金额数据
	currentFirstAmount := "0.00000"
	previousFirstAmount := "0.00000"
	if currentDataMap[models.CateFirst] != nil {
		currentFirstAmount = currentDataMap[models.CateFirst].Amount
	}
	if previousDataMap[models.CateFirst] != nil {
		previousFirstAmount = previousDataMap[models.CateFirst].Amount
	}
	resp.AmountFirstPass = &order.SalesAmountStatItem{
		CurrentPeriodAmount:  currentFirstAmount,
		PreviousPeriodAmount: previousFirstAmount,
		PercentageChange:     calculateAmountPercentageChange(currentFirstAmount, previousFirstAmount),
		IsValid:              previousFirstAmount != "0.00000",
	}

	// 计算申请支款统计
	currentDisbursementStat := convertSalesDataToFinancialStat(currentDataMap[models.CateDisbursement])
	previousDisbursementStat := convertSalesDataToFinancialStat(previousDataMap[models.CateDisbursement])
	disbursementStat := calculateFinancialStat(currentDisbursementStat, previousDisbursementStat)
	resp.NumDisbursementApply = &order.SalesStatItem{
		CurrentPeriodCount:  disbursementStat.CurrentPeriodCount,
		PreviousPeriodCount: disbursementStat.PreviousPeriodCount,
		PercentageChange:    disbursementStat.PercentageChange,
		IsValid:             disbursementStat.IsValid,
	}

	return
}

// InsertYesterdaySalesData implements the ServiceImpl interface.
func (o *OrderService) InsertYesterdaySalesData(ctx context.Context, req *order.InsertYesterdaySalesDataReq) (resp *order.InsertYesterdaySalesDataRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.InsertYesterdaySalesDataRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	err = (&models.Order{}).InsertYesterdaySalesData(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "%s InsertYesterdaySalesData db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)

		return
	}

	return
}

// InsertHistoricalSalesData implements the ServiceImpl interface.
// 历史数据补录-统计指定时间范围的订单销售数据
func (o *OrderService) InsertHistoricalSalesData(ctx context.Context, req *order.InsertHistoricalSalesDataReq) (resp *order.InsertHistoricalSalesDataRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.InsertHistoricalSalesDataRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	// 验证请求参数
	if req.GetStartDate() <= 0 || req.GetEndDate() <= 0 {
		logger.CtxErrorf(ctx, "%s invalid date range: start=%d, end=%d", funcName, req.GetStartDate(), req.GetEndDate())
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return
	}

	// 转换时间戳为time.Time
	startDate := time.UnixMilli(req.GetStartDate())
	endDate := time.UnixMilli(req.GetEndDate())

	// 调用模型层方法补充数据
	err = (&models.Order{}).InsertHistoricalSalesData(ctx, startDate, endDate)
	if err != nil {
		logger.CtxErrorf(ctx, "%s InsertHistoricalSalesData db err: %+v", funcName, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	// 成功完成，返回成功响应
	logger.CtxInfof(ctx, "%s historical sales data insertion completed successfully for range %s to %s", funcName, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

	return
}

// 移除周统计和月统计相关的RPC方法，改为前端传入日期范围进行灵活查询
