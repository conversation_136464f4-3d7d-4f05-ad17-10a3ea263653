package services

import (
	"context"
	"fmt"
	"time"

	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/order/internal/global"
	"uofferv2/server/cmd/order/internal/models"
)

func GetFinancialStats(ctx context.Context, req *order.GetFinancialStatsRequest) (*order.GetFinancialStatsResponse, error) {
	resp := &order.GetFinancialStatsResponse{
		CollectionStats:           &order.CollectionStats{},
		DisbursementApprovalStats: &order.DisbursementApprovalStats{},
		DisbursementPaymentStats:  &order.DisbursementPaymentStats{},
	}

	startDate := time.UnixMilli(req.StartDate)
	endDate := time.UnixMilli(req.EndDate)

	// Get current and previous period stats
	currentCollectionStats, err := getCollectionStats(ctx, startDate, endDate, req.BrandIds, req.AccountIds, req.AccountTypes, req.StaffIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get current collection stats: %w", err)
	}

	previousCollectionStats, err := getCollectionStats(ctx, startDate.AddDate(0, 0, -int(endDate.Sub(startDate).Hours()/24)), startDate, req.BrandIds, req.AccountIds, req.AccountTypes, req.StaffIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get previous collection stats: %w", err)
	}

	resp.CollectionStats.NewPendingReceipts = calculateFinancialStat(currentCollectionStats.NewPendingReceipts, previousCollectionStats.NewPendingReceipts)
	resp.CollectionStats.ApprovedReceipts = calculateFinancialStat(currentCollectionStats.ApprovedReceipts, previousCollectionStats.ApprovedReceipts)
	resp.CollectionStats.RejectedReceipts = calculateFinancialStat(currentCollectionStats.RejectedReceipts, previousCollectionStats.RejectedReceipts)
	resp.CollectionStats.AverageApproveDurationHours = calculateFinancialStat(currentCollectionStats.AverageApproveDurationHours, previousCollectionStats.AverageApproveDurationHours)

	currentDisbursementApprovalStats, err := getDisbursementApprovalStats(ctx, startDate, endDate, req.RefundTypes, req.StaffIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get current disbursement approval stats: %w", err)
	}

	previousDisbursementApprovalStats, err := getDisbursementApprovalStats(ctx, startDate.AddDate(0, 0, -int(endDate.Sub(startDate).Hours()/24)), startDate, req.RefundTypes, req.StaffIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get previous disbursement approval stats: %w", err)
	}

	resp.DisbursementApprovalStats.NewPendingDisbursements = calculateFinancialStat(currentDisbursementApprovalStats.NewPendingDisbursements, previousDisbursementApprovalStats.NewPendingDisbursements)
	resp.DisbursementApprovalStats.ApprovedDisbursements = calculateFinancialStat(currentDisbursementApprovalStats.ApprovedDisbursements, previousDisbursementApprovalStats.ApprovedDisbursements)
	resp.DisbursementApprovalStats.RejectedDisbursements = calculateFinancialStat(currentDisbursementApprovalStats.RejectedDisbursements, previousDisbursementApprovalStats.RejectedDisbursements)
	resp.DisbursementApprovalStats.AverageApproveDurationHours = calculateFinancialStat(currentDisbursementApprovalStats.AverageApproveDurationHours, previousDisbursementApprovalStats.AverageApproveDurationHours)

	currentDisbursementPaymentStats, err := getDisbursementPaymentStats(ctx, startDate, endDate, req.RefundTypes, req.StaffIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get current disbursement payment stats: %w", err)
	}

	previousDisbursementPaymentStats, err := getDisbursementPaymentStats(ctx, startDate.AddDate(0, 0, -int(endDate.Sub(startDate).Hours()/24)), startDate, req.RefundTypes, req.StaffIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get previous disbursement payment stats: %w", err)
	}

	resp.DisbursementPaymentStats.NewPendingPayments = calculateFinancialStat(currentDisbursementPaymentStats.NewPendingPayments, previousDisbursementPaymentStats.NewPendingPayments)
	resp.DisbursementPaymentStats.SuccessfulPayments = calculateFinancialStat(currentDisbursementPaymentStats.SuccessfulPayments, previousDisbursementPaymentStats.SuccessfulPayments)
	resp.DisbursementPaymentStats.RejectedPayments = calculateFinancialStat(currentDisbursementPaymentStats.RejectedPayments, previousDisbursementPaymentStats.RejectedPayments)
	resp.DisbursementPaymentStats.OverduePayments = calculateFinancialStat(currentDisbursementPaymentStats.OverduePayments, previousDisbursementPaymentStats.OverduePayments)
	resp.DisbursementPaymentStats.AveragePaymentDurationHours = calculateFinancialStat(currentDisbursementPaymentStats.AveragePaymentDurationHours, previousDisbursementPaymentStats.AveragePaymentDurationHours)

	return resp, nil
}

func getCollectionStats(ctx context.Context, startDate, endDate time.Time, brandIDs []int64, accountIDs []int64, accountTypes []int32, staffIds []int64) (*order.CollectionStats, error) {
	stats, err := models.GetCollectionStatsFromDB(ctx, global.DB, startDate, endDate, brandIDs, accountIDs, accountTypes, staffIds)
	if err != nil {
		return nil, err
	}

	collectionStats := &order.CollectionStats{
		NewPendingReceipts: &order.FinancialStat{
			CurrentPeriodCount: stats.TotalNewPendingCount,
		},
		ApprovedReceipts: &order.FinancialStat{
			CurrentPeriodCount: stats.TotalApprovedCount,
		},
		RejectedReceipts: &order.FinancialStat{
			CurrentPeriodCount: stats.TotalRejectedCount,
		},
	}

	if stats.TotalApproveCompletedCount > 0 {
		collectionStats.AverageApproveDurationHours = &order.FinancialStat{
			CurrentPeriodCount: int64(stats.TotalApproveDurationHours / float64(stats.TotalApproveCompletedCount)),
		}
	} else {
		collectionStats.AverageApproveDurationHours = &order.FinancialStat{}
	}

	return collectionStats, nil
}

func getDisbursementApprovalStats(ctx context.Context, startDate, endDate time.Time, refundTypes []int32, staffIds []int64) (*order.DisbursementApprovalStats, error) {
	stats, err := models.GetDisbursementApprovalStatsFromDB(ctx, global.DB, startDate, endDate, refundTypes, staffIds)
	if err != nil {
		return nil, err
	}

	disbursementApprovalStats := &order.DisbursementApprovalStats{
		NewPendingDisbursements: &order.FinancialStat{
			CurrentPeriodCount: stats.TotalNewPendingCount,
		},
		ApprovedDisbursements: &order.FinancialStat{
			CurrentPeriodCount: stats.TotalApprovedCount,
		},
		RejectedDisbursements: &order.FinancialStat{
			CurrentPeriodCount: stats.TotalRejectedCount,
		},
	}

	if stats.TotalApproveCompletedCount > 0 {
		disbursementApprovalStats.AverageApproveDurationHours = &order.FinancialStat{
			CurrentPeriodCount: int64(stats.TotalApproveDurationHours / float64(stats.TotalApproveCompletedCount)),
		}
	} else {
		disbursementApprovalStats.AverageApproveDurationHours = &order.FinancialStat{}
	}

	return disbursementApprovalStats, nil
}

func getDisbursementPaymentStats(ctx context.Context, startDate, endDate time.Time, refundTypes []int32, staffIds []int64) (*order.DisbursementPaymentStats, error) {
	stats, err := models.GetDisbursementPaymentStatsFromDB(ctx, global.DB, startDate, endDate, refundTypes, staffIds)
	if err != nil {
		return nil, err
	}

	disbursementPaymentStats := &order.DisbursementPaymentStats{
		NewPendingPayments: &order.FinancialStat{
			CurrentPeriodCount: stats.TotalNewPendingPaymentCount,
		},
		SuccessfulPayments: &order.FinancialStat{
			CurrentPeriodCount: stats.TotalPaymentSuccessCount,
		},
		RejectedPayments: &order.FinancialStat{
			CurrentPeriodCount: stats.TotalPaymentRejectedCount,
		},
		OverduePayments: &order.FinancialStat{
			CurrentPeriodCount: stats.TotalPaymentTimeoutCount,
		},
	}

	if stats.TotalPaymentCompletedCount > 0 {
		disbursementPaymentStats.AveragePaymentDurationHours = &order.FinancialStat{
			CurrentPeriodCount: int64(stats.TotalPaymentDurationHours / float64(stats.TotalPaymentCompletedCount)),
		}
	} else {
		disbursementPaymentStats.AveragePaymentDurationHours = &order.FinancialStat{}
	}

	return disbursementPaymentStats, nil
}

func calculateFinancialStat(current, previous *order.FinancialStat) *order.FinancialStat {
	if current == nil {
		current = &order.FinancialStat{}
	}
	if previous == nil {
		previous = &order.FinancialStat{}
	}

	stat := &order.FinancialStat{
		CurrentPeriodCount:  current.CurrentPeriodCount,
		PreviousPeriodCount: previous.CurrentPeriodCount,
		IsValid:             previous.CurrentPeriodCount > 0,
	}

	if previous.CurrentPeriodCount > 0 {
		stat.PercentageChange = (float64(current.CurrentPeriodCount) - float64(previous.CurrentPeriodCount)) / float64(previous.CurrentPeriodCount) * 100
	}

	return stat
}

func GetFundPendingStats(ctx context.Context, req *order.GetFundPendingStatsReq) (*order.GetFundPendingStatsRsp, error) {
	// 转换请求参数
	modelReq := &models.FundPendingStatsRequest{
		BrandIds:            req.BrandIds,
		PaymentAccountTypes: utils.ConvertSliceToInt32(req.PaymentAccountTypes),
		PaymentAccounts:     req.PaymentAccounts,
		StaffIds:            req.StaffIds,
	}

	// 调用模型层查询
	result, err := models.GetFundPendingStatsFromDB(ctx, global.DB, modelReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get fund pending stats: %w", err)
	}

	// 转换返回结果
	return &order.GetFundPendingStatsRsp{
		NewPendingReview:        result.NewPendingReview,
		RejectedResubmitPending: result.RejectedResubmitPending,
		OverduePendingReview:    result.OverduePendingReview,
	}, nil
}

func GetRefundPendingStats(ctx context.Context, req *order.GetRefundPendingStatsReq) (*order.GetRefundPendingStatsRsp, error) {
	// 转换请求参数
	modelReq := &models.RefundPendingStatsRequest{
		RefundTypes: req.RefundTypes,
		StaffIds:    req.StaffIds,
	}

	// 调用模型层查询
	result, err := models.GetRefundPendingStatsFromDB(ctx, global.DB, modelReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get refund pending stats: %w", err)
	}

	// 转换返回结果
	return &order.GetRefundPendingStatsRsp{
		NewPendingReview:        result.NewPendingReview,
		RejectedResubmitPending: result.RejectedResubmitPending,
		OverduePendingReview:    result.OverduePendingReview,
		PendingPayment:          result.PendingPayment,
		NearDeadlinePayment:     result.NearDeadlinePayment,
	}, nil
}
