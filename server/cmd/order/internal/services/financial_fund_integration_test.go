package services

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/pkg/mysql"
	"uofferv2/pkg/nacos"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/order/internal/global"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"github.com/bytedance/sonic"
	"gorm.io/gorm"
)

func init() {
	// 设置环境变量忽略 proto 注册冲突
	os.Setenv("GOLANG_PROTOBUF_REGISTRATION_CONFLICT", "ignore")
}

// FinancialFundIntegrationTestSuite 财务收款集成测试套件
type FinancialFundIntegrationTestSuite struct {
	suite.Suite
	db  *gorm.DB
	ctx context.Context
}

/**
 * SetupSuite 测试套件初始化
 * 通过 nacos 获取数据库配置并连接真实数据库
 */
func (suite *FinancialFundIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// 初始化日志
	logger.InitLogger("debug", "console", logger.LogSize(10*1024*1024)) // 10MB

	// 通过 nacos 获取配置
	err := suite.initConfigFromNacos()
	if err != nil {
		suite.T().Skipf("跳过集成测试: 无法连接 nacos 或数据库配置错误: %v", err)
		return
	}

	// 初始化数据库连接
	suite.db, err = mysql.InitDB(global.ServerConfig.MysqlInfo)
	if err != nil {
		suite.T().Skipf("跳过集成测试: 无法连接数据库: %v", err)
		return
	}

	// 设置全局数据库连接
	global.DB = suite.db

	suite.T().Log("集成测试环境初始化成功")
}

/**
 * initConfigFromNacos 从 nacos 初始化配置
 */
func (suite *FinancialFundIntegrationTestSuite) initConfigFromNacos() error {

	dataId := "order"
	accountGroup := "svc"

	// 使用自适应配置路径
	// 优先级：NACOS_CONFIG_PATH 环境变量 > 命令行参数 > 自动检测 > 默认值
	configPath := utils.GetConfigPath("config/nacos_config.yaml", "NACOS_CONFIG_PATH")
	fmt.Println("configPath", configPath)

	// 从 nacos 获取配置
	content, err := nacos.InitNacos(dataId, accountGroup, configPath)
	if err != nil {
		return err
	}

	// 解析配置
	err = sonic.Unmarshal([]byte(content), &global.ServerConfig)
	if err != nil {
		return err
	}

	return nil
}

/**
 * TearDownSuite 测试套件清理
 */
func (suite *FinancialFundIntegrationTestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		if sqlDB != nil {
			sqlDB.Close()
		}
	}
}

/**
 * SetupTest 每个测试用例前的准备工作
 */
func (suite *FinancialFundIntegrationTestSuite) SetupTest() {
	if suite.db == nil {
		suite.T().Skip("数据库未初始化，跳过测试")
	}
}

/**
 * TestRealFundCreate 测试真实的收款单创建
 */
func (suite *FinancialFundIntegrationTestSuite) TestRealFundCreate() {
	// 准备测试数据
	req := &order.FinancialFundCreateReq{
		OrderNo:         "TEST_INTEGRATION_001",
		CustomerId:      1, // 假设存在的客户ID
		PayType:         1,
		FundType:        1, // 定金
		Currency:        "CNY",
		RealAmountOther: "1000.00",
		RealAmountRmb:   "1000.00",
		DiscountRate:    "100.00000",
		OrderId:         1, // 假设存在的订单ID
		FinancialPaidInfo: []*order.FinancialPaidInfo{
			{
				PaymentAccountId: 1, // 假设存在的支付账户ID
				Currency:         "CNY",
				PaidType:         1,
				AccountName:      "集成测试账户",
				AmountCny:        "1000.00",
				AmountOther:      "1000.00",
				ExchangeRate:     "1.00",
				TransactionNo:    []string{"INTEGRATION_TXN_001"},
			},
		},
	}

	// 执行创建操作
	resp, err := FundCreate(suite.ctx, req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.Greater(suite.T(), resp.Id, int64(0))

	// 验证数据库中的记录
	var fund model.FinancialFund
	err = suite.db.Where("id = ?", resp.Id).First(&fund).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), req.OrderNo, fund.OrderNo)
	assert.Equal(suite.T(), "1000.00000", fund.RealAmountRmb)

	// 验证支付记录
	var paidRecords []model.FinancialPaid
	err = suite.db.Where("financial_fund_id = ?", resp.Id).Find(&paidRecords).Error
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), paidRecords, 1)
	assert.Equal(suite.T(), "微信-有录网001-北京", paidRecords[0].AccountName)

	// 清理测试数据
	suite.cleanupFundData(resp.Id)

	suite.T().Logf("集成测试 - 收款单创建成功，ID: %d", resp.Id)
}

/**
 * TestRealCalculateShouldAmountRmb 测试真实的应收金额计算
 */
func (suite *FinancialFundIntegrationTestSuite) TestRealCalculateShouldAmountRmb() {
	// 创建测试订单支付记录，使用时间戳确保唯一性
	orderID := int64(999999) + time.Now().Unix()%1000 // 使用时间戳确保唯一性

	// 先创建一个定金记录用于测试首款计算
	depositFund := &model.FinancialFund{
		OrderNo:       "CALC_TEST_ORDER",
		OrderID:       orderID,
		FundType:      1, // 定金
		RealAmountRmb: "1000.00",
		ApproveStatus: 2, // 已审核通过
		ContractURL:   "[]",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	err := suite.db.Create(depositFund).Error
	assert.NoError(suite.T(), err)

	// 创建测试订单支付记录
	orderPay := &model.OrderPay{
		OrderID:               orderID,
		AmountFirstReceivable: "4000.00",
		AmountFinalReceivable: "0.00",
	}
	err = suite.db.Create(orderPay).Error
	assert.NoError(suite.T(), err)

	// 测试首款应收金额计算（需要减去已付定金）
	shouldAmount, err := calculateShouldAmountRmb(suite.ctx, 2, orderID, "5000.00")
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "4000.00000", shouldAmount) // 5000 - 1000 = 4000

	// 测试定金应收金额计算
	shouldAmount, err = calculateShouldAmountRmb(suite.ctx, 1, orderID, "1500.00")
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "1500.00", shouldAmount) // 定金直接返回实付金额

	// 清理测试数据
	suite.db.Delete(&model.FinancialFund{}, depositFund.ID)

	suite.T().Log("集成测试 - 应收金额计算验证成功")
}

/**
 * TestRealGetOrderRelations 测试真实的订单关系查询
 */
func (suite *FinancialFundIntegrationTestSuite) TestRealGetOrderRelations() {
	orderID := int64(888888)

	// 创建测试订单关系数据
	relations := []*model.OrderRelation{
		{
			OrderID:   orderID,
			UserID:    1001,
			Action:    6, // 共同提交人
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			OrderID:   orderID,
			UserID:    1002,
			Action:    7, // 订单来源
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	for _, relation := range relations {
		err := suite.db.Create(relation).Error
		assert.NoError(suite.T(), err)
	}

	// 测试查询共同提交人关系
	submitRelations, err := getOrderRelations(suite.ctx, []int64{orderID}, 6)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), submitRelations, orderID)
	assert.Len(suite.T(), submitRelations[orderID], 1)
	assert.Equal(suite.T(), int64(1001), submitRelations[orderID][0].UserID)

	// 测试查询订单来源关系
	sourceRelations, err := getOrderRelations(suite.ctx, []int64{orderID}, 7)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), sourceRelations, orderID)
	assert.Len(suite.T(), sourceRelations[orderID], 1)
	assert.Equal(suite.T(), int64(1002), sourceRelations[orderID][0].UserID)

	// 清理测试数据
	suite.db.Where("order_id = ?", orderID).Delete(&model.OrderRelation{})

	suite.T().Log("集成测试 - 订单关系查询验证成功")
}

/**
 * TestRealFundUpdate 测试真实的收款单更新（包含事务）
 */
func (suite *FinancialFundIntegrationTestSuite) TestRealFundUpdate() {
	// 先创建一个收款单
	fund := &model.FinancialFund{
		OrderNo:       "UPDATE_TEST_ORDER",
		OrderID:       777777,
		FundType:      1,
		RealAmountRmb: "1000.00",
		Currency:      "CNY",
		ApproveStatus: 1,
		ContractURL:   "[]",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	err := suite.db.Create(fund).Error
	assert.NoError(suite.T(), err)

	// 创建原始支付记录
	originalPaid := &model.FinancialPaid{
		FinancialFundID:  fund.ID,
		PaymentAccountID: 1,
		AccountName:      "原始账户",
		AmountCny:        "1000.00",
		Currency:         "CNY",
		ImagesPath:       "[]",
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = suite.db.Create(originalPaid).Error
	assert.NoError(suite.T(), err)

	// 准备更新请求
	updateReq := &order.FinancialFundUpdateReq{
		FinancialFundId: fund.ID,
		RealAmountRmb:   "1500.00",
		Currency:        "CNY",
		FinancialPaidInfo: []*order.FinancialPaidInfo{
			{
				PaymentAccountId: 1,
				Currency:         "CNY",
				PaidType:         1,
				AccountName:      "更新后账户",
				AmountCny:        "1500.00",
				AmountOther:      "1500.00",
				ExchangeRate:     "1.00",
				TransactionNo:    []string{"UPDATE_TXN_001"},
			},
		},
	}

	// 执行更新操作
	resp, err := FundUpdate(suite.ctx, updateReq)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)

	// 验证主表更新
	var updatedFund model.FinancialFund
	err = suite.db.Where("id = ?", fund.ID).First(&updatedFund).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "1500.00000", updatedFund.RealAmountRmb)

	// 验证支付记录更新（原记录应该被删除，新记录被创建）
	var paidRecords []model.FinancialPaid
	err = suite.db.Where("financial_fund_id = ?", fund.ID).Find(&paidRecords).Error
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), paidRecords, 1)
	assert.Equal(suite.T(), "微信-有录网001-北京", paidRecords[0].AccountName)
	assert.Equal(suite.T(), "1500.00000", paidRecords[0].AmountCny)

	// 清理测试数据
	suite.cleanupFundData(fund.ID)

	suite.T().Log("集成测试 - 收款单更新（事务）验证成功")
}

/**
 * TestRealFundDel 测试真实的收款单删除（包含事务）
 */
func (suite *FinancialFundIntegrationTestSuite) TestRealFundDel() {
	// 创建测试收款单
	fund := &model.FinancialFund{
		OrderNo:       "DELETE_TEST_ORDER",
		FundNo:        "DELETE_FUND_001",
		OrderID:       666666,
		FundType:      1,
		RealAmountRmb: "1000.00",
		Currency:      "CNY",
		ApproveStatus: 1,
		ContractURL:   "[]",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	err := suite.db.Create(fund).Error
	assert.NoError(suite.T(), err)

	// 创建支付记录
	paidRecord := &model.FinancialPaid{
		FinancialFundID:  fund.ID,
		PaymentAccountID: 1,
		AccountName:      "删除测试账户",
		AmountCny:        "1000.00",
		Currency:         "CNY",
		ImagesPath:       "[]",
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = suite.db.Create(paidRecord).Error
	assert.NoError(suite.T(), err)

	// 准备删除请求
	delReq := &order.FinancialFundDelReq{
		FinancialFundId: fund.ID,
		OrderNo:         fund.OrderNo,
		FundNo:          fund.FundNo,
		FundType:        fund.FundType,
		OrderId:         fund.OrderID,
	}

	// 执行删除操作
	resp, err := FundDel(suite.ctx, delReq)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)

	// 验证主表记录被删除
	var deletedFund model.FinancialFund
	err = suite.db.Where("id = ?", fund.ID).First(&deletedFund).Error
	assert.Error(suite.T(), err)
	assert.True(suite.T(), err == gorm.ErrRecordNotFound)

	// 验证支付记录被删除
	var deletedPaid []model.FinancialPaid
	err = suite.db.Where("financial_fund_id = ?", fund.ID).Find(&deletedPaid).Error
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), deletedPaid, 0)

	suite.T().Log("集成测试 - 收款单删除（事务）验证成功")
}

/**
 * TestRealCreateFinancialPaidRecords 测试真实的支付记录创建
 */
func (suite *FinancialFundIntegrationTestSuite) TestRealCreateFinancialPaidRecords() {
	fundID := int64(555555) // 假设的收款单ID

	paidInfo := []*order.FinancialPaidInfo{
		{
			PaymentAccountId: 1,
			Currency:         "CNY",
			PaidType:         1,
			AccountName:      "集成测试账户1",
			AmountCny:        "500.00",
			AmountOther:      "500.00",
			ExchangeRate:     "1.00",
			TransactionNo:    []string{"INT_TXN_001", "INT_TXN_002"},
		},
		{
			PaymentAccountId: 2,
			Currency:         "USD",
			PaidType:         2,
			AccountName:      "集成测试账户2",
			AmountCny:        "700.00",
			AmountOther:      "100.00",
			ExchangeRate:     "7.00",
			TransactionNo:    []string{"INT_TXN_003"},
		},
	}

	// 执行支付记录创建
	err := createFinancialPaidRecords(suite.ctx, fundID, paidInfo)
	assert.NoError(suite.T(), err)

	// 验证支付记录
	var paidRecords []model.FinancialPaid
	err = suite.db.Where("financial_fund_id = ?", fundID).Find(&paidRecords).Error
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), paidRecords, 2)

	// 验证第一条记录
	record1 := paidRecords[0]
	assert.Equal(suite.T(), "微信-有录网001-北京", record1.AccountName)
	assert.Equal(suite.T(), "500.00000", record1.AmountCny)
	assert.Contains(suite.T(), record1.TransactionNo, "INT_TXN_001")

	// 清理测试数据
	suite.db.Where("financial_fund_id = ?", fundID).Delete(&model.FinancialPaid{})

	suite.T().Log("集成测试 - 支付记录创建验证成功")
}

/**
 * cleanupFundData 清理收款单测试数据
 */
func (suite *FinancialFundIntegrationTestSuite) cleanupFundData(fundID int64) {
	// 删除支付记录
	suite.db.Where("financial_fund_id = ?", fundID).Delete(&model.FinancialPaid{})
	// 删除收款单
	suite.db.Delete(&model.FinancialFund{}, fundID)
}

/**
 * TestFinancialFundIntegrationTestSuite 运行集成测试套件
 */
func TestFinancialFundIntegrationTestSuite(t *testing.T) {
	// 检查是否启用集成测试
	if os.Getenv("INTEGRATION_TEST") != "true" {
		t.Skip("跳过集成测试: 设置环境变量 INTEGRATION_TEST=true 来启用")
	}

	suite.Run(t, new(FinancialFundIntegrationTestSuite))
}

/**
 * TestRealFundList 测试真实的收款单列表查询
 */
func (suite *FinancialFundIntegrationTestSuite) TestRealFundList() {
	// 创建测试数据
	testFunds := []*model.FinancialFund{
		{
			OrderNo:       "LIST_TEST_001",
			FundNo:        "FUND_LIST_001",
			OrderID:       111111,
			CustomerID:    1,
			FundType:      1,
			RealAmountRmb: "1000.00",
			Currency:      "CNY",
			ApproveStatus: 1,
			ContractURL:   "[]",
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			OrderNo:       "LIST_TEST_002",
			FundNo:        "FUND_LIST_002",
			OrderID:       111112,
			CustomerID:    1,
			FundType:      2,
			RealAmountRmb: "2000.00",
			Currency:      "CNY",
			ApproveStatus: 2,
			ContractURL:   "[]",
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
	}

	var createdIDs []int64
	for _, fund := range testFunds {
		err := suite.db.Create(fund).Error
		assert.NoError(suite.T(), err)
		createdIDs = append(createdIDs, fund.ID)
	}

	// 准备查询请求
	listReq := &order.FinancialFundListReq{
		CustomerId:    1,
		ApproveStatus: 0, // 查询所有状态
		PageNum:       1,
		PageSize:      10,
	}

	// 执行列表查询
	resp, err := FundList(suite.ctx, listReq)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.GreaterOrEqual(suite.T(), len(resp.FinancialFundList), 2)
	assert.GreaterOrEqual(suite.T(), resp.Total, int64(2))

	// 验证返回数据的正确性
	foundTestData := 0
	for _, item := range resp.FinancialFundList {
		if item.OrderNo == "LIST_TEST_001" || item.OrderNo == "LIST_TEST_002" {
			foundTestData++
			assert.Equal(suite.T(), int64(1), item.CustomerId)
			assert.Contains(suite.T(), []string{"CNY"}, item.Currency)
		}
	}
	assert.GreaterOrEqual(suite.T(), foundTestData, 2)

	// 清理测试数据
	for _, id := range createdIDs {
		suite.cleanupFundData(id)
	}

	suite.T().Log("集成测试 - 收款单列表查询验证成功")
}

/**
 * BenchmarkRealFundList 真实环境下的列表查询性能测试
 */
func (suite *FinancialFundIntegrationTestSuite) BenchmarkRealFundList() {
	if testing.Short() {
		suite.T().Skip("跳过性能测试")
	}

	req := &order.FinancialFundListReq{
		PageNum:  1,
		PageSize: 100,
	}

	// 预热
	_, _ = FundList(suite.ctx, req)

	// 性能测试
	start := time.Now()
	iterations := 10

	for i := 0; i < iterations; i++ {
		_, err := FundList(suite.ctx, req)
		assert.NoError(suite.T(), err)
	}

	duration := time.Since(start)
	avgDuration := duration / time.Duration(iterations)

	suite.T().Logf("性能测试 - FundList 平均执行时间: %v (总计: %v, 迭代次数: %d)",
		avgDuration, duration, iterations)

	// 性能断言 - 平均响应时间应该小于500ms
	assert.Less(suite.T(), avgDuration, 500*time.Millisecond,
		"FundList 平均响应时间应该小于500ms")
}
