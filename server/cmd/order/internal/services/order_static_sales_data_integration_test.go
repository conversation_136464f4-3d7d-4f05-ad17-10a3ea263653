package services

import (
	"context"
	"os"
	"testing"
	"time"

	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/consts"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/order/internal/global"
	"uofferv2/server/cmd/order/internal/initialize"

	"github.com/stretchr/testify/assert"
)

// 销售数据统计集成测试
//
// 使用方法:
//    INTEGRATION_TEST=true HOST_ENV=local GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore go test -run TestOrderStaticSalesDataIntegration -v
//
// 注意: 这些测试需要连接到本地开发环境的数据库

func TestOrderStaticSalesDataIntegration(t *testing.T) {
	// 检查是否为集成测试
	if os.Getenv("INTEGRATION_TEST") != "true" {
		t.Skip("跳过集成测试: 设置环境变量 INTEGRATION_TEST=true 来启用")
		return
	}

	// 设置本地环境
	if os.Getenv("HOST_ENV") != "local" {
		t.Skip("此测试只能在本地环境运行，需要设置 HOST_ENV=local")
		return
	}

	// 设置protobuf冲突处理
	os.Setenv("GOLANG_PROTOBUF_REGISTRATION_CONFLICT", "ignore")

	// 初始化测试环境
	setupTestEnvironment(t)

	t.Run("TestInsertYesterdaySalesData", func(t *testing.T) {
		testInsertYesterdaySalesData(t)
	})

	t.Run("TestInsertHistoricalSalesData", func(t *testing.T) {
		testInsertHistoricalSalesData(t)
	})

	t.Run("TestInsertHistoricalSalesDataWithVerification", func(t *testing.T) {
		testInsertHistoricalSalesDataWithVerification(t)
	})
}

// setupTestEnvironment 设置测试环境
func setupTestEnvironment(t *testing.T) {
	// 设置工作目录到项目根目录
	err := os.Chdir("../../../../../")
	if err != nil {
		t.Fatalf("无法切换到项目根目录: %v", err)
	}

	// 初始化日志
	logger.InitLogger("./tmp/logs/order_integration_test.log", logger.LevelDebug.String(), 500*logger.LogSizeMb)

	// 初始化Nacos配置
	initialize.InitNacos(consts.DataIdOrder, consts.GroupSvc)

	// 初始化数据库
	initialize.InitDB()

	t.Logf("测试环境初始化完成")
	t.Logf("数据库连接状态: %v", global.DB != nil)
}

// testInsertYesterdaySalesData 测试插入昨天销售数据
func testInsertYesterdaySalesData(t *testing.T) {
	ctx := context.Background()
	service := &OrderService{}

	req := &order.InsertYesterdaySalesDataReq{}

	t.Logf("开始测试 InsertYesterdaySalesData")

	resp, err := service.InsertYesterdaySalesData(ctx, req)

	// 验证结果
	assert.NoError(t, err, "InsertYesterdaySalesData 不应该返回错误")
	assert.NotNil(t, resp, "响应不应该为 nil")
	assert.NotNil(t, resp.Base, "响应的 Base 字段不应该为 nil")

	t.Logf("InsertYesterdaySalesData 测试完成")
}

// testInsertHistoricalSalesData 测试插入历史销售数据
func testInsertHistoricalSalesData(t *testing.T) {
	ctx := context.Background()
	service := &OrderService{}

	// 测试最近60天的数据
	now := time.Now()
	startDate := now.AddDate(0, 0, -60) // 60天前
	endDate := now.AddDate(0, 0, -1)    // 昨天

	req := &order.InsertHistoricalSalesDataReq{
		StartDate: startDate.UnixMilli(),
		EndDate:   endDate.UnixMilli(),
	}

	t.Logf("开始测试 InsertHistoricalSalesData (60天历史数据)")
	t.Logf("测试时间范围: %s 到 %s", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

	resp, err := service.InsertHistoricalSalesData(ctx, req)

	// 验证结果
	assert.NoError(t, err, "InsertHistoricalSalesData 不应该返回错误")
	assert.NotNil(t, resp, "响应不应该为 nil")
	assert.NotNil(t, resp.Base, "响应的 Base 字段不应该为 nil")

	t.Logf("InsertHistoricalSalesData (60天历史数据) 测试完成")
}

// testInsertHistoricalSalesDataWithVerification 测试插入历史销售数据并验证结果
func testInsertHistoricalSalesDataWithVerification(t *testing.T) {
	ctx := context.Background()
	service := &OrderService{}

	// 测试最近60天的数据
	now := time.Now()
	startDate := now.AddDate(0, 0, -60) // 60天前
	endDate := now.AddDate(0, 0, -1)    // 昨天

	req := &order.InsertHistoricalSalesDataReq{
		StartDate: startDate.UnixMilli(),
		EndDate:   endDate.UnixMilli(),
	}

	t.Logf("开始测试 InsertHistoricalSalesData 并验证数据")
	t.Logf("测试时间范围: %s 到 %s", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

	// 记录执行前的数据条数
	var countBefore int64
	err := global.DB.Table("order_static_daily").
		Where("day BETWEEN ? AND ?", startDate, endDate).
		Count(&countBefore).Error
	if err != nil {
		t.Fatalf("查询执行前数据条数失败: %v", err)
	}
	t.Logf("执行前数据库中相关数据条数: %d", countBefore)

	resp, err := service.InsertHistoricalSalesData(ctx, req)

	// 验证结果
	assert.NoError(t, err, "InsertHistoricalSalesData 不应该返回错误")
	assert.NotNil(t, resp, "响应不应该为 nil")
	assert.NotNil(t, resp.Base, "响应的 Base 字段不应该为 nil")

	// 记录执行后的数据条数
	var countAfter int64
	err = global.DB.Table("order_static_daily").
		Where("day BETWEEN ? AND ?", startDate, endDate).
		Count(&countAfter).Error
	if err != nil {
		t.Fatalf("查询执行后数据条数失败: %v", err)
	}
	t.Logf("执行后数据库中相关数据条数: %d", countAfter)

	if countAfter > countBefore {
		t.Logf("✅ 成功插入了 %d 条新数据", countAfter-countBefore)
	} else if countAfter == countBefore {
		t.Logf("⚠️  没有新数据插入，可能数据已存在或没有符合条件的订单")
	}

	// 查询一些示例数据
	var sampleData []struct {
		Day    time.Time `json:"day"`
		UID    int64     `json:"uid"`
		Cate   int32     `json:"cate"`
		Num    int32     `json:"num"`
		Amount string    `json:"amount"`
	}

	err = global.DB.Table("order_static_daily").
		Where("day BETWEEN ? AND ?", startDate, endDate).
		Limit(5).
		Scan(&sampleData).Error

	if err != nil {
		t.Logf("查询示例数据失败: %v", err)
	} else {
		t.Logf("示例数据 (前5条):")
		for i, data := range sampleData {
			t.Logf("  [%d] 日期: %s, 用户ID: %d, 类型: %d, 数量: %d, 金额: %s",
				i+1, data.Day.Format("2006-01-02"), data.UID, data.Cate, data.Num, data.Amount)
		}
	}

	t.Logf("InsertHistoricalSalesData 数据验证测试完成")
}

// TestOrderStaticSalesDataInvalidParams 测试无效参数
func TestOrderStaticSalesDataInvalidParams(t *testing.T) {
	// 检查是否为集成测试
	if os.Getenv("INTEGRATION_TEST") != "true" {
		t.Skip("跳过集成测试: 设置环境变量 INTEGRATION_TEST=true 来启用")
		return
	}

	ctx := context.Background()
	service := &OrderService{}

	t.Run("TestInvalidDateRange", func(t *testing.T) {
		// 测试无效的日期范围
		req := &order.InsertHistoricalSalesDataReq{
			StartDate: 0,
			EndDate:   0,
		}

		resp, err := service.InsertHistoricalSalesData(ctx, req)

		assert.NoError(t, err, "服务层不应该返回 error，而是通过响应状态表示错误")
		assert.NotNil(t, resp, "响应不应该为 nil")
		assert.NotNil(t, resp.Base, "响应的 Base 字段不应该为 nil")
		// 参数无效时，应该返回错误状态的响应
	})

	t.Run("TestStartDateAfterEndDate", func(t *testing.T) {
		// 测试开始日期晚于结束日期
		now := time.Now()
		req := &order.InsertHistoricalSalesDataReq{
			StartDate: now.UnixMilli(),
			EndDate:   now.AddDate(0, 0, -1).UnixMilli(), // 昨天
		}

		resp, err := service.InsertHistoricalSalesData(ctx, req)

		// 模型层会返回错误，服务层会正确地返回错误
		assert.Error(t, err, "开始日期晚于结束日期时应该返回错误")
		assert.NotNil(t, resp, "响应不应该为 nil")
		assert.NotNil(t, resp.Base, "响应的 Base 字段不应该为 nil")
	})
}
