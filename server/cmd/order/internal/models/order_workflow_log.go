package models

import (
	"context"
	"fmt"
	"time"

	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/order/internal/global"
)

type GetOrderWorkflowLogList struct {
	PageNum     int32      `json:"page_num,omitempty"`     // 页数
	PageSize    int32      `json:"page_size,omitempty"`    // 每页数量
	CustomerIds []int64    `json:"customer_ids,omitempty"` // 用户UID
	UpdaterIds  []int64    `json:"updater_ids,omitempty"`  // 操作人UID（出单成员）
	OrderBy     []*OrderBy `json:"order_by"`               // 排序字段
}

func (o *Order) GetOrderWorkflowLogList(ctx context.Context, req *GetOrderWorkflowLogList) ([]*model.OrderWorkflowLog, int64, error) {

	const StatusOpened = 1              // 状态#1%正常|2%关闭
	const WorkflowNodeTypeTerminate = 5 // 节点状态(0:UNKNOWN, 1:未开始,2:进行中,3暂停中,4:已完成,5:已终止)

	var (
		err        error
		total      int64
		creatorIds = make([]int64, 0)
		actions    = make([]OrderRelationAction, 0)
		mods       = make([]*model.OrderWorkflowLog, 0)
		orderBy    = fmt.Sprintf(" %s DESC ", model.OrderWorkflowLogColumns.CreatedAt())
	)

	// order_relation
	subQuery := o.getOrderListQueryByRelation(ctx, creatorIds, req.UpdaterIds, actions)

	query := global.DB.WithContext(ctx).Debug().
		Model(&model.OrderWorkflowLog{}).
		Joins(orderWorkflowJoinOrder).
		Where(tableNameOrderWorkflowLog+"."+model.OrderWorkflowLogColumns.Status(), StatusOpened).
		Where(tableNameOrderWorkflowLog+"."+model.OrderWorkflowLogColumns.WorkflowNodeStatus(), WorkflowNodeTypeTerminate).
		Where("EXISTS(?)", subQuery)

	queryCount := global.DB.WithContext(ctx).Debug().
		Model(&model.OrderWorkflowLog{}).
		Joins(orderWorkflowJoinOrder).
		Where(tableNameOrderWorkflowLog+"."+model.OrderWorkflowLogColumns.Status(), 1).
		Where(tableNameOrderWorkflowLog+"."+model.OrderWorkflowLogColumns.WorkflowNodeStatus(), 5).
		Where("EXISTS(?)", subQuery).
		Select("COUNT(*)")

	if len(req.CustomerIds) == 1 && req.CustomerIds[0] > 0 {
		query = query.Where(tableNameOrderWorkflowLog+"."+model.OrderWorkflowLogColumns.CustomerID(), req.CustomerIds[0])
		queryCount = queryCount.Where(tableNameOrderWorkflowLog+"."+model.OrderWorkflowLogColumns.CustomerID(), req.CustomerIds[0])
	}

	if len(req.CustomerIds) > 1 {
		query = query.Where(tableNameOrderWorkflowLog+"."+model.OrderWorkflowLogColumns.CustomerID()+" IN (?)", req.CustomerIds)
		queryCount = queryCount.Where(tableNameOrderWorkflowLog+"."+model.OrderWorkflowLogColumns.CustomerID()+" IN (?)", req.CustomerIds)
	}

	err = queryCount.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Limit(int(req.PageSize)).
		Offset(int((req.PageNum - 1) * req.PageSize)).
		Order(orderBy).
		Find(&mods).
		Error
	if err != nil {
		return nil, 0, err
	}

	return mods, total, nil
}

func (o *Order) CloseOrderWorkflowLog(ctx context.Context, closedBy int64, ids []int64) error {

	if len(ids) == 0 {
		return nil
	}

	timeNow := time.Now()

	updates := &model.OrderWorkflowLog{
		ClosedBy: closedBy,
		ClosedAt: &timeNow,
		Status:   2, // 状态#1%正常|2%关闭
	}

	query := global.DB.WithContext(ctx).Debug().Model(&model.OrderWorkflowLog{})

	if len(ids) == 1 {
		query = query.Where(tableNameOrderWorkflowLog+"."+model.OrderWorkflowLogColumns.ID(), ids[0])
	}

	if len(ids) > 1 {
		query = query.Where(tableNameOrderWorkflowLog+"."+model.OrderWorkflowLogColumns.ID()+" in (?)", ids)
	}

	return query.Updates(updates).Error
}
