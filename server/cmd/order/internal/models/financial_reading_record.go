package models

import (
	"context"
	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/order/internal/global"
)

type FinancialReadingRecord struct{}

// Create 设置已读 - 检查后写入
func (f *FinancialReadingRecord) Create(ctx context.Context, req *model.FinancialReadingRecord) error {
	// 先检查是否已存在相同记录
	exists, err := f.CheckIsReadWithStatus(ctx, req.EmployeeID, req.RecordID, req.RecordType, req.ApproveStatus)
	if err != nil {
		return err
	}

	// 如果已存在，则不重复创建
	if exists {
		return nil
	}

	// 不存在则创建新记录
	if err = global.DB.WithContext(ctx).Create(&req).Error; err != nil {
		return err
	}
	return nil
}

// CheckIsReadWithStatus 检查指定状态是否已读
func (f *FinancialReadingRecord) CheckIsReadWithStatus(ctx context.Context, employeeID, recordID int64, recordType, approveStatus int32) (bool, error) {
	var count int64
	err := global.DB.WithContext(ctx).
		Model(&model.FinancialReadingRecord{}).
		Where("employee_id = ? AND record_id = ? AND record_type = ? AND approve_status = ?", employeeID, recordID, recordType, approveStatus).
		Count(&count).
		Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
