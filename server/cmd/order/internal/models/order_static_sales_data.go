package models

import (
	"context"
	"fmt"
	"time"

	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/order/internal/global"
)

// 移除 StaticPeriod 枚举，改为纯日数据存储

type StaticCate = int32

// 类型#1%新增订单|2%审核通过|3%申请支款
const (
	CateNew          StaticCate = 1
	CateFirst        StaticCate = 2
	CateDisbursement StaticCate = 3
)

const (
	BatchInsertStaticDailySize = 1000
)

var (
	// OrderNewStaticColumns 新建订单数
	// order_relation.user_id AS user_id, COUNT(*) AS total, SUM(order_pay.amount_total) AS amount
	// 修复：移除 DISTINCT 避免金额重复计算问题，通过子查询确保正确的金额汇总
	OrderNewStaticColumns = fmt.Sprintf(
		"%s.%s AS user_id, COUNT(DISTINCT %s.%s) AS total, SUM(%s.%s) AS amount",
		tableNameOrderRelation,
		model.OrderRelationColumns.UserID(),
		tableNameOrder,
		model.OrderColumns.ID(),
		tableNameOrderPay,
		model.OrderPayColumns.AmountTotal(),
	)

	// OrderFirstStaticColumns 审核通过
	// order_relation.user_id AS user_id, COUNT(*) AS total, SUM(order_pay.amount_first) AS amount
	// 修复：移除 DISTINCT 避免金额重复计算问题，通过子查询确保正确的金额汇总
	OrderFirstStaticColumns = fmt.Sprintf(
		"%s.%s AS user_id, COUNT(DISTINCT %s.%s) AS total, SUM(%s.%s) AS amount",
		tableNameOrderRelation,
		model.OrderRelationColumns.UserID(),
		tableNameOrder,
		model.OrderColumns.ID(),
		tableNameOrderPay,
		model.OrderPayColumns.AmountFirst(),
	)

	// OrderDisbursementStaticColumns 申请支款
	// order_relation.user_id AS user_id, COUNT(*) AS total, SUM(order_pay.amount_disbursement_review) AS amount
	// 修复：移除 DISTINCT 避免金额重复计算问题，通过子查询确保正确的金额汇总
	OrderDisbursementStaticColumns = fmt.Sprintf(
		"%s.%s AS user_id, COUNT(DISTINCT %s.%s) AS total, SUM(%s.%s) AS amount",
		tableNameOrderRelation,
		model.OrderRelationColumns.UserID(),
		tableNameOrder,
		model.OrderColumns.ID(),
		tableNameOrderPay,
		model.OrderPayColumns.AmountDisbursementReview(),
	)

	// OrderStaticDailyColumns
	// uid AS user_id, cate, SUM(num) AS total, SUM(amount) AS amount
	OrderStaticDailyColumns = fmt.Sprintf(
		"%s.%s AS user_id, %s.%s, SUM(%s.%s) AS total, SUM(%s.%s) AS amount",
		tableNameOrderStaticDaily,
		model.OrderStaticDailyColumns.UID(),
		tableNameOrderStaticDaily,
		model.OrderStaticDailyColumns.Cate(),
		tableNameOrderStaticDaily,
		model.OrderStaticDailyColumns.Num(),
		tableNameOrderStaticDaily,
		model.OrderStaticDailyColumns.Amount(),
	)
)

type StaticSalesData struct {
	UserId int64  `gorm:"column:user_id"`
	Total  int32  `gorm:"column:total"`
	Amount string `gorm:"column:amount"`
	Cate   int32  `gorm:"column:cate"`
}

func (o *Order) GetYesterdaySalesDataNew(ctx context.Context) ([]StaticSalesData, time.Time, error) {
	timeNow := time.Now()
	todayStart := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, timeNow.Location())
	yesterdayStart := todayStart.AddDate(0, 0, -1)

	results, err := o.GetSalesDataNewByDate(ctx, yesterdayStart)
	return results, yesterdayStart, err
}

func (o *Order) GetYesterdaySalesDataFirst(ctx context.Context) ([]StaticSalesData, time.Time, error) {
	timeNow := time.Now()
	todayStart := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, timeNow.Location())
	yesterdayStart := todayStart.AddDate(0, 0, -1)

	results, err := o.GetSalesDataFirstByDate(ctx, yesterdayStart)
	return results, yesterdayStart, err
}

func (o *Order) GetYesterdaySalesDataDisbursement(ctx context.Context) ([]StaticSalesData, time.Time, error) {
	timeNow := time.Now()
	todayStart := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, timeNow.Location())
	yesterdayStart := todayStart.AddDate(0, 0, -1)

	results, err := o.GetSalesDataDisbursementByDate(ctx, yesterdayStart)
	return results, yesterdayStart, err
}

func (o *Order) InsertYesterdaySalesData(ctx context.Context) error {

	// 新增订单
	resultsNew, yesterdayStart, err := o.GetYesterdaySalesDataNew(ctx)
	if err != nil {
		return err
	}

	// 审核通过，首款
	resultsFirst, yesterdayStart, err := o.GetYesterdaySalesDataFirst(ctx)
	if err != nil {
		return err
	}

	// 申请订单
	resultsDisbursement, yesterdayStart, err := o.GetYesterdaySalesDataDisbursement(ctx)
	if err != nil {
		return err
	}

	lens := len(resultsNew) + len(resultsFirst) + len(resultsDisbursement)
	timeNow := time.Now()

	insertData := make([]model.OrderStaticDaily, 0, lens)

	for _, result := range resultsNew {
		insertData = append(insertData, model.OrderStaticDaily{
			ID:        0,
			UID:       result.UserId,
			Cate:      CateNew, // 类型#1%新增订单|2%审核通过|3%申请支款
			Num:       result.Total,
			Amount:    result.Amount,
			Day:       yesterdayStart,
			CreatedAt: timeNow,
		})
	}

	for _, result := range resultsFirst {
		insertData = append(insertData, model.OrderStaticDaily{
			ID:        0,
			UID:       result.UserId,
			Cate:      CateFirst, // 类型#1%新增订单|2%审核通过|3%申请支款
			Num:       result.Total,
			Amount:    result.Amount,
			Day:       yesterdayStart,
			CreatedAt: timeNow,
		})
	}

	for _, result := range resultsDisbursement {
		insertData = append(insertData, model.OrderStaticDaily{
			ID:        0,
			UID:       result.UserId,
			Cate:      CateDisbursement, // 类型#1%新增订单|2%审核通过|3%申请支款
			Num:       result.Total,
			Amount:    result.Amount,
			Day:       yesterdayStart,
			CreatedAt: timeNow,
		})
	}

	// 批量插入（每batchSize条一个批次）
	err = global.DB.WithContext(ctx).Debug().CreateInBatches(insertData, BatchInsertStaticDailySize).Error
	if err != nil {
		return err
	}

	return nil
}

func (o *Order) GetSalesStaticByUidDayStartEnd(ctx context.Context, uid int64, startDay, endDay time.Time) ([]StaticSalesData, error) {

	// SELECT
	//	order_static_daily.uid AS user_id,
	//	order_static_daily.cate,
	//	SUM(order_static_daily.num) AS total,
	//	SUM(order_static_daily.amount) AS amount
	// FROM
	//	`order_static_daily`
	// WHERE
	//  uid = 1
	//	AND `period` = 1
	//	AND (DAY BETWEEN '2025-07-14 00:00:00' AND '2025-07-20 23:59:59.999')
	// GROUP BY
	//	uid,
	//	cate
	query := global.DB.WithContext(ctx).Debug().
		Model(&model.OrderStaticDaily{}).
		Select(OrderStaticDailyColumns).
		Where(model.OrderStaticDailyColumns.UID(), uid).
		Where(model.OrderStaticDailyColumns.Day()+" BETWEEN ? AND ?", startDay, endDay).
		Group(model.OrderStaticDailyColumns.UID() + ", " + model.OrderStaticDailyColumns.Cate())

	results := make([]StaticSalesData, 0)
	if err := query.Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (o *Order) GetSalesStaticByDayStartEnd(ctx context.Context, startDay, endDay time.Time) ([]StaticSalesData, error) {

	// SELECT
	//	order_static_daily.uid AS user_id,
	//	order_static_daily.cate,
	//	SUM(order_static_daily.num) AS total,
	//	SUM(order_static_daily.amount) AS amount
	// FROM
	//	`order_static_daily`
	// WHERE
	//	`period` = 1
	//	AND (DAY BETWEEN '2025-07-14 00:00:00' AND '2025-07-20 23:59:59.999')
	// GROUP BY
	//	uid,
	//	cate
	query := global.DB.WithContext(ctx).Debug().
		Model(&model.OrderStaticDaily{}).
		Select(OrderStaticDailyColumns).
		Where(model.OrderStaticDailyColumns.Day()+" BETWEEN ? AND ?", startDay, endDay).
		Group(model.OrderStaticDailyColumns.UID() + ", " + model.OrderStaticDailyColumns.Cate())

	results := make([]StaticSalesData, 0)
	if err := query.Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

// GetSalesDataNewByDate 获取指定日期的新增订单数据
func (o *Order) GetSalesDataNewByDate(ctx context.Context, targetDate time.Time) ([]StaticSalesData, error) {
	var (
		// 新建订单数：创建时间在统计周期内，当前登录用户为"创建人"或"共同提交人"或"订单来源"的订单数求和
		actions = []OrderRelationAction{OrderRelationActionCreate, OrderRelationActionSubmission, OrderRelationActionSource}
		results = make([]StaticSalesData, 0)
	)

	// 计算目标日期的开始和结束时间
	targetStart := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 0, 0, 0, 0, targetDate.Location())
	targetEnd := targetStart.Add(24*time.Hour - time.Nanosecond)

	// 正确的解决方案：同一用户在同一订单上可能有多种关系，需要去重
	// 先获取每个用户关联的唯一订单，再统计数量和金额
	query := global.DB.WithContext(ctx).Debug().
		Table("(?) as user_orders",
			global.DB.
				Model(&model.Order{}).
				Select("DISTINCT "+
					tableNameOrderRelation+"."+model.OrderRelationColumns.UserID()+" as user_id, "+
					tableNameOrder+"."+model.OrderColumns.ID()+" as order_id, "+
					tableNameOrderPay+"."+model.OrderPayColumns.AmountTotal()+" as amount").
				Joins(orderJoinOrderPay).
				Joins(orderJoinOrderRelation).
				Where(tableNameOrderRelation+"."+model.OrderRelationColumns.Action()+" IN (?)", actions).
				Where(tableNameOrder+"."+model.OrderColumns.DeletedAt(), 0).
				Where(tableNameOrderRelation+"."+model.OrderRelationColumns.DeletedAt(), 0).
				Where(tableNameOrder+"."+model.OrderColumns.CreatedAt()+" BETWEEN ? AND ? ", targetStart, targetEnd),
		).
		Select("user_id, COUNT(order_id) as total, SUM(amount) as amount").
		Group("user_id")

	if err := query.Scan(&results).Error; err != nil {
		return results, err
	}

	return results, nil
}

// GetSalesDataFirstByDate 获取指定日期的审核通过数据
func (o *Order) GetSalesDataFirstByDate(ctx context.Context, targetDate time.Time) ([]StaticSalesData, error) {
	var (
		// 审核通过订单数：审核通过时间在统计周期内，当前登录用户为"创建人"或"共同提交人"或"订单来源"的，首期款或全款审核通过的订单数求和
		actions = []OrderRelationAction{OrderRelationActionCreate, OrderRelationActionSubmission, OrderRelationActionSource}
		results = make([]StaticSalesData, 0)
	)

	// 计算目标日期的开始和结束时间
	targetStart := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 0, 0, 0, 0, targetDate.Location())
	targetEnd := targetStart.Add(24*time.Hour - time.Nanosecond)

	// 正确的解决方案：同一用户在同一订单上可能有多种关系，需要去重
	// 先获取每个用户关联的唯一订单，再统计数量和金额
	query := global.DB.WithContext(ctx).Debug().
		Table("(?) as user_orders",
			global.DB.
				Model(&model.Order{}).
				Select("DISTINCT "+
					tableNameOrderRelation+"."+model.OrderRelationColumns.UserID()+" as user_id, "+
					tableNameOrder+"."+model.OrderColumns.ID()+" as order_id, "+
					tableNameOrderPay+"."+model.OrderPayColumns.AmountFirst()+" as amount").
				Joins(orderJoinOrderPay).
				Joins(orderJoinOrderRelation).
				Where(tableNameOrderRelation+"."+model.OrderRelationColumns.Action()+" IN (?)", actions).
				Where(tableNameOrder+"."+model.OrderColumns.DeletedAt(), 0).
				Where(tableNameOrderRelation+"."+model.OrderRelationColumns.DeletedAt(), 0).
				Where(tableNameOrder+"."+model.OrderColumns.PassFirstAt()+" BETWEEN ? AND ? ", targetStart, targetEnd),
		).
		Select("user_id, COUNT(order_id) as total, SUM(amount) as amount").
		Group("user_id")

	if err := query.Scan(&results).Error; err != nil {
		return results, err
	}

	return results, nil
}

// GetSalesDataDisbursementByDate 获取指定日期的申请支款数据
func (o *Order) GetSalesDataDisbursementByDate(ctx context.Context, targetDate time.Time) ([]StaticSalesData, error) {
	var (
		// 申请支款订单数:支款提交时间在统计周期内，当前登录用户为"创建人"或"共同提交人"或"订单来源"的，状态为【支款订单】的订单数求和
		actions = []OrderRelationAction{OrderRelationActionCreate, OrderRelationActionSubmission, OrderRelationActionSource}
		results = make([]StaticSalesData, 0)
	)

	// 计算目标日期的开始和结束时间
	targetStart := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 0, 0, 0, 0, targetDate.Location())
	targetEnd := targetStart.Add(24*time.Hour - time.Nanosecond)

	// 正确的解决方案：同一用户在同一订单上可能有多种关系，需要去重
	// 先获取每个用户关联的唯一订单，再统计数量和金额
	query := global.DB.WithContext(ctx).Debug().
		Table("(?) as user_orders",
			global.DB.
				Model(&model.Order{}).
				Select("DISTINCT "+
					tableNameOrderRelation+"."+model.OrderRelationColumns.UserID()+" as user_id, "+
					tableNameOrder+"."+model.OrderColumns.ID()+" as order_id, "+
					tableNameOrderPay+"."+model.OrderPayColumns.AmountDisbursementReview()+" as amount").
				Joins(orderJoinOrderPay).
				Joins(orderJoinOrderRelation).
				Where(tableNameOrderRelation+"."+model.OrderRelationColumns.Action()+" IN (?)", actions).
				Where(tableNameOrder+"."+model.OrderColumns.DeletedAt(), 0).
				Where(tableNameOrderRelation+"."+model.OrderRelationColumns.DeletedAt(), 0).
				Where(tableNameOrder+"."+model.OrderColumns.ApplyDisbursementAt()+" BETWEEN ? AND ? ", targetStart, targetEnd),
		).
		Select("user_id, COUNT(order_id) as total, SUM(amount) as amount").
		Group("user_id")

	if err := query.Scan(&results).Error; err != nil {
		return results, err
	}

	return results, nil
}

// CheckDailyDataExistsInRange 检查指定日期范围内的数据是否已存在
func (o *Order) CheckDailyDataExistsInRange(ctx context.Context, startDate, endDate time.Time) (bool, error) {
	// 计算开始日期和结束日期的时间范围
	startDateTime := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, startDate.Location())
	endDateTime := time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 999999999, endDate.Location())

	var count int64
	err := global.DB.WithContext(ctx).Model(&model.OrderStaticDaily{}).
		Where(model.OrderStaticDailyColumns.Day()+" BETWEEN ? AND ?", startDateTime, endDateTime).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// CheckDataExistsInDays 检查指定天数内的数据是否已存在
func (o *Order) CheckDataExistsInDays(ctx context.Context, days int) (bool, error) {
	now := time.Now()
	// 计算指定天数前的日期
	startDate := now.AddDate(0, 0, -days)
	endDate := now.AddDate(0, 0, -1) // 昨天

	return o.CheckDailyDataExistsInRange(ctx, startDate, endDate)
}

// InsertHistoricalSalesData 批量插入历史数据
func (o *Order) InsertHistoricalSalesData(ctx context.Context, startDate, endDate time.Time) error {
	// 验证日期范围
	if startDate.After(endDate) {
		return fmt.Errorf("开始日期不能晚于结束日期")
	}

	// 限制处理范围，避免一次性处理过多数据
	maxDays := int32(365) // 最多处理365天
	currentDate := startDate
	endDateLimited := endDate

	// 计算实际处理的结束日期
	daysDiff := int32(endDate.Sub(startDate).Hours() / 24)
	if daysDiff > maxDays {
		endDateLimited = startDate.AddDate(0, 0, int(maxDays))
	}

	// 按日处理数据
	for !currentDate.After(endDateLimited) {
		// 检查数据是否已存在，如果存在则跳过
		targetStart := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(), 0, 0, 0, 0, currentDate.Location())
		targetEnd := targetStart.Add(24*time.Hour - time.Nanosecond)

		var count int64
		err := global.DB.WithContext(ctx).Model(&model.OrderStaticDaily{}).
			Where(model.OrderStaticDailyColumns.Day()+" BETWEEN ? AND ?", targetStart, targetEnd).
			Count(&count).Error

		if err != nil {
			currentDate = currentDate.AddDate(0, 0, 1)
			continue
		}

		// 如果数据已存在，跳过
		if count > 0 {
			currentDate = currentDate.AddDate(0, 0, 1)
			continue
		}

		// 插入新数据（直接内嵌 InsertDailySalesDataByDate 的逻辑）
		err = o.insertDailySalesDataByDate(ctx, currentDate)
		if err != nil {
			currentDate = currentDate.AddDate(0, 0, 1)
			continue
		}

		currentDate = currentDate.AddDate(0, 0, 1)
	}

	return nil
}

// insertDailySalesDataByDate 插入指定日期的数据（内部方法）
func (o *Order) insertDailySalesDataByDate(ctx context.Context, targetDate time.Time) error {
	// 新增订单
	resultsNew, err := o.GetSalesDataNewByDate(ctx, targetDate)
	if err != nil {
		return err
	}

	// 审核通过，首款
	resultsFirst, err := o.GetSalesDataFirstByDate(ctx, targetDate)
	if err != nil {
		return err
	}

	// 申请订单
	resultsDisbursement, err := o.GetSalesDataDisbursementByDate(ctx, targetDate)
	if err != nil {
		return err
	}

	lens := len(resultsNew) + len(resultsFirst) + len(resultsDisbursement)
	timeNow := time.Now()

	// 计算目标日期的开始时间（用于day字段）
	targetStart := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 0, 0, 0, 0, targetDate.Location())

	insertData := make([]model.OrderStaticDaily, 0, lens)

	for _, result := range resultsNew {
		insertData = append(insertData, model.OrderStaticDaily{
			ID:        0,
			UID:       result.UserId,
			Cate:      CateNew, // 类型#1%新增订单|2%审核通过|3%申请支款
			Num:       result.Total,
			Amount:    result.Amount,
			Day:       targetStart,
			CreatedAt: timeNow,
		})
	}

	for _, result := range resultsFirst {
		insertData = append(insertData, model.OrderStaticDaily{
			ID:        0,
			UID:       result.UserId,
			Cate:      CateFirst, // 类型#1%新增订单|2%审核通过|3%申请支款
			Num:       result.Total,
			Amount:    result.Amount,
			Day:       targetStart,
			CreatedAt: timeNow,
		})
	}

	for _, result := range resultsDisbursement {
		insertData = append(insertData, model.OrderStaticDaily{
			ID:        0,
			UID:       result.UserId,
			Cate:      CateDisbursement, // 类型#1%新增订单|2%审核通过|3%申请支款
			Num:       result.Total,
			Amount:    result.Amount,
			Day:       targetStart,
			CreatedAt: timeNow,
		})
	}

	// 批量插入（每batchSize条一个批次）
	err = global.DB.WithContext(ctx).Debug().CreateInBatches(insertData, BatchInsertStaticDailySize).Error
	if err != nil {
		return err
	}

	return nil
}

// CheckAndBackfillLastNMonths 检查并补录最近N个月的数据
// months: 月数，例如2表示最近2个月
func (o *Order) CheckAndBackfillLastNMonths(ctx context.Context, months int) error {
	now := time.Now()
	// 计算开始日期和结束日期
	startDate := now.AddDate(0, -months, 0) // N个月前
	endDate := now.AddDate(0, 0, -1)        // 昨天（不包括今天）

	return o.InsertHistoricalSalesData(ctx, startDate, endDate)
}
