package models

import (
	"context"
	"time"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/utils"

	"gorm.io/gorm"
)

// CollectionStatsAggregated 收款统计聚合结果
type CollectionStatsAggregated struct {
	TotalNewPendingCount       int64   `json:"total_new_pending_count"`
	TotalApprovedCount         int64   `json:"total_approved_count"`
	TotalRejectedCount         int64   `json:"total_rejected_count"`
	TotalApproveDurationHours  float64 `json:"total_approve_duration_hours"`
	TotalApproveCompletedCount int64   `json:"total_approve_completed_count"`
}

// DisbursementApprovalStatsAggregated 支付审核统计聚合结果
type DisbursementApprovalStatsAggregated struct {
	TotalNewPendingCount       int64   `json:"total_new_pending_count"`
	TotalApprovedCount         int64   `json:"total_approved_count"`
	TotalRejectedCount         int64   `json:"total_rejected_count"`
	TotalApproveDurationHours  float64 `json:"total_approve_duration_hours"`
	TotalApproveCompletedCount int64   `json:"total_approve_completed_count"`
}

// DisbursementPaymentStatsAggregated 支付打款统计聚合结果
type DisbursementPaymentStatsAggregated struct {
	TotalNewPendingPaymentCount int64   `json:"total_new_pending_payment_count"`
	TotalPaymentSuccessCount    int64   `json:"total_payment_success_count"`
	TotalPaymentRejectedCount   int64   `json:"total_payment_rejected_count"`
	TotalPaymentTimeoutCount    int64   `json:"total_payment_timeout_count"`
	TotalPaymentDurationHours   float64 `json:"total_payment_duration_hours"`
	TotalPaymentCompletedCount  int64   `json:"total_payment_completed_count"`
}

// GetCollectionStatsFromDB retrieves aggregated collection statistics from the database.
func GetCollectionStatsFromDB(ctx context.Context, db *gorm.DB, startDate, endDate time.Time, brandIDs []int64, accountIDs []int64, accountTypes []int32, staffIds []int64) (*CollectionStatsAggregated, error) {
	var result CollectionStatsAggregated

	query := db.WithContext(ctx).Table("financial_fund_daily_stats").
		Select(`
			SUM(new_pending_count) as total_new_pending_count,
			SUM(approved_count) as total_approved_count,
			SUM(rejected_count) as total_rejected_count,
			SUM(COALESCE(total_approve_duration_hours, 0.0)) as total_approve_duration_hours,
			SUM(approve_completed_count) as total_approve_completed_count
		`).
		Where("stat_date BETWEEN ? AND ?", startDate, endDate)

	if len(brandIDs) > 0 {
		query = query.Where("brand_id IN (?)", brandIDs)
	}
	if len(accountIDs) > 0 {
		query = query.Where("account_id IN (?)", accountIDs)
	}
	if len(accountTypes) > 0 {
		query = query.Where("account_type IN (?)", accountTypes)
	}

	// 权限控制：只有超管或空权限控制时才不过滤
	if len(staffIds) > 0 {
		query = query.Where("handle_by_staff_id IN (?) OR order_staff_id IN (?)",
			staffIds, staffIds)
	}

	err := query.Scan(&result).Error
	return &result, err
}

// GetDisbursementApprovalStatsFromDB retrieves aggregated disbursement approval statistics from the database.
func GetDisbursementApprovalStatsFromDB(ctx context.Context, db *gorm.DB, startDate, endDate time.Time, refundTypes []int32, staffIds []int64) (*DisbursementApprovalStatsAggregated, error) {
	var result DisbursementApprovalStatsAggregated

	query := db.WithContext(ctx).Table("financial_refund_approve_daily_stats").
		Select(`
			SUM(new_pending_count) as total_new_pending_count,
			SUM(approved_count) as total_approved_count,
			SUM(rejected_count) as total_rejected_count,
			SUM(COALESCE(total_approve_duration_hours, 0.0)) as total_approve_duration_hours,
			SUM(approve_completed_count) as total_approve_completed_count
		`).
		Where("stat_date BETWEEN ? AND ?", startDate, endDate)

	if len(refundTypes) > 0 {
		query = query.Where("refund_type IN (?)", refundTypes)
	}

	// 权限控制：只有超管或空权限控制时才不过滤
	if len(staffIds) > 0 {
		query = query.Where("handle_by_staff_id IN (?) OR order_staff_id IN (?)",
			staffIds, staffIds)
	}

	err := query.Scan(&result).Error
	return &result, err
}

// GetDisbursementPaymentStatsFromDB retrieves aggregated disbursement payment statistics from the database.
func GetDisbursementPaymentStatsFromDB(ctx context.Context, db *gorm.DB, startDate, endDate time.Time, refundTypes []int32, staffIds []int64) (*DisbursementPaymentStatsAggregated, error) {
	var result DisbursementPaymentStatsAggregated

	query := db.WithContext(ctx).Table("financial_refund_payment_daily_stats").
		Select(`
			SUM(new_pending_payment_count) as total_new_pending_payment_count,
			SUM(payment_success_count) as total_payment_success_count,
			SUM(payment_rejected_count) as total_payment_rejected_count,
			SUM(payment_timeout_count) as total_payment_timeout_count,
			SUM(COALESCE(total_payment_duration_hours, 0.0)) as total_payment_duration_hours,
			SUM(payment_completed_count) as total_payment_completed_count
		`).
		Where("stat_date BETWEEN ? AND ?", startDate, endDate)

	if len(refundTypes) > 0 {
		query = query.Where("refund_type IN (?)", refundTypes)
	}

	// 权限控制：只有超管或空权限控制时才不过滤
	if len(staffIds) > 0 {
		query = query.Where("handle_by_staff_id IN (?) OR order_staff_id IN (?)",
			staffIds, staffIds)
	}

	err := query.Scan(&result).Error
	return &result, err
}

// FundPendingStatsRequest 收款单待审核统计请求参数
type FundPendingStatsRequest struct {
	BrandIds            []int64 `json:"brand_ids"`
	PaymentAccountTypes []int32 `json:"payment_account_types"`
	PaymentAccounts     []int64 `json:"payment_accounts"`
	StaffIds            []int64 `json:"staff_ids"`
}

// FundPendingStatsResponse 收款单待审核统计响应
type FundPendingStatsResponse struct {
	NewPendingReview        int32 `json:"new_pending_review"`
	RejectedResubmitPending int32 `json:"rejected_resubmit_pending"`
	OverduePendingReview    int32 `json:"overdue_pending_review"`
}

// RefundPendingStatsRequest 支款单待审核统计请求参数
type RefundPendingStatsRequest struct {
	RefundTypes []int32 `json:"refund_types"`
	StaffIds    []int64 `json:"staff_ids"`
}

// RefundPendingStatsResponse 支款单待审核统计响应
type RefundPendingStatsResponse struct {
	NewPendingReview        int32 `json:"new_pending_review"`
	RejectedResubmitPending int32 `json:"rejected_resubmit_pending"`
	OverduePendingReview    int32 `json:"overdue_pending_review"`
	PendingPayment          int32 `json:"pending_payment"`
	NearDeadlinePayment     int32 `json:"near_deadline_payment"`
}

// GetFundPendingStatsFromDB 获取收款单待审核统计数据
func GetFundPendingStatsFromDB(ctx context.Context, db *gorm.DB, req *FundPendingStatsRequest) (*FundPendingStatsResponse, error) {
	// 构建基本查询条件
	baseDB := db.WithContext(ctx).Model(&model.FinancialFund{})

	// 添加筛选条件
	if len(req.BrandIds) > 0 {
		baseDB = baseDB.Joins("LEFT JOIN order_goods ON financial_fund.order_id = order_goods.order_id").
			Joins("LEFT JOIN products ON order_goods.goods_id = products.id").
			Where("products.brand_id IN (?)", req.BrandIds)
	}

	if len(req.PaymentAccountTypes) > 0 {
		baseDB = baseDB.Joins("LEFT JOIN financial_paid ON financial_fund.id = financial_paid.financial_fund_id").
			Where("financial_paid.deleted_at = 0 AND financial_paid.paid_type IN (?)", req.PaymentAccountTypes)
	}

	if len(req.PaymentAccounts) > 0 {
		// 如果前面没有JOIN financial_paid表，则需要JOIN
		if len(req.PaymentAccountTypes) == 0 {
			baseDB = baseDB.Joins("LEFT JOIN financial_paid ON financial_fund.id = financial_paid.financial_fund_id").
				Where("financial_paid.deleted_at = 0")
		}
		baseDB = baseDB.Where("financial_paid.payment_account_id IN (?)", req.PaymentAccounts)
	}

	// 权限控制
	if len(req.StaffIds) > 0 {
		// 查订单来源。或的关系
		baseDB = baseDB.Joins("LEFT JOIN order_relation as order_auth ON financial_fund.order_id = order_auth.order_id AND order_auth.action IN (?) and order_auth.deleted_at = 0", []int{6, 7}).
			// 查客户来源，或的关系
			Joins("LEFT JOIN customer_referral_relationship as customer_auth ON financial_fund.customer_id = customer_auth.customer_id").
			Where("financial_fund.handle_by IN (?) OR order_auth.user_id IN (?) OR customer_auth.admin_user_id IN (?)",
				req.StaffIds, req.StaffIds, req.StaffIds)
	}

	resp := &FundPendingStatsResponse{}

	// 1. 新增待审核数量：状态为待审批且没有审核记录的收款单
	var newPendingCount int64
	newPendingDB := baseDB.Session(&gorm.Session{}).Where("financial_fund.approve_status = ?", 1)
	// 排除有审核记录的
	newPendingDB = newPendingDB.Where("NOT EXISTS (SELECT 1 FROM financial_fund_approve_log WHERE financial_fund_approve_log.financial_fund_id = financial_fund.id)")
	newPendingDB.Group("financial_fund.id").Count(&newPendingCount)

	// 2. 驳回重新提交待审核数量：有过驳回记录且当前状态为待审批的收款单
	var rejectedResubmitCount int64
	rejectedResubmitDB := baseDB.Session(&gorm.Session{}).Where("financial_fund.approve_status = ?", 1)
	// 存在驳回记录
	rejectedResubmitDB = rejectedResubmitDB.Where("EXISTS (SELECT 1 FROM financial_fund_approve_log WHERE financial_fund_approve_log.financial_fund_id = financial_fund.id AND financial_fund_approve_log.status = ?)", 4)
	rejectedResubmitDB.Group("financial_fund.id").Count(&rejectedResubmitCount)

	// 3. 超3个工作日未审核数量：待审批状态且从相关时间点开始超过3个工作日
	// 需要考虑两种情况：
	// - 直接待审核的：从创建时间开始计算
	// - 审核拒绝后转成待审核的：从最后一次审核时间开始计算
	var overdueCount int64
	now := time.Now()
	threeWorkdaysAgo := utils.SubWorkdays(now, 3)

	// 使用子查询获取每个收款单的有效起始时间（创建时间或最后审核时间的较大值）
	overdueDB := baseDB.Session(&gorm.Session{}).Where("financial_fund.approve_status = ?", 1)
	overdueDB = overdueDB.Where(`
		COALESCE(
			(SELECT MAX(financial_fund_approve_log.created_at)
			 FROM financial_fund_approve_log
			 WHERE financial_fund_approve_log.financial_fund_id = financial_fund.id),
			financial_fund.created_at
		) < ?`, threeWorkdaysAgo)
	overdueDB.Group("financial_fund.id").Count(&overdueCount)

	// 设置返回结果
	resp.NewPendingReview = int32(newPendingCount)
	resp.RejectedResubmitPending = int32(rejectedResubmitCount)
	resp.OverduePendingReview = int32(overdueCount)

	return resp, nil
}

// GetRefundPendingStatsFromDB 获取支款单待审核统计数据
func GetRefundPendingStatsFromDB(ctx context.Context, db *gorm.DB, req *RefundPendingStatsRequest) (*RefundPendingStatsResponse, error) {
	// 构建基本查询条件
	baseDB := db.WithContext(ctx).Model(&model.FinancialRefund{})

	// 添加筛选条件
	if len(req.RefundTypes) > 0 {
		baseDB = baseDB.Where("financial_refund.refund_type IN (?)", req.RefundTypes)
	}

	// 权限控制
	if len(req.StaffIds) > 0 {
		// 查订单来源。或的关系
		baseDB = baseDB.Joins("LEFT JOIN order_relation as order_auth ON financial_refund.order_id = order_auth.order_id AND order_auth.action IN (?) and order_auth.deleted_at = 0", []int{6, 7}).
			// 查客户来源，或的关系
			Joins("LEFT JOIN customer_referral_relationship as customer_auth ON financial_refund.customer_id = customer_auth.customer_id").
			Where("financial_refund.handle_by IN (?) OR order_auth.user_id IN (?) OR customer_auth.admin_user_id IN (?)",
				req.StaffIds, req.StaffIds, req.StaffIds)
	}

	resp := &RefundPendingStatsResponse{}

	// 1. 新增待审核数量：状态为待审批且没有审核记录的支款单
	var newPendingCount int64
	newPendingDB := baseDB.Session(&gorm.Session{}).Where("financial_refund.approve_status = ?", 1)
	// 排除有审核记录的
	newPendingDB = newPendingDB.Where("NOT EXISTS (SELECT 1 FROM financial_refund_approve_log WHERE financial_refund_approve_log.financial_refund_id = financial_refund.id)")
	newPendingDB.Group("financial_refund.id").Count(&newPendingCount)

	// 2. 驳回重新提交待审核数量：有过驳回记录且当前状态为待审批的支款单
	var rejectedResubmitCount int64
	rejectedResubmitDB := baseDB.Session(&gorm.Session{}).Where("financial_refund.approve_status = ?", 1)
	// 存在驳回记录
	rejectedResubmitDB = rejectedResubmitDB.Where("EXISTS (SELECT 1 FROM financial_refund_approve_log WHERE financial_refund_approve_log.financial_refund_id = financial_refund.id AND financial_refund_approve_log.status = ?)", 4)
	rejectedResubmitDB.Group("financial_refund.id").Count(&rejectedResubmitCount)

	// 3. 超3个工作日未审核数量：待审批状态且从相关时间点开始超过3个工作日
	// 需要考虑两种情况：
	// - 直接待审核的：从创建时间开始计算
	// - 审核拒绝后转成待审核的：从最后一次审核时间开始计算
	var overdueCount int64
	now := time.Now()
	threeWorkdaysAgo := utils.SubWorkdays(now, 3)

	// 使用子查询获取每个支款单的有效起始时间（创建时间或最后审核时间的较大值）
	overdueDB := baseDB.Session(&gorm.Session{}).Where("financial_refund.approve_status = ?", 1)
	overdueDB = overdueDB.Where(`
		COALESCE(
			(SELECT MAX(financial_refund_approve_log.created_at)
			 FROM financial_refund_approve_log
			 WHERE financial_refund_approve_log.financial_refund_id = financial_refund.id),
			financial_refund.created_at
		) < ?`, threeWorkdaysAgo)
	overdueDB.Group("financial_refund.id").Count(&overdueCount)

	// 4. 待支款数量：状态为待支款的支款单
	var pendingPaymentCount int64
	pendingPaymentDB := baseDB.Session(&gorm.Session{}).Where("financial_refund.approve_status = ?", 2)
	pendingPaymentDB.Group("financial_refund.id").Count(&pendingPaymentCount)

	// 5. 临近支款截止日期数量：距离支款截止日期不足3日的支款单
	var nearDeadlineCount int64
	threeDaysLater := now.AddDate(0, 0, 3)
	nearDeadlineDB := baseDB.Session(&gorm.Session{}).
		Where("financial_refund.approve_status IN (?) AND financial_refund.refund_deadline IS NOT NULL AND financial_refund.refund_deadline BETWEEN ? AND ?",
			[]int{1, 2}, now, threeDaysLater)
	nearDeadlineDB.Group("financial_refund.id").Count(&nearDeadlineCount)

	// 设置返回结果
	resp.NewPendingReview = int32(newPendingCount)
	resp.RejectedResubmitPending = int32(rejectedResubmitCount)
	resp.OverduePendingReview = int32(overdueCount)
	resp.PendingPayment = int32(pendingPaymentCount)
	resp.NearDeadlinePayment = int32(nearDeadlineCount)

	return resp, nil
}
