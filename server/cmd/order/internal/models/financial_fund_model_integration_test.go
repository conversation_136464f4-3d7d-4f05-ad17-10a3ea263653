package models

import (
	"context"
	"errors"
	"fmt"
	"os"
	"testing"
	"time"

	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/pkg/mysql"
	"uofferv2/pkg/nacos"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/order/internal/global"

	"github.com/bytedance/sonic"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
)

// FinancialFundModelIntegrationTestSuite 模型层集成测试套件
type FinancialFundModelIntegrationTestSuite struct {
	suite.Suite
	db  *gorm.DB
	ctx context.Context
}

func (suite *FinancialFundModelIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	logger.InitLogger("debug", "console", logger.LogSize(10*1024*1024))

	err := suite.initConfigFromNacos()
	if err != nil {
		suite.T().Skipf("跳过集成测试: 无法连接 nacos 或数据库配置错误: %v", err)
		return
	}

	suite.db, err = mysql.InitDB(global.ServerConfig.MysqlInfo)
	if err != nil {
		suite.T().Skipf("跳过集成测试: 无法连接数据库: %v", err)
		return
	}

	global.DB = suite.db
	suite.T().Log("模型层集成测试环境初始化成功")
}

func (suite *FinancialFundModelIntegrationTestSuite) initConfigFromNacos() error {
	dataId := "order"
	accountGroup := "svc"
	configPath := utils.GetConfigPath("config/nacos_config.yaml", "NACOS_CONFIG_PATH")
	fmt.Println("configPath", configPath)

	content, err := nacos.InitNacos(dataId, accountGroup, configPath)
	if err != nil {
		return err
	}

	err = sonic.Unmarshal([]byte(content), &global.ServerConfig)
	if err != nil {
		return err
	}

	return nil
}

func (suite *FinancialFundModelIntegrationTestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		if sqlDB != nil {
			sqlDB.Close()
		}
	}
}

func (suite *FinancialFundModelIntegrationTestSuite) SetupTest() {
	if suite.db == nil {
		suite.T().Skip("数据库未初始化，跳过测试")
	}
}

// createTestFund 创建一个用于测试的收款单
func (suite *FinancialFundModelIntegrationTestSuite) createTestFund() *model.FinancialFund {
	fund := &model.FinancialFund{
		OrderNo:       fmt.Sprintf("MODEL_TEST_%d", time.Now().UnixNano()),
		FundNo:        fmt.Sprintf("MFUND_%d", time.Now().UnixNano()),
		OrderID:       1,
		CustomerID:    1,
		FundType:      FundTypeDeposit,
		ApproveStatus: FundApproveStatusPending,
		RealAmountRmb: "100.00000",
		ContractURL:   "[]",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	err := suite.db.Create(fund).Error
	assert.NoError(suite.T(), err)
	return fund
}

// cleanupTestFund 清理测试数据
func (suite *FinancialFundModelIntegrationTestSuite) cleanupTestFund(id int64) {
	suite.db.Delete(&model.FinancialFund{}, id)
}

func (suite *FinancialFundModelIntegrationTestSuite) TestFundInfo() {
	testFund := suite.createTestFund()
	defer suite.cleanupTestFund(testFund.ID)

	// 测试通过 ID 查询
	info, err := FundInfo(suite.ctx, &FundInfoRequest{Id: testFund.ID})
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), info)
	assert.Equal(suite.T(), testFund.ID, info.ID)

	// 测试通过 FundNo 查询
	info, err = FundInfo(suite.ctx, &FundInfoRequest{FundNo: testFund.FundNo})
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), info)
	assert.Equal(suite.T(), testFund.ID, info.ID)

	// 测试无结果查询
	info, err = FundInfo(suite.ctx, &FundInfoRequest{Id: 99999999})
	assert.Error(suite.T(), err)
	assert.True(suite.T(), errors.Is(err, gorm.ErrRecordNotFound))

	// 测试无参数查询
	info, err = FundInfo(suite.ctx, &FundInfoRequest{})
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(0), info.ID) // 应该返回一个空对象
}

func (suite *FinancialFundModelIntegrationTestSuite) TestFundCreateWithTx() {
	tx := suite.db.Begin()
	assert.NoError(suite.T(), tx.Error)

	newFund := &model.FinancialFund{
		OrderNo:       fmt.Sprintf("TX_TEST_%d", time.Now().UnixNano()),
		FundNo:        fmt.Sprintf("TXFUND_%d", time.Now().UnixNano()),
		OrderID:       2,
		CustomerID:    2,
		FundType:      FundTypeFirstPayment,
		ApproveStatus: FundApproveStatusPending,
		RealAmountRmb: "200.00000",
		ContractURL:   "[]",
	}

	createdID, err := FundCreate(suite.ctx, tx, newFund)
	assert.NoError(suite.T(), err)
	assert.Greater(suite.T(), createdID, int64(0))

	// 在事务提交前，主数据库中应该查询不到
	var fund model.FinancialFund
	err = suite.db.Where("id = ?", createdID).First(&fund).Error
	assert.Error(suite.T(), err)
	assert.True(suite.T(), errors.Is(err, gorm.ErrRecordNotFound))

	// 提交事务
	err = tx.Commit().Error
	assert.NoError(suite.T(), err)

	// 事务提交后，应该能查询到
	err = suite.db.Where("id = ?", createdID).First(&fund).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), newFund.OrderNo, fund.OrderNo)

	// 清理数据
	suite.cleanupTestFund(createdID)
}

func (suite *FinancialFundModelIntegrationTestSuite) TestFundUpdateWithTx() {
	testFund := suite.createTestFund()
	defer suite.cleanupTestFund(testFund.ID)

	// 1. 测试事务回滚
	txRollback := suite.db.Begin()
	assert.NoError(suite.T(), txRollback.Error)

	updateData := &model.FinancialFund{ID: testFund.ID, RealAmountRmb: "999.00000"}
	_, err := FundUpdate(suite.ctx, txRollback, updateData)
	assert.NoError(suite.T(), err)

	txRollback.Rollback()

	// 验证数据未被更新
	info, _ := FundInfo(suite.ctx, &FundInfoRequest{Id: testFund.ID})
	assert.Equal(suite.T(), "100.00000", info.RealAmountRmb)

	// 2. 测试事务提交
	txCommit := suite.db.Begin()
	assert.NoError(suite.T(), txCommit.Error)

	_, err = FundUpdate(suite.ctx, txCommit, updateData)
	assert.NoError(suite.T(), err)

	err = txCommit.Commit().Error
	assert.NoError(suite.T(), err)

	// 验证数据已被更新
	info, _ = FundInfo(suite.ctx, &FundInfoRequest{Id: testFund.ID})
	assert.Equal(suite.T(), "999.00000", info.RealAmountRmb)
}

func (suite *FinancialFundModelIntegrationTestSuite) TestFundDeleteWithTx() {
	testFund := suite.createTestFund()
	// 不在这里 defer cleanup，因为我们要验证它是否被删除

	// 1. 测试事务回滚
	txRollback := suite.db.Begin()
	assert.NoError(suite.T(), txRollback.Error)

	err := FundDelete(suite.ctx, txRollback, testFund.ID)
	assert.NoError(suite.T(), err)

	txRollback.Rollback()

	// 验证数据未被删除
	info, err := FundInfo(suite.ctx, &FundInfoRequest{Id: testFund.ID})
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), info)

	// 2. 测试事务提交
	txCommit := suite.db.Begin()
	assert.NoError(suite.T(), txCommit.Error)

	err = FundDelete(suite.ctx, txCommit, testFund.ID)
	assert.NoError(suite.T(), err)

	err = txCommit.Commit().Error
	assert.NoError(suite.T(), err)

	// 验证数据已被删除
	_, err = FundInfo(suite.ctx, &FundInfoRequest{Id: testFund.ID})
	assert.Error(suite.T(), err)
	assert.True(suite.T(), errors.Is(err, gorm.ErrRecordNotFound))
}

func TestFinancialFundModelIntegrationTestSuite(t *testing.T) {
	if os.Getenv("INTEGRATION_TEST") != "true" {
		t.Skip("跳过集成测试: 设置环境变量 INTEGRATION_TEST=true 来启用")
	}
	suite.Run(t, new(FinancialFundModelIntegrationTestSuite))
}
