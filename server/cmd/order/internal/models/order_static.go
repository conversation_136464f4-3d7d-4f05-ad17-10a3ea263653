package models

import (
	"context"
	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/order/internal/global"
)

var (
	// COUNT(*) AS counts, customer_id, `status`, `status_review`
	orderStaticColumns = " COUNT(*) AS num, " + model.OrderColumns.CustomerID() + ",  " +
		model.OrderColumns.Status() + ", " + model.OrderColumns.StatusReview()

	// COUNT(*) AS counts, customer_id, `status`, `status_review`
	orderStaticEmployeeColumns = " COUNT(*) AS num, " +
		model.OrderColumns.Status() + ", " + model.OrderColumns.StatusReview()

	// GROUP BY customer_id, `status`, `status_review`
	orderStaticGroupBy = model.OrderColumns.CustomerID() + ", " +
		model.OrderColumns.Status() + ", " +
		model.OrderColumns.StatusReview()

	// GROUP BY `status`, `status_review`
	orderStaticEmployeeGroupBy = model.OrderColumns.Status() + ", " + model.OrderColumns.StatusReview()
)

// GetFinalWorkflowProcess 通过用户获取尾款待支付-尾款待提交的订单数量
//
//	Description: 尾款待支付-服务未完成
//
// status = 3 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
// status_reviews = 2 审核状态#1%审核通过|2%草稿|3%草稿待审核|4%驳回待审核|5%审核驳回
// status_workflow = 4,5 工单状态(0:UNKNOWN, 1:待接收, 2:服务中, 3:服务暂停, 4:服务完成, 5:服务终止)
//
//	receiver o
//	param ctx
//	param customerId
//	return count
//	return err
func (o *Order) GetFinalWorkflowProcess(ctx context.Context, customerId int64) (count int64, err error) {
	workflowInProcessSli := []StatusOrderWorkflow{
		StatusOrderWorkflowPending,
		StatusOrderWorkflowInService,
		StatusOrderWorkflowInPaused,
	}
	err = global.DB.WithContext(ctx).Debug().
		Model(&model.Order{}).
		Where(model.OrderColumns.CustomerID(), customerId).
		Where(model.OrderColumns.Status(), StatusOrderFinal).
		Where(model.OrderColumns.StatusReview(), StatusReviewDraft).
		Where(model.OrderColumns.StatusWorkflow()+" IN (?)", workflowInProcessSli).
		Count(&count).Error

	if err != nil {
		return
	}

	return
}

func (o *Order) GetOrderStaticEmployee(ctx context.Context, customerIds, updaterIds []int64) (mods []*OrderStaticItem, err error) {

	mods = make([]*OrderStaticItem, 0)

	query := global.DB.WithContext(ctx).Debug().Model(&model.Order{})

	// order_relation
	subQuery := o.getOrderListQueryByRelation(ctx, []int64{}, updaterIds, []OrderRelationAction{})

	query = query.Where("EXISTS(?)", subQuery)

	if len(customerIds) > 0 {
		query.Where(tableNameOrder+"."+model.OrderColumns.CustomerID()+" IN (?)", customerIds)
	}

	err = query.Group(orderStaticEmployeeGroupBy).Select(orderStaticEmployeeColumns).Scan(&mods).Error

	if err != nil {
		return
	}

	return
}

// GetOrderDisbursementCountEmployee 通过用户获取支款订单-支款完成的订单数量
//
//	Description: 支款订单-支款完成
//
// status = 5 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
// status_reviews = 1 审核状态#1%审核通过|2%草稿|3%草稿待审核|4%驳回待审核|5%审核驳回
// status_pay_disbursements = 1 支款支付状态#1%已支付|2%待支付|3%支付中|4%支付失败
//
//	receiver o
//	param ctx
//	param customerId
//	return count
//	return err
func (o *Order) GetOrderDisbursementCountEmployee(ctx context.Context, customerIds, updaterIds []int64) (count int64, err error) {

	var (
		creatorIds = make([]int64, 0)
		actions    = make([]OrderRelationAction, 0)
	)

	query := global.DB.WithContext(ctx).Debug().Model(&model.Order{})

	// order_relation
	subQuery := o.getOrderListQueryByRelation(ctx, creatorIds, updaterIds, actions)

	query = query.Where("EXISTS(?)", subQuery)

	if len(customerIds) > 0 {
		query.Where(tableNameOrder+"."+model.OrderColumns.CustomerID()+" IN (?)", customerIds)
	}

	err = query.Where(tableNameOrder+"."+model.OrderColumns.Status(), StatusOrderDisbursement).
		Where(tableNameOrder+"."+model.OrderColumns.StatusReview(), StatusReviewPass).
		Where(tableNameOrder+"."+model.OrderColumns.StatusPayDisbursement(), StatusPayPaid).
		Count(&count).Error

	if err != nil {
		return
	}

	return
}

// GetFinalWorkflowProcessEmployee 通过用户获取尾款待支付-尾款待提交的订单数量
//
//	Description: 尾款待支付-服务未完成
//
// status = 3 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
// status_reviews = 2 审核状态#1%审核通过|2%草稿|3%草稿待审核|4%驳回待审核|5%审核驳回
// status_workflow = 4,5 工单状态(0:UNKNOWN, 1:待接收, 2:服务中, 3:服务暂停, 4:服务完成, 5:服务终止)
//
//	receiver o
//	param ctx
//	param customerId
//	return count
//	return err
func (o *Order) GetFinalWorkflowProcessEmployee(ctx context.Context, customerIds, updaterIds []int64) (count int64, err error) {

	var (
		workflowInProcessSli = []StatusOrderWorkflow{
			StatusOrderWorkflowPending,
			StatusOrderWorkflowInService,
			StatusOrderWorkflowInPaused,
		}

		creatorIds = make([]int64, 0)
		actions    = make([]OrderRelationAction, 0)
	)

	query := global.DB.WithContext(ctx).Debug().Model(&model.Order{})

	// order_relation
	subQuery := o.getOrderListQueryByRelation(ctx, creatorIds, updaterIds, actions)

	query = query.Where("EXISTS(?)", subQuery)

	if len(customerIds) > 0 {
		query.Where(tableNameOrder+"."+model.OrderColumns.CustomerID()+" IN (?)", customerIds)
	}

	err = query.Where(tableNameOrder+"."+model.OrderColumns.Status(), StatusOrderFinal).
		Where(tableNameOrder+"."+model.OrderColumns.StatusReview(), StatusReviewDraft).
		Where(tableNameOrder+"."+model.OrderColumns.StatusWorkflow()+" IN (?)", workflowInProcessSli).
		Count(&count).Error

	if err != nil {
		return
	}

	return
}

// GetFinalWorkflowCompletedEmployee 通过用户获取尾款待支付-尾款待提交的订单数量
//
//	Description: 尾款待支付-尾款待提交
//
// status = 3 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
// status_reviews = 2 审核状态#1%审核通过|2%草稿|3%草稿待审核|4%驳回待审核|5%审核驳回
// status_workflow = 4,5 工单状态(0:UNKNOWN, 1:待接收, 2:服务中, 3:服务暂停, 4:服务完成, 5:服务终止)
//
//	receiver o
//	param ctx
//	param customerId
//	return count
//	return err
func (o *Order) GetFinalWorkflowCompletedEmployee(ctx context.Context, customerIds, updaterIds []int64) (count int64, err error) {

	var (
		workflowCompletedSli = []StatusOrderWorkflow{
			StatusOrderWorkflowCompleted,
			StatusOrderWorkflowTerminated,
		}

		creatorIds = make([]int64, 0)
		actions    = make([]OrderRelationAction, 0)
	)

	query := global.DB.WithContext(ctx).Debug().Model(&model.Order{})

	// order_relation
	subQuery := o.getOrderListQueryByRelation(ctx, creatorIds, updaterIds, actions)

	query = query.Where("EXISTS(?)", subQuery)

	if len(customerIds) > 0 {
		query.Where(tableNameOrder+"."+model.OrderColumns.CustomerID()+" IN (?)", customerIds)
	}

	err = query.Where(tableNameOrder+"."+model.OrderColumns.Status(), StatusOrderFinal).
		Where(tableNameOrder+"."+model.OrderColumns.StatusReview(), StatusReviewDraft).
		Where(tableNameOrder+"."+model.OrderColumns.StatusWorkflow()+" IN (?)", workflowCompletedSli).
		Count(&count).Error

	if err != nil {
		return
	}

	return
}
