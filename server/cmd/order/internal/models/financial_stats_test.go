package models

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestCollectionStatsAggregation 测试收款统计聚合功能
func TestCollectionStatsAggregation(t *testing.T) {
	// 创建内存数据库用于测试
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	assert.NoError(t, err)

	// 创建测试表结构
	err = db.Exec(`
		CREATE TABLE financial_fund_daily_stats (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			stat_date DATE NOT NULL,
			brand_id BIGINT,
			account_type TINYINT,
			account_id BIGINT,
			handle_by_staff_id BIGINT DEFAULT 0,
			order_staff_id BIGINT DEFAULT 0,
			new_pending_count INT DEFAULT 0,
			approved_count INT DEFAULT 0,
			rejected_count INT DEFAULT 0,
			total_approve_duration_hours DECIMAL(15,2) DEFAULT 0.00,
			approve_completed_count INT DEFAULT 0,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`).Error
	assert.NoError(t, err)

	// 插入测试数据
	testData := []map[string]interface{}{
		{
			"stat_date":                    time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			"brand_id":                     1,
			"account_type":                 1,
			"account_id":                   100,
			"handle_by_staff_id":           123,
			"order_staff_id":               456,
			"new_pending_count":            10,
			"approved_count":               8,
			"rejected_count":               2,
			"total_approve_duration_hours": 24.5,
			"approve_completed_count":      10,
		},
		{
			"stat_date":                    time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
			"brand_id":                     1,
			"account_type":                 1,
			"account_id":                   100,
			"handle_by_staff_id":           123,
			"order_staff_id":               456,
			"new_pending_count":            15,
			"approved_count":               12,
			"rejected_count":               3,
			"total_approve_duration_hours": 36.0,
			"approve_completed_count":      15,
		},
		{
			"stat_date":                    time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC),
			"brand_id":                     1,
			"account_type":                 1,
			"account_id":                   100,
			"handle_by_staff_id":           123,
			"order_staff_id":               456,
			"new_pending_count":            5,
			"approved_count":               4,
			"rejected_count":               1,
			"total_approve_duration_hours": 12.25,
			"approve_completed_count":      5,
		},
		{
			"stat_date":                    time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			"brand_id":                     2, // 不同品牌的数据，不应该被包含在过滤结果中
			"account_type":                 1,
			"account_id":                   100,
			"handle_by_staff_id":           999,
			"order_staff_id":               888,
			"new_pending_count":            20,
			"approved_count":               18,
			"rejected_count":               2,
			"total_approve_duration_hours": 48.0,
			"approve_completed_count":      20,
		},
	}

	for _, data := range testData {
		err = db.Table("financial_fund_daily_stats").Create(data).Error
		assert.NoError(t, err)
	}

	ctx := context.Background()
	startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(2024, 1, 3, 23, 59, 59, 0, time.UTC)
	brandID := int64(1)

	// 调试：查看实际的SQL查询结果
	var debugResult []map[string]interface{}
	err = db.WithContext(ctx).Table("financial_fund_daily_stats").
		Select("*").
		Where("stat_date BETWEEN ? AND ?", startDate, endDate).
		Where("brand_id = ?", brandID).
		Find(&debugResult).Error
	assert.NoError(t, err)
	t.Logf("Filtered data: %+v", debugResult)

	// 测试聚合查询 - 不带权限过滤
	result, err := GetCollectionStatsFromDB(ctx, db, startDate, endDate, []int64{brandID}, nil, nil, []int64{})
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证聚合结果
	// 期望结果：品牌1的3天数据聚合
	// new_pending_count: 10 + 15 + 5 = 30
	// approved_count: 8 + 12 + 4 = 24
	// rejected_count: 2 + 3 + 1 = 6
	// total_approve_duration_hours: 24.5 + 36.0 + 12.25 = 72.75
	// approve_completed_count: 10 + 15 + 5 = 30
	assert.Equal(t, int64(30), result.TotalNewPendingCount)
	assert.Equal(t, int64(24), result.TotalApprovedCount)
	assert.Equal(t, int64(6), result.TotalRejectedCount)
	assert.Equal(t, 72.75, result.TotalApproveDurationHours)
	assert.Equal(t, int64(30), result.TotalApproveCompletedCount)

	// 调试：查看聚合查询的SQL
	t.Logf("Aggregated result for brand 1: %+v", result)

	// 测试不带品牌过滤的查询（应该包含所有品牌）
	resultAllBrands, err := GetCollectionStatsFromDB(ctx, db, startDate, endDate, nil, nil, nil, []int64{})
	assert.NoError(t, err)
	assert.NotNil(t, resultAllBrands)

	// 调试：查看所有品牌的结果
	t.Logf("Aggregated result for all brands: %+v", resultAllBrands)

	// 验证包含所有品牌的结果
	// 应该包含品牌2的数据：new_pending_count增加20
	assert.Equal(t, int64(50), resultAllBrands.TotalNewPendingCount) // 30 + 20
	assert.Equal(t, int64(42), resultAllBrands.TotalApprovedCount)   // 24 + 18

	// 测试权限过滤查询
	staffIds := []int64{123, 456}
	resultWithPermission, err := GetCollectionStatsFromDB(ctx, db, startDate, endDate, []int64{brandID}, nil, nil, staffIds)
	assert.NoError(t, err)
	assert.NotNil(t, resultWithPermission)

	// 验证权限过滤结果（应该只包含staff_id为123或456的数据）
	assert.Equal(t, int64(30), resultWithPermission.TotalNewPendingCount) // 品牌1的数据
	assert.Equal(t, int64(24), resultWithPermission.TotalApprovedCount)   // 品牌1的数据

	// 测试权限过滤 - 不存在的员工ID
	nonExistentStaffIds := []int64{999999}
	resultNoPermission, err := GetCollectionStatsFromDB(ctx, db, startDate, endDate, []int64{brandID}, nil, nil, nonExistentStaffIds)
	assert.NoError(t, err)
	assert.NotNil(t, resultNoPermission)

	// 应该返回0，因为没有匹配的权限数据
	assert.Equal(t, int64(0), resultNoPermission.TotalNewPendingCount)
	assert.Equal(t, int64(0), resultNoPermission.TotalApprovedCount)
}

// TestDisbursementApprovalStatsAggregation 测试支付审核统计聚合功能
func TestDisbursementApprovalStatsAggregation(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	assert.NoError(t, err)

	// 创建测试表结构
	err = db.Exec(`
		CREATE TABLE financial_refund_approve_daily_stats (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			stat_date DATE NOT NULL,
			refund_type TINYINT,
			handle_by_staff_id BIGINT DEFAULT 0,
			order_staff_id BIGINT DEFAULT 0,
			new_pending_count INT DEFAULT 0,
			approved_count INT DEFAULT 0,
			rejected_count INT DEFAULT 0,
			total_approve_duration_hours DECIMAL(15,2) DEFAULT 0.00,
			approve_completed_count INT DEFAULT 0,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`).Error
	assert.NoError(t, err)

	// 插入测试数据，包含NULL值用于测试COALESCE功能
	testData := []map[string]interface{}{
		{
			"stat_date":                    time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			"refund_type":                  1,
			"handle_by_staff_id":           123,
			"order_staff_id":               456,
			"new_pending_count":            5,
			"approved_count":               4,
			"rejected_count":               1,
			"total_approve_duration_hours": 12.5,
			"approve_completed_count":      5,
		},
		{
			"stat_date":                    time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
			"refund_type":                  1,
			"handle_by_staff_id":           123,
			"order_staff_id":               456,
			"new_pending_count":            8,
			"approved_count":               6,
			"rejected_count":               2,
			"total_approve_duration_hours": nil, // 测试NULL值处理
			"approve_completed_count":      8,
		},
	}

	for _, data := range testData {
		err = db.Table("financial_refund_approve_daily_stats").Create(data).Error
		assert.NoError(t, err)
	}

	ctx := context.Background()
	startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(2024, 1, 2, 23, 59, 59, 0, time.UTC)
	refundType := int32(1)

	result, err := GetDisbursementApprovalStatsFromDB(ctx, db, startDate, endDate, []int32{refundType}, []int64{})
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证聚合结果，特别是NULL值被正确处理为0
	assert.Equal(t, int64(13), result.TotalNewPendingCount)       // 5 + 8
	assert.Equal(t, int64(10), result.TotalApprovedCount)         // 4 + 6
	assert.Equal(t, int64(3), result.TotalRejectedCount)          // 1 + 2
	assert.Equal(t, 12.5, result.TotalApproveDurationHours)       // 12.5 + 0 (NULL被处理为0)
	assert.Equal(t, int64(13), result.TotalApproveCompletedCount) // 5 + 8

	// 测试权限过滤
	staffIds := []int64{123, 456}
	resultWithPermission, err := GetDisbursementApprovalStatsFromDB(ctx, db, startDate, endDate, []int32{refundType}, staffIds)
	assert.NoError(t, err)
	assert.NotNil(t, resultWithPermission)

	// 验证权限过滤结果
	assert.Equal(t, int64(13), resultWithPermission.TotalNewPendingCount)
	assert.Equal(t, int64(10), resultWithPermission.TotalApprovedCount)
}

// TestDisbursementPaymentStatsAggregation 测试支付打款统计聚合功能
func TestDisbursementPaymentStatsAggregation(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	assert.NoError(t, err)

	// 创建测试表结构
	err = db.Exec(`
		CREATE TABLE financial_refund_payment_daily_stats (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			stat_date DATE NOT NULL,
			refund_type TINYINT,
			handle_by_staff_id BIGINT DEFAULT 0,
			order_staff_id BIGINT DEFAULT 0,
			new_pending_payment_count INT DEFAULT 0,
			payment_success_count INT DEFAULT 0,
			payment_rejected_count INT DEFAULT 0,
			payment_timeout_count INT DEFAULT 0,
			total_payment_duration_hours DECIMAL(15,2) DEFAULT 0.00,
			payment_completed_count INT DEFAULT 0,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`).Error
	assert.NoError(t, err)

	// 插入测试数据，包含NULL值
	testData := []map[string]interface{}{
		{
			"stat_date":                    time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			"refund_type":                  1,
			"handle_by_staff_id":           123,
			"order_staff_id":               456,
			"new_pending_payment_count":    10,
			"payment_success_count":        8,
			"payment_rejected_count":       1,
			"payment_timeout_count":        1,
			"total_payment_duration_hours": 48.5,
			"payment_completed_count":      10,
		},
		{
			"stat_date":                    time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
			"refund_type":                  1,
			"handle_by_staff_id":           123,
			"order_staff_id":               456,
			"new_pending_payment_count":    15,
			"payment_success_count":        12,
			"payment_rejected_count":       2,
			"payment_timeout_count":        1,
			"total_payment_duration_hours": nil, // 测试NULL值处理
			"payment_completed_count":      15,
		},
	}

	for _, data := range testData {
		err = db.Table("financial_refund_payment_daily_stats").Create(data).Error
		assert.NoError(t, err)
	}

	ctx := context.Background()
	startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(2024, 1, 2, 23, 59, 59, 0, time.UTC)
	refundType := int32(1)

	result, err := GetDisbursementPaymentStatsFromDB(ctx, db, startDate, endDate, []int32{refundType}, []int64{})
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证聚合结果
	assert.Equal(t, int64(25), result.TotalNewPendingPaymentCount) // 10 + 15
	assert.Equal(t, int64(20), result.TotalPaymentSuccessCount)    // 8 + 12
	assert.Equal(t, int64(3), result.TotalPaymentRejectedCount)    // 1 + 2
	assert.Equal(t, int64(2), result.TotalPaymentTimeoutCount)     // 1 + 1
	assert.Equal(t, 48.5, result.TotalPaymentDurationHours)        // 48.5 + 0 (NULL被处理为0)
	assert.Equal(t, int64(25), result.TotalPaymentCompletedCount)  // 10 + 15

	// 测试权限过滤
	staffIds := []int64{123, 456}
	resultWithPermission, err := GetDisbursementPaymentStatsFromDB(ctx, db, startDate, endDate, []int32{refundType}, staffIds)
	assert.NoError(t, err)
	assert.NotNil(t, resultWithPermission)

	// 验证权限过滤结果
	assert.Equal(t, int64(25), resultWithPermission.TotalNewPendingPaymentCount)
	assert.Equal(t, int64(20), resultWithPermission.TotalPaymentSuccessCount)
}

// BenchmarkAggregationPerformance 性能基准测试
func BenchmarkAggregationPerformance(b *testing.B) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		b.Fatal(err)
	}

	// 创建测试表
	err = db.Exec(`
		CREATE TABLE financial_fund_daily_stats (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			stat_date DATE NOT NULL,
			brand_id BIGINT,
			account_type TINYINT,
			account_id BIGINT,
			handle_by_staff_id BIGINT DEFAULT 0,
			order_staff_id BIGINT DEFAULT 0,
			new_pending_count INT DEFAULT 0,
			approved_count INT DEFAULT 0,
			rejected_count INT DEFAULT 0,
			total_approve_duration_hours DECIMAL(15,2) DEFAULT 0.00,
			approve_completed_count INT DEFAULT 0,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`).Error
	if err != nil {
		b.Fatal(err)
	}

	// 插入大量测试数据
	for i := 0; i < 1000; i++ {
		data := map[string]interface{}{
			"stat_date":                    time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			"brand_id":                     int64(i % 10), // 10个不同品牌
			"account_type":                 1,
			"account_id":                   100,
			"handle_by_staff_id":           int64(i % 5), // 5个不同员工
			"order_staff_id":               int64(i % 3), // 3个不同订单员工
			"new_pending_count":            i % 100,
			"approved_count":               i % 80,
			"rejected_count":               i % 20,
			"total_approve_duration_hours": float64(i%48) + 0.5,
			"approve_completed_count":      i % 90,
		}
		err = db.Table("financial_fund_daily_stats").Create(data).Error
		if err != nil {
			b.Fatal(err)
		}
	}

	ctx := context.Background()
	startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	brandID := int64(1)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := GetCollectionStatsFromDB(ctx, db, startDate, endDate, []int64{brandID}, nil, nil, []int64{})
		if err != nil {
			b.Fatal(err)
		}
	}
}
