package services

import (
	"context"
	"time"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/customer/internal/models"
)

/**
 * 创建客户托管邮箱账号
 * @param ctx 上下文
 * @param req *customer.CreateCustomerProvisionedEmailReq 请求参数
 * @return *customer.CreateCustomerProvisionedEmailRsp 响应
 * @return error 错误信息
 */
func CreateCustomerProvisionedEmail(ctx context.Context, req *customer.CreateCustomerProvisionedEmailReq) (*customer.CreateCustomerProvisionedEmailRsp, error) {

	email, err := models.GetCustomerProvisionedEmailByCustomerID(ctx, req.CustomerId, req.Url)
	if err != nil {
		return &customer.CreateCustomerProvisionedEmailRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InternalError),
		}, err
	} else if email != nil {
		return &customer.CreateCustomerProvisionedEmailRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_CustomerProvisionedEmailAlreadyExists),
		}, nil
	}
	email = &model.CustomerProvisionedEmail{
		CustomerID: req.CustomerId,
		Describe:   req.Describe,
		URL:        req.Url,
		Password:   req.Password,
		Forward:    req.Forward,
		ForwardURL: req.ForwardUrl,
	}
	err = models.CreateCustomerProvisionedEmail(ctx, email)
	if err != nil {
		return &customer.CreateCustomerProvisionedEmailRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InternalError),
		}, err
	}
	return &customer.CreateCustomerProvisionedEmailRsp{
		Email: &customer.CustomerProvisionedEmail{
			Id:         email.ID,
			CustomerId: email.CustomerID,
			Describe:   email.Describe,
			Url:        email.URL,
			Password:   email.Password,
			Forward:    email.Forward,
			ForwardUrl: email.ForwardURL,
			CreatedAt:  email.CreatedAt.UnixMilli(),
			UpdatedAt:  email.UpdatedAt.UnixMilli(),
		},
	}, nil
}

/**
 * 更新客户托管邮箱账号
 */
func UpdateCustomerProvisionedEmail(ctx context.Context, req *customer.UpdateCustomerProvisionedEmailReq) (*customer.UpdateCustomerProvisionedEmailRsp, error) {
	email, err := models.GetCustomerProvisionedEmailByID(ctx, req.Id)
	if err != nil {
		return &customer.UpdateCustomerProvisionedEmailRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InternalError),
		}, err
	}
	if email == nil {
		return &customer.UpdateCustomerProvisionedEmailRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_CustomerProvisionedEmailNotExist),
		}, nil
	}
	email.Describe = req.Describe
	email.URL = req.Url
	email.Password = req.Password
	email.Forward = req.Forward
	email.ForwardURL = req.ForwardUrl
	email.UpdatedAt = time.Now()
	err = models.UpdateCustomerProvisionedEmail(ctx, email)
	if err != nil {
		return &customer.UpdateCustomerProvisionedEmailRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InternalError),
		}, err
	}
	return &customer.UpdateCustomerProvisionedEmailRsp{
		Email: &customer.CustomerProvisionedEmail{
			Id:         email.ID,
			CustomerId: email.CustomerID,
			Describe:   email.Describe,
			Url:        email.URL,
			Password:   email.Password,
			Forward:    email.Forward,
			ForwardUrl: email.ForwardURL,
			CreatedAt:  email.CreatedAt.UnixMilli(),
			UpdatedAt:  email.UpdatedAt.UnixMilli(),
		},
	}, nil
}

/**
 * 删除客户托管邮箱账号
 */
func DeleteCustomerProvisionedEmail(ctx context.Context, req *customer.DeleteCustomerProvisionedEmailReq) (*customer.DeleteCustomerProvisionedEmailRsp, error) {
	err := models.DeleteCustomerProvisionedEmail(ctx, req.Id)
	if err != nil {
		return &customer.DeleteCustomerProvisionedEmailRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InternalError),
		}, err
	}
	return &customer.DeleteCustomerProvisionedEmailRsp{}, nil
}

/**
 * 查询客户托管邮箱账号列表（全量）
 */
func ListCustomerProvisionedEmails(ctx context.Context, req *customer.ListCustomerProvisionedEmailsReq) (*customer.ListCustomerProvisionedEmailsRsp, error) {
	emails, err := models.ListCustomerProvisionedEmails(ctx, req.CustomerId)
	if err != nil {
		return &customer.ListCustomerProvisionedEmailsRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InternalError),
		}, err
	}
	var respList []*customer.CustomerProvisionedEmail
	for _, email := range emails {
		respList = append(respList, &customer.CustomerProvisionedEmail{
			Id:         email.ID,
			CustomerId: email.CustomerID,
			Describe:   email.Describe,
			Url:        email.URL,
			Password:   email.Password,
			Forward:    email.Forward,
			ForwardUrl: email.ForwardURL,
			CreatedAt:  email.CreatedAt.UnixMilli(),
			UpdatedAt:  email.UpdatedAt.UnixMilli(),
		})
	}
	return &customer.ListCustomerProvisionedEmailsRsp{
		Emails: respList,
	}, nil
}

/**
 * 批量更新客户托管邮箱账号
 * @param ctx 上下文
 * @param req *customer.BatchUpdateCustomerProvisionedEmailsReq 请求参数
 * @return *customer.BatchUpdateCustomerProvisionedEmailsRsp 响应
 * @return error 错误信息
 */
func BatchUpdateCustomerProvisionedEmails(ctx context.Context, req *customer.BatchUpdateCustomerProvisionedEmailsReq) (*customer.BatchUpdateCustomerProvisionedEmailsRsp, error) {
	// 如果更新列表为空，表示删除该客户的所有邮箱账号
	if len(req.Updates) == 0 {
		err := models.DeleteAllCustomerProvisionedEmails(ctx, req.CustomerId)
		if err != nil {
			return &customer.BatchUpdateCustomerProvisionedEmailsRsp{
				Base: coderror.MakeBaseRsp(ctx, errno.Errno_InternalError),
			}, err
		}
		return &customer.BatchUpdateCustomerProvisionedEmailsRsp{
			Emails: []*customer.CustomerProvisionedEmail{},
		}, nil
	}

	// 开启事务进行批量操作
	var resultEmails []*customer.CustomerProvisionedEmail
	err := models.BatchUpdateCustomerProvisionedEmails(ctx, req.CustomerId, req.Updates, func(emails []*model.CustomerProvisionedEmail) {
		// 转换为响应格式
		for _, email := range emails {
			resultEmails = append(resultEmails, &customer.CustomerProvisionedEmail{
				Id:         email.ID,
				CustomerId: email.CustomerID,
				Describe:   email.Describe,
				Url:        email.URL,
				Password:   email.Password,
				Forward:    email.Forward,
				ForwardUrl: email.ForwardURL,
				CreatedAt:  email.CreatedAt.UnixMilli(),
				UpdatedAt:  email.UpdatedAt.UnixMilli(),
			})
		}
	})

	if err != nil {
		return &customer.BatchUpdateCustomerProvisionedEmailsRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InternalError),
		}, err
	}

	return &customer.BatchUpdateCustomerProvisionedEmailsRsp{
		Emails: resultEmails,
	}, nil
}
