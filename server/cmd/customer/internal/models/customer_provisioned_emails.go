package models

import (
	"context"
	"errors"
	"time"

	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/customer/internal/global"

	"gorm.io/gorm"
)

/**
 * 创建客户邮箱账号
 * @param ctx 上下文
 * @param email *model.CustomerProvisionedEmail 邮箱账号对象
 * @return error 错误信息
 */
func CreateCustomerProvisionedEmail(ctx context.Context, email *model.CustomerProvisionedEmail) error {
	return global.DB.WithContext(ctx).Create(email).Error
}

/**
 * 根据ID获取客户邮箱账号
 * @param ctx 上下文
 * @param id int64 邮箱账号ID
 * @return *model.CustomerProvisionedEmail 邮箱账号对象
 * @return error 错误信息
 */
func GetCustomerProvisionedEmailByID(ctx context.Context, id int64) (*model.CustomerProvisionedEmail, error) {
	email := &model.CustomerProvisionedEmail{}
	err := global.DB.WithContext(ctx).First(email, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return email, nil
}

/**
 * 根据客户ID获取客户邮箱账号
 * @param ctx 上下文
 * @param customerID int64 客户ID
 * @param url string 邮箱账号URL
 * @return *model.CustomerProvisionedEmail 邮箱账号对象
 * @return error 错误信息
 */
func GetCustomerProvisionedEmailByCustomerID(ctx context.Context, customerID int64, url string) (*model.CustomerProvisionedEmail, error) {
	email := &model.CustomerProvisionedEmail{}
	err := global.DB.WithContext(ctx).Where("customer_id = ?", customerID).Where("url = ?", url).First(email).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return email, nil
}

/**
 * 更新客户邮箱账号
 * @param ctx 上下文
 * @param email *model.CustomerProvisionedEmail 邮箱账号对象（需包含ID）
 * @return error 错误信息
 */
func UpdateCustomerProvisionedEmail(ctx context.Context, email *model.CustomerProvisionedEmail) error {
	return global.DB.WithContext(ctx).Save(email).Error
}

/**
 * 根据ID删除客户邮箱账号
 * @param ctx 上下文
 * @param id int64 邮箱账号ID
 * @return error 错误信息
 */
func DeleteCustomerProvisionedEmail(ctx context.Context, id int64) error {
	return global.DB.WithContext(ctx).Delete(&model.CustomerProvisionedEmail{}, id).Error
}

/**
 * 获取所有客户邮箱账号
 * @param ctx 上下文
 * @param customerID int64 客户ID
 * @return []*model.CustomerProvisionedEmail 邮箱账号列表
 * @return error 错误信息
 */
func ListCustomerProvisionedEmails(ctx context.Context, customerID int64) ([]*model.CustomerProvisionedEmail, error) {
	var list []*model.CustomerProvisionedEmail
	err := global.DB.WithContext(ctx).Where("customer_id = ?", customerID).Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

/**
 * 删除客户的所有邮箱账号
 * @param ctx 上下文
 * @param customerID int64 客户ID
 * @return error 错误信息
 */
func DeleteAllCustomerProvisionedEmails(ctx context.Context, customerID int64) error {
	return global.DB.WithContext(ctx).Where("customer_id = ?", customerID).Delete(&model.CustomerProvisionedEmail{}).Error
}

/**
 * 批量更新客户邮箱账号（事务性操作）
 * @param ctx 上下文
 * @param customerID int64 客户ID
 * @param updates []*customer.UpdateCustomerProvisionedEmailReq 更新请求列表
 * @param callback func([]*model.CustomerProvisionedEmail) 回调函数，用于处理结果
 * @return error 错误信息
 */
func BatchUpdateCustomerProvisionedEmails(ctx context.Context, customerID int64, updates []*customer.UpdateCustomerProvisionedEmailReq, callback func([]*model.CustomerProvisionedEmail)) error {
	var resultEmails []*model.CustomerProvisionedEmail

	// 开启事务
	return global.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 先删除该客户的所有邮箱账号
		err := tx.Where("customer_id = ?", customerID).Delete(&model.CustomerProvisionedEmail{}).Error
		if err != nil {
			return err
		}

		// 2. 如果有新的邮箱账号，批量插入
		if len(updates) > 0 {
			now := time.Now()
			for _, update := range updates {
				newEmail := &model.CustomerProvisionedEmail{
					CustomerID: customerID,
					Describe:   update.Describe,
					URL:        update.Url,
					Password:   update.Password,
					Forward:    update.Forward,
					ForwardURL: update.ForwardUrl,
					CreatedAt:  now,
					UpdatedAt:  now,
				}

				err = tx.Create(newEmail).Error
				if err != nil {
					return err
				}
				resultEmails = append(resultEmails, newEmail)
			}
		}

		// 调用回调函数处理结果
		callback(resultEmails)
		return nil
	})
}
