package cron

import (
	"context"
	"runtime/debug"
	"time"

	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/internal/global"
)

const (
	StaticOrderSalesDataDailyTimeout = 60 * time.Second
)

func StaticOrderSalesDataDaily() {

	funcName := "cron:StaticOrderSalesDataDaily"
	logger.Infof("%s start", funcName)

	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("%s panic: %v\n%s", funcName, r, debug.Stack())
		}
	}()

	rootCtx, rootCancel := context.WithTimeout(context.Background(), StaticOrderSalesDataDailyTimeout)
	defer rootCancel()

	// 1. 检查并补录前60天到前天的历史数据（如果有缺失）
	now := time.Now()
	startDate := now.AddDate(0, 0, -60) // 60天前
	endDate := now.AddDate(0, 0, -2)    // 前天
	_, err := global.OrderClient.InsertHistoricalSalesData(rootCtx, &order.InsertHistoricalSalesDataReq{
		StartDate: startDate.UnixMilli(),
		EndDate:   endDate.UnixMilli(),
	})
	if err != nil {
		logger.Errorf("%s RPC global.OrderClient.InsertHistoricalSalesData failed: %v \n", funcName, err)
		// 不return，让昨天的数据统计继续完成
	} else {
		// 对于定时任务，只记录简单的成功信息，不关心详细的统计数据
		logger.Infof("%s InsertHistoricalSalesData completed successfully", funcName)
	}

	// 2. 统计昨天的销售数据
	req := &order.InsertYesterdaySalesDataReq{}
	_, err = global.OrderClient.InsertYesterdaySalesData(rootCtx, req)
	if err != nil {
		logger.Errorf("%s RPC global.OrderClient.InsertYesterdaySalesData failed: %v \n", funcName, err)
		return
	}

	// 安全日志输出
	logger.Infof("%s successfully", funcName)
}
