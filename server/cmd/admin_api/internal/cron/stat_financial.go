package cron

import (
	"context"
	"log"

	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/server/cmd/admin_api/internal/global"
)

func RunFinancialStatsCron(ctx context.Context) {
	// 1. 补录最近60天
	_, err := global.FinancialClient.CallBackfillFinancialStats(ctx, &order.CallBackfillFinancialStatsReq{Force: false})
	if err != nil {
		log.Printf("CallBackfillFinancialStats failed: %v", err)
		// 可选择return或继续
	}

	// 2. 收款统计
	_, err = global.FinancialClient.CallStatFinancialFundDaily(ctx, &order.CallStatFinancialFundDailyReq{})
	if err != nil {
		log.Printf("CallStatFinancialFundDaily failed: %v", err)
	}

	// 3. 支款统计
	_, err = global.FinancialClient.CallStatFinancialRefundDaily(ctx, &order.CallStatFinancialRefundDailyReq{})
	if err != nil {
		log.Printf("CallStatFinancialRefundDaily failed: %v", err)
	}

	log.Println("Financial stats cron finished.")
}
