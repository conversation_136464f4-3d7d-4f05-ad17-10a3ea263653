package customer_service

import (
	"context"
	"fmt"

	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"

	"github.com/cloudwego/hertz/pkg/app"
)

// 创建客户托管邮箱账号
func CreateCustomerProvisionedEmail(ctx context.Context, c *app.RequestContext) {
	var req admin_api.CreateCustomerProvisionedEmailReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	if req.Forward == 2 && req.ForwardUrl == "" {
		httputil.ResponseParamsErr(c, "设置转发时转发地址不能为空")
		return
	}
	rpcReq := &customer.CreateCustomerProvisionedEmailReq{
		CustomerId: req.CustomerId,
		Describe:   req.Describe,
		Url:        req.Url,
		Password:   req.Password,
		Forward:    req.Forward,
		ForwardUrl: req.ForwardUrl,
	}
	rpcResp, err := global.CustomerProfileClient.CreateCustomerProvisionedEmail(ctx, rpcReq)
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "CreateCustomerProvisionedEmail rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	httputil.ResponseSuccess(c, admin_api.CreateCustomerProvisionedEmailRsp{
		Email: convertCustomerProvisionedEmail2Api(rpcResp.Email),
	})
}

// 更新客户托管邮箱账号
func UpdateCustomerProvisionedEmail(ctx context.Context, c *app.RequestContext) {
	var req admin_api.UpdateCustomerProvisionedEmailReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	if req.Forward == 2 && req.ForwardUrl == "" {
		httputil.ResponseParamsErr(c, "设置转发时转发地址不能为空")
		return
	}
	rpcReq := &customer.UpdateCustomerProvisionedEmailReq{
		Id:         req.Id,
		Describe:   req.Describe,
		Url:        req.Url,
		Password:   req.Password,
		Forward:    req.Forward,
		ForwardUrl: req.ForwardUrl,
	}
	rpcResp, err := global.CustomerProfileClient.UpdateCustomerProvisionedEmail(ctx, rpcReq)
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "UpdateCustomerProvisionedEmail rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	httputil.ResponseSuccess(c, admin_api.UpdateCustomerProvisionedEmailRsp{
		Email: convertCustomerProvisionedEmail2Api(rpcResp.Email),
	})
}

// 删除客户托管邮箱账号
func DeleteCustomerProvisionedEmail(ctx context.Context, c *app.RequestContext) {
	var req admin_api.DeleteCustomerProvisionedEmailReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	rpcReq := &customer.DeleteCustomerProvisionedEmailReq{
		Id: req.Id,
	}
	rpcResp, err := global.CustomerProfileClient.DeleteCustomerProvisionedEmail(ctx, rpcReq)
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "DeleteCustomerProvisionedEmail rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	httputil.ResponseSuccess(c, admin_api.DeleteCustomerProvisionedEmailRsp{})
}

// 查询客户托管邮箱账号列表（全量）
func ListCustomerProvisionedEmails(ctx context.Context, c *app.RequestContext) {
	var req admin_api.ListCustomerProvisionedEmailsReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	rpcReq := &customer.ListCustomerProvisionedEmailsReq{
		CustomerId: req.CustomerId,
	}
	rpcResp, err := global.CustomerProfileClient.ListCustomerProvisionedEmails(ctx, rpcReq)
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "ListCustomerProvisionedEmails rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	var emails []*admin_api.CustomerProvisionedEmail
	for _, email := range rpcResp.Emails {
		emails = append(emails, convertCustomerProvisionedEmail2Api(email))
	}
	httputil.ResponseSuccess(c, admin_api.ListCustomerProvisionedEmailsRsp{
		Emails: emails,
	})
}

// 工具函数：RPC层转API层
func convertCustomerProvisionedEmail2Api(email *customer.CustomerProvisionedEmail) *admin_api.CustomerProvisionedEmail {
	if email == nil {
		return nil
	}
	return &admin_api.CustomerProvisionedEmail{
		Id:         email.Id,
		CustomerId: email.CustomerId,
		Describe:   email.Describe,
		Url:        email.Url,
		Password:   email.Password,
		Forward:    email.Forward,
		ForwardUrl: email.ForwardUrl,
		CreatedAt:  email.CreatedAt,
		UpdatedAt:  email.UpdatedAt,
	}
}

/**
 * 批量更新客户托管邮箱账号
 * @param ctx 上下文
 * @param c *app.RequestContext HTTP请求上下文
 */
func BatchUpdateCustomerProvisionedEmails(ctx context.Context, c *app.RequestContext) {
	var req admin_api.BatchUpdateCustomerProvisionedEmailsReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	// 验证转发设置
	for i, update := range req.Updates {
		if update.Forward == 2 && update.ForwardUrl == "" {
			httputil.ResponseParamsErr(c, fmt.Sprintf("第%d个邮箱账号设置转发时转发地址不能为空", i+1))
			return
		}
	}

	// 构建RPC请求
	var rpcUpdates []*customer.UpdateCustomerProvisionedEmailReq
	for _, update := range req.Updates {
		rpcUpdates = append(rpcUpdates, &customer.UpdateCustomerProvisionedEmailReq{
			Id:         update.Id,
			Describe:   update.Describe,
			Url:        update.Url,
			Password:   update.Password,
			Forward:    update.Forward,
			ForwardUrl: update.ForwardUrl,
		})
	}

	rpcReq := &customer.BatchUpdateCustomerProvisionedEmailsReq{
		CustomerId: req.CustomerId,
		Updates:    rpcUpdates,
	}

	// 调用RPC服务
	rpcResp, err := global.CustomerProfileClient.BatchUpdateCustomerProvisionedEmails(ctx, rpcReq)
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "BatchUpdateCustomerProvisionedEmails rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	// 转换响应数据
	var emails []*admin_api.CustomerProvisionedEmail
	for _, email := range rpcResp.Emails {
		emails = append(emails, convertCustomerProvisionedEmail2Api(email))
	}

	httputil.ResponseSuccess(c, admin_api.BatchUpdateCustomerProvisionedEmailsRsp{
		Emails: emails,
	})
}
