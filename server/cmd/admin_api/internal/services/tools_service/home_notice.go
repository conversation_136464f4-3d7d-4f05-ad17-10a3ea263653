package tools_service

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"

	"uofferv2/kitex_gen/server/cmd/tools"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"
)

// ListHomeNotice .
// @router /tools/ListHomeNotice [POST]
func ListHomeNotice(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.ListHomeNoticeReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "ListHomeNotice invalid request param: %v", err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	resp := new(admin_api.ListHomeNoticeRsp)

	toolsServiceRsp, err := global.ToolsClient.ListHomeNotice(ctx, &tools.ListHomeNoticeReq{
		Type:     tools.HomeNoticeType(req.Type),
		PageNum:  req.PageNum,
		PageSize: req.PageSize,
	})
	errWrap := coderror.Warp(toolsServiceRsp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "ListHomeNotice failed: %v", errWrap)
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	updateStaffIds := make([]int64, 0)
	for _, notice := range toolsServiceRsp.Items {
		updateStaffIds = append(updateStaffIds, notice.UpdatedBy)
	}

	updateStaffMap := GetEmployeeInfoByIds(ctx, updateStaffIds)

	for _, notice := range toolsServiceRsp.Items {
		resp.Items = append(resp.Items, &admin_api.HomeNotice{
			Id:        notice.Id,
			Title:     notice.Title,
			Content:   notice.Content,
			Type:      admin_api.HomeNoticeType(notice.Type),
			UpdatedAt: notice.UpdatedAt,
			UpdatedBy: updateStaffMap[notice.UpdatedBy],
		})
	}

	resp.Total = toolsServiceRsp.Total

	httputil.ResponseSuccess(c, resp)
}

func GetEmployeeInfoByIds(ctx context.Context, ids []int64) map[int64]*admin_api.HomeNoticeEmployee {
	resp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id: ids,
	})
	errWrap := coderror.Warp(resp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds rpc error: %v, ids: %v", errWrap.Error(), ids)
		res := make(map[int64]*admin_api.HomeNoticeEmployee)
		for _, id := range ids {
			res[id] = &admin_api.HomeNoticeEmployee{}
		}
		return res
	}

	if len(resp.List) == 0 {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds not found employee ids: %v", ids)
		res := make(map[int64]*admin_api.HomeNoticeEmployee)
		for _, id := range ids {
			res[id] = &admin_api.HomeNoticeEmployee{}
		}
		return res
	}

	res := make(map[int64]*admin_api.HomeNoticeEmployee)
	for _, info := range resp.List {
		res[info.Id] = &admin_api.HomeNoticeEmployee{
			Id:                 info.Id,
			Name:               info.Name,
			PhoneCountryCodeId: info.PhoneCountryCodeId,
			PhoneNumber:        info.PhoneNumber,
			Email:              info.Email,
			RoleId:             info.RoleId,
			RoleName:           info.RoleName,
			DeptId:             info.DeptId,
			DeptName:           info.DeptName,
			Avatar:             info.Avatar,
		}
	}

	for _, id := range ids {
		if _, ok := res[id]; !ok {
			res[id] = &admin_api.HomeNoticeEmployee{}
		}
	}

	return res
}

// SaveHomeNotice .
// @router /tools/SaveHomeNotice [POST]
func SaveHomeNotice(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.SaveHomeNoticeReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "SaveHomeNotice invalid request param: %v", err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	resp := new(admin_api.SaveHomeNoticeRsp)

	auth := ctxmeta.MustGetAuth(ctx)
	toolServiceRsp, err := global.ToolsClient.SaveHomeNotice(ctx, &tools.SaveHomeNoticeReq{
		Id:        req.Id,
		Title:     req.Title,
		Content:   req.Content,
		Type:      tools.HomeNoticeType(req.Type),
		UpdatedBy: auth.Id,
	})
	errWrap := coderror.Warp(toolServiceRsp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "SaveHomeNotice failed: %v", errWrap)
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	resp.Id = toolServiceRsp.Id

	httputil.ResponseSuccess(c, resp)
}

// DeleteHomeNotice .
// @router /tools/DeleteHomeNotice [POST]
func DeleteHomeNotice(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.DeleteHomeNoticeReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "DeleteHomeNotice invalid request param: %v", err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	resp := new(admin_api.DeleteHomeNoticeRsp)
	auth := ctxmeta.MustGetAuth(ctx)
	toolServiceRsp, err := global.ToolsClient.DeleteHomeNotice(ctx, &tools.DeleteHomeNoticeReq{
		Id:        req.Id,
		UpdatedBy: auth.Id,
	})
	errWrap := coderror.Warp(toolServiceRsp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "DeleteHomeNotice failed: %v", errWrap)
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	httputil.ResponseSuccess(c, resp)
}

// GetHomeNotice .
// @router /tools/GetHomeNotice [POST]
func GetHomeNotice(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.GetHomeNoticeReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "GetHomeNotice invalid request param: %v", err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	resp := new(admin_api.GetHomeNoticeRsp)
	toolServiceRsp, err := global.ToolsClient.GetHomeNotice(ctx, &tools.GetHomeNoticeReq{
		Id: req.Id,
	})
	errWrap := coderror.Warp(toolServiceRsp, err)
	if errWrap != nil || toolServiceRsp.Notice == nil {
		logger.CtxErrorf(ctx, "GetHomeNotice failed: %v", errWrap)
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	staffMap := GetEmployeeInfoByIds(ctx, []int64{toolServiceRsp.Notice.UpdatedBy})

	resp.Item = &admin_api.HomeNotice{
		Id:        toolServiceRsp.Notice.Id,
		Title:     toolServiceRsp.Notice.Title,
		Content:   toolServiceRsp.Notice.Content,
		Type:      admin_api.HomeNoticeType(toolServiceRsp.Notice.Type),
		UpdatedAt: toolServiceRsp.Notice.UpdatedAt,
		UpdatedBy: staffMap[toolServiceRsp.Notice.UpdatedBy],
	}

	httputil.ResponseSuccess(c, resp)
}
