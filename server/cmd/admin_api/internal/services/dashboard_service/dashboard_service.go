package dashboardservice

import (
	"context"
	"sync"

	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/utils"

	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"

	"github.com/cloudwego/hertz/pkg/app"
)

func GetPendingTaskStats(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.GetPendingTaskStatsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "GetPendingTaskStats invalid request param: %v", err)
		httputil.ResponseParamsErr(c, "invalid param")
		return
	}

	// 数据权限处理，主管、超管、员工
	staffIds := make([]int64, 0)
	isManager := ctxmeta.IsManger(ctx)
	isAdmin := ctxmeta.IsFinanceReceivableFull(ctx)
	if isAdmin {
		// 超管
		staffIds = []int64{}
	} else if isManager {
		// 主管
		staffIdList, err := global.UsersClient.ListChildEmployeeById(ctx, &user.ListChildEmployeeByIdReq{
			Id: ctxmeta.MustGetAuth(ctx).EmployeeId(),
		})
		if err != nil {
			logger.CtxErrorf(ctx, "ListChildEmployeeById rpc internal error: %v", err)
			httputil.ResponseInternalErr(c)
			return
		}
		staffIds = staffIdList.Id
		staffIds = append(staffIds, ctxmeta.MustGetAuth(ctx).EmployeeId())
	} else {
		// 员工
		staffIds = []int64{ctxmeta.MustGetAuth(ctx).EmployeeId()}
	}

	resp := new(admin_api.GetPendingTaskStatsRsp)
	// 初始化响应结构体中的指针字段，避免空指针解引用
	resp.FundStats = &admin_api.FundStats{}
	resp.RefundStats = &admin_api.RefundStats{}
	resp.WorkflowViewStats = &admin_api.WorkflowViewStats{}
	resp.WorkflowTaskViewStats = &admin_api.WorkflowTaskViewStats{}
	resp.WorkflowThirdFundStats = &admin_api.WorkflowThirdFundStats{}
	resp.OrderStaticEmployeeStats = &admin_api.OrderStaticEmployeeStats{}

	paymentAccountTypes := utils.ConvertSlice(req.FundFilter.PaymentAccountTypes, func(t admin_api.PaymentAccountType) order.PaymentAccountType {
		return order.PaymentAccountType(t)
	})

	var wg sync.WaitGroup
	errCh := make(chan *coderror.ErrorWarp, 7) // 增加通道容量以容纳所有可能的错误（包括合并后的订单统计）

	// 并发获取收款统计
	var orderSvcRsp *order.GetFundPendingStatsRsp
	wg.Add(1)
	go func() {
		defer wg.Done()
		orderSvcReq := ConvertGetFundPendingStatsReq2Rpc(&req)
		orderSvcReq.StaffIds = staffIds
		orderSvcReq.PaymentAccountTypes = paymentAccountTypes
		rsp, err := global.FinancialClient.GetFundPendingStats(ctx, orderSvcReq)
		if err != nil {
			logger.CtxErrorf(ctx, "GetPendingTaskStats GetFundPendingStats error: %v", err)
			errCh <- coderror.Warp(nil, err)
			return
		}
		orderSvcRsp = rsp
		resp.FundStats = ConvertFundStats2Http(orderSvcRsp)

	}()

	// 并发获取支款统计
	var refundSvcRsp *order.GetRefundPendingStatsRsp
	wg.Add(1)
	go func() {
		defer wg.Done()
		refundSvcReq := ConvertGetRefundPendingStatsReq2Rpc(&req)
		refundSvcReq.StaffIds = staffIds
		rsp, err := global.FinancialClient.GetRefundPendingStats(ctx, refundSvcReq)
		if err != nil {
			logger.CtxErrorf(ctx, "GetRefundPendingStats error: %v", err)
			errCh <- coderror.Warp(nil, err)
			return
		}
		refundSvcRsp = rsp
		resp.RefundStats = ConvertRefundStats2Http(refundSvcRsp)
	}()

	// 并发获取工单统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		rsp, err := global.WorkflowClient.ListWorkflowV1(ctx, &workflow.ListWorkflowReq{
			WorkflowStatus: workflow.WorkflowStatus(req.GetWorkflowViewFilter().GetWorkflowStatus()),
			PageNum:        1,
			PageSize:       10,
		})
		errWrap := coderror.Warp(rsp, err)
		if errWrap != nil {
			logger.CtxErrorf(ctx, "GetPendingTaskStats ListWorkflowV1 error: %v", errWrap)
			errCh <- errWrap
			return
		}
		resp.WorkflowViewStats.PendingWorkflowView = rsp.GetTotal()
	}()

	// 并发获取工单任务节点统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		rsp, err := global.WorkflowClient.ListWorkflowTaskView(ctx, &workflow.ListWorkflowTaskViewReq{
			WorkflowTaskViewStatus: workflow.WorkflowTaskViewStatus(req.GetWorkflowTaskViewFilter().GetWorkflowTaskViewStatus()),
			PageNum:                1,
			PageSize:               10,
		})
		errWrap := coderror.Warp(rsp, err)
		if errWrap != nil {
			logger.CtxErrorf(ctx, "GetPendingTaskStats ListWorkflowTaskView error: %v", errWrap)
			errCh <- errWrap
			return
		}
		resp.WorkflowTaskViewStats.PendingWorkflowTaskView = rsp.GetTotal()
	}()

	// 获取第三方申请费审批通过的工单数
	wg.Add(1)
	go func() {
		defer wg.Done()
		rsp, err := global.WorkflowClient.CurrentThirdFundWorkflow(ctx, &workflow.CurrentThirdFundWorkflowReq{
			ApproveStatus: workflow.ThirdFundWorkflowStatus_ThirdFundWorkflowStatusApprove,
			PageNum:       1,
			PageSize:      10,
		})
		errWrap := coderror.Warp(rsp, err)
		if errWrap != nil {
			logger.CtxErrorf(ctx, "GetPendingTaskStats CurrentThirdFundWorkflow error: %v", errWrap)
			errCh <- errWrap
			return
		}
		resp.WorkflowThirdFundStats.ApproveCount = int64(rsp.GetTotal())
	}()

	// 获取第三方申请费审批的工单数
	wg.Add(1)
	go func() {
		defer wg.Done()
		rsp, err := global.WorkflowClient.CurrentThirdFundWorkflow(ctx, &workflow.CurrentThirdFundWorkflowReq{
			ApproveStatus: workflow.ThirdFundWorkflowStatus_ThirdFundWorkflowStatusReject,
			PageNum:       1,
			PageSize:      10,
		})
		errWrap := coderror.Warp(rsp, err)
		if errWrap != nil {
			logger.CtxErrorf(ctx, "GetPendingTaskStats CurrentThirdFundWorkflow error: %v", errWrap)
			errCh <- errWrap
			return
		}
		resp.WorkflowThirdFundStats.RejectCount = int64(rsp.GetTotal())
	}()

	// 获取订单统计（员工侧）包括派单被驳回统计
	wg.Add(1)
	go func() {
		defer wg.Done()

		// 并发获取订单统计和派单被驳回统计
		var orderStatsRsp *order.GetOrderStaticEmployeeRsp
		var workflowRejectedRsp *order.GetOrderWorkflowLogRsp
		var orderStatsErr, workflowRejectedErr error

		var innerWg sync.WaitGroup

		// 获取订单统计
		innerWg.Add(1)
		go func() {
			defer innerWg.Done()
			orderStatsRsp, orderStatsErr = global.OrderClient.GetOrderStaticEmployee(ctx, &order.GetOrderStaticEmployeeReq{
				CustomerIds: []int64{}, // 不限制客户
				UpdaterIds:  staffIds,
			})
		}()

		// 获取派单被驳回统计
		innerWg.Add(1)
		go func() {
			defer innerWg.Done()
			workflowRejectedRsp, workflowRejectedErr = global.OrderClient.GetOrderWorkflowLog(ctx, &order.GetOrderWorkflowLogReq{
				PageNum:     1,
				PageSize:    1, // 只需要数量，不需要具体数据
				UpdaterIds:  staffIds,
				CustomerIds: []int64{}, // 不限制客户
			})
		}()

		innerWg.Wait()

		// 检查错误
		if orderStatsErr != nil {
			errWrap := coderror.Warp(orderStatsRsp, orderStatsErr)
			logger.CtxErrorf(ctx, "GetPendingTaskStats GetOrderStaticEmployee error: %v", errWrap)
			errCh <- errWrap
			return
		}

		if workflowRejectedErr != nil {
			errWrap := coderror.Warp(workflowRejectedRsp, workflowRejectedErr)
			logger.CtxErrorf(ctx, "GetPendingTaskStats GetOrderWorkflowLog error: %v", errWrap)
			errCh <- errWrap
			return
		}

		// 转换订单统计数据并包含派单被驳回统计
		resp.OrderStaticEmployeeStats = convertOrderStaticEmployeeStatsWithRejected(orderStatsRsp, workflowRejectedRsp.GetTotal())
	}()

	wg.Wait()

	close(errCh)
	for errWrap := range errCh {
		if errWrap != nil {
			logger.CtxErrorf(ctx, "GetPendingTaskStats error: %v", errWrap)
			httputil.ResponseWithErrorWrap(c, errWrap)
			return
		}
	}

	httputil.ResponseSuccess(c, resp)
}

func GetKeyDataStats(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.GetKeyDataReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "GetKeyDataStats invalid request param: %v", err)
		httputil.ResponseParamsErr(c, "invalid param")
		return
	}

	resp := new(admin_api.GetKeyDataRsp)

	// 权限处理逻辑：暂时只查当前登录用户
	staffIds := []int64{ctxmeta.MustGetAuth(ctx).EmployeeId()}

	// 使用 sync.WaitGroup 并发获取各种统计数据
	var wg sync.WaitGroup
	errCh := make(chan error, 4) // 增加通道容量以容纳所有可能的错误

	var orderSvcRsp *order.GetFinancialStatsResponse
	var wfSvcRsp *workflow.GetWorkflowStatsRsp
	var wfNodeSvcRsp *workflow.GetWorkflowNodeStatsRsp
	var salesSvcRsp *order.GetSalesCompareDataRsp

	// 并发获取财务统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		orderSvcReq := ConvertGetKeyDataReq2Rpc(&req)
		orderSvcReq.StaffIds = staffIds
		rsp, err := global.FinancialClient.GetFinancialStats(ctx, orderSvcReq)
		if err != nil {
			logger.CtxErrorf(ctx, "GetKeyDataStats GetFinancialStats rpc internal error: %v", err)
			errCh <- err
			return
		}
		orderSvcRsp = rsp
	}()

	// 并发获取工单统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		rsp, err := global.WorkflowClient.GetWorkflowStats(ctx, &workflow.GetStatsRequest{
			StartDate: req.StartDate,
			EndDate:   req.EndDate,
		})
		if err != nil {
			logger.CtxErrorf(ctx, "GetKeyDataStats GetWorkflowStats rpc internal error: %v", err)
			errCh <- err
			return
		}
		wfSvcRsp = rsp
	}()

	// 并发获取工单节点统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		rsp, err := global.WorkflowClient.GetWorkflowNodeStats(ctx, &workflow.GetStatsRequest{
			StartDate: req.StartDate,
			EndDate:   req.EndDate,
		})
		if err != nil {
			logger.CtxErrorf(ctx, "GetKeyDataStats GetWorkflowNodeStats rpc internal error: %v", err)
			errCh <- err
			return
		}
		wfNodeSvcRsp = rsp
	}()

	// 并发获取销售对比统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		rsp, err := global.OrderClient.GetSalesCompareData(ctx, &order.GetSalesCompareDataReq{
			Uid:   ctxmeta.MustGetAuth(ctx).EmployeeId(),
			Start: req.StartDate,
			End:   req.EndDate,
		})
		if err != nil {
			logger.CtxErrorf(ctx, "GetKeyDataStats GetSalesCompareData rpc internal error: %v", err)
			errCh <- err
			return
		}
		salesSvcRsp = rsp
	}()

	wg.Wait()
	close(errCh)

	// 检查是否有错误
	for err := range errCh {
		if err != nil {
			httputil.ResponseInternalErr(c)
			return
		}
	}

	resp = ConvertGetKeyDataRsp2Http(orderSvcRsp, wfSvcRsp, wfNodeSvcRsp, salesSvcRsp)

	httputil.ResponseSuccess(c, resp)
}
