package dashboardservice

import (
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
)

// ConvertGetFundPendingStatsReq2Rpc
func ConvertGetFundPendingStatsReq2Rpc(req *admin_api.GetPendingTaskStatsReq) *order.GetFundPendingStatsReq {
	brandIds := make([]int64, 0)
	for _, brandId := range req.FundFilter.BrandIds {
		brandIds = append(brandIds, int64(brandId))
	}

	paymentAccounts := make([]int64, 0)
	for _, paymentAccount := range req.FundFilter.PaymentAccounts {
		paymentAccounts = append(paymentAccounts, int64(paymentAccount))
	}

	return &order.GetFundPendingStatsReq{
		BrandIds:        brandIds,
		PaymentAccounts: paymentAccounts,
	}
}

// ConvertGetRefundPendingStatsReq2Rpc
func ConvertGetRefundPendingStatsReq2Rpc(req *admin_api.GetPendingTaskStatsReq) *order.GetRefundPendingStatsReq {
	refundTypes := make([]int32, 0)
	for _, refundType := range req.RefundFilter.RefundTypes {
		refundTypes = append(refundTypes, int32(refundType))
	}
	return &order.GetRefundPendingStatsReq{
		RefundTypes: refundTypes,
	}
}

// ConvertFundStats2Http
func ConvertFundStats2Http(rsp *order.GetFundPendingStatsRsp) *admin_api.FundStats {
	return &admin_api.FundStats{
		NewPendingReview:        rsp.NewPendingReview,
		RejectedResubmitPending: rsp.RejectedResubmitPending,
		OverduePendingReview:    rsp.OverduePendingReview,
	}
}

// ConvertRefundStats2Http
func ConvertRefundStats2Http(rsp *order.GetRefundPendingStatsRsp) *admin_api.RefundStats {
	return &admin_api.RefundStats{
		NewPendingReview:        rsp.NewPendingReview,
		RejectedResubmitPending: rsp.RejectedResubmitPending,
		OverduePendingReview:    rsp.OverduePendingReview,
		PendingPayment:          rsp.PendingPayment,
		NearDeadlinePayment:     rsp.NearDeadlinePayment,
	}
}

// convertOrderStaticEmployeeStatsWithRejected 转换订单统计数据（包含派单被驳回统计）
func convertOrderStaticEmployeeStatsWithRejected(rsp *order.GetOrderStaticEmployeeRsp, workflowRejectedCount int64) *admin_api.OrderStaticEmployeeStats {
	return &admin_api.OrderStaticEmployeeStats{
		Deposit: &admin_api.OrderReviewStatic{
			Pass:        rsp.GetDeposit().GetPass(),
			Draft:       rsp.GetDeposit().GetDraft(),
			DraftAudit:  rsp.GetDeposit().GetDraftAudit(),
			RejectAudit: rsp.GetDeposit().GetRejectAudit(),
			Reject:      rsp.GetDeposit().GetReject(),
			All:         rsp.GetDeposit().GetAll(),
		},
		First: &admin_api.OrderReviewStatic{
			Pass:        rsp.GetFirst().GetPass(),
			Draft:       rsp.GetFirst().GetDraft(),
			DraftAudit:  rsp.GetFirst().GetDraftAudit(),
			RejectAudit: rsp.GetFirst().GetRejectAudit(),
			Reject:      rsp.GetFirst().GetReject(),
			All:         rsp.GetFirst().GetAll(),
		},
		Final: &admin_api.OrderReviewStatic{
			Pass:        rsp.GetFinal().GetPass(),
			Draft:       rsp.GetFinal().GetDraft(),
			DraftAudit:  rsp.GetFinal().GetDraftAudit(),
			RejectAudit: rsp.GetFinal().GetRejectAudit(),
			Reject:      rsp.GetFinal().GetReject(),
			All:         rsp.GetFinal().GetAll(),
		},
		Success: &admin_api.OrderReviewStatic{
			Pass:        rsp.GetSuccess().GetPass(),
			Draft:       rsp.GetSuccess().GetDraft(),
			DraftAudit:  rsp.GetSuccess().GetDraftAudit(),
			RejectAudit: rsp.GetSuccess().GetRejectAudit(),
			Reject:      rsp.GetSuccess().GetReject(),
			All:         rsp.GetSuccess().GetAll(),
		},
		Disbursement: &admin_api.OrderReviewStatic{
			Pass:        rsp.GetDisbursement().GetPass(),
			Draft:       rsp.GetDisbursement().GetDraft(),
			DraftAudit:  rsp.GetDisbursement().GetDraftAudit(),
			RejectAudit: rsp.GetDisbursement().GetRejectAudit(),
			Reject:      rsp.GetDisbursement().GetReject(),
			All:         rsp.GetDisbursement().GetAll(),
		},
		Close: &admin_api.OrderReviewStatic{
			Pass:        rsp.GetClose().GetPass(),
			Draft:       rsp.GetClose().GetDraft(),
			DraftAudit:  rsp.GetClose().GetDraftAudit(),
			RejectAudit: rsp.GetClose().GetRejectAudit(),
			Reject:      rsp.GetClose().GetReject(),
			All:         rsp.GetClose().GetAll(),
		},
		DisbursementPaid:       rsp.GetDisbursementPaid(),
		FinalWorkflowCompleted: rsp.GetFinalWorkflowCompleted(),
		FinalWorkflowProcess:   rsp.GetFinalWorkflowProcess(),
		WorkflowRejectedCount:  workflowRejectedCount, // 派单被驳回数量
	}
}

// ConvertGetKeyDataReq2Rpc converts HTTP request to RPC request
func ConvertGetKeyDataReq2Rpc(req *admin_api.GetKeyDataReq) *order.GetFinancialStatsRequest {
	rpcReq := &order.GetFinancialStatsRequest{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
	}

	// Convert brand IDs
	if len(req.BrandIds) > 0 {
		brandIds := make([]int64, 0, len(req.BrandIds))
		for _, brandId := range req.BrandIds {
			brandIds = append(brandIds, int64(brandId))
		}
		rpcReq.BrandIds = brandIds
	}

	// Convert account types
	if len(req.AccountTypes) > 0 {
		accountTypes := make([]int32, 0, len(req.AccountTypes))
		for _, accountType := range req.AccountTypes {
			accountTypes = append(accountTypes, int32(accountType))
		}
		rpcReq.AccountTypes = accountTypes
	}

	// Convert account IDs
	if len(req.AccountIds) > 0 {
		accountIds := make([]int64, 0, len(req.AccountIds))
		for _, accountId := range req.AccountIds {
			accountIds = append(accountIds, int64(accountId))
		}
		rpcReq.AccountIds = accountIds
	}

	// Convert refund types
	if len(req.RefundTypes) > 0 {
		refundTypes := make([]int32, 0, len(req.RefundTypes))
		for _, refundType := range req.RefundTypes {
			refundTypes = append(refundTypes, int32(refundType))
		}
		rpcReq.RefundTypes = refundTypes
	}

	return rpcReq
}

// ConvertGetKeyDataRsp2Http converts RPC response to HTTP response
func ConvertGetKeyDataRsp2Http(orderSvcRsp *order.GetFinancialStatsResponse, wfSvcRsp *workflow.GetWorkflowStatsRsp, wfNodeSvcRsp *workflow.GetWorkflowNodeStatsRsp, salesSvcRsp *order.GetSalesCompareDataRsp) *admin_api.GetKeyDataRsp {
	return &admin_api.GetKeyDataRsp{
		CollectionStats:           ConvertCollectionStats2Http(orderSvcRsp.CollectionStats),
		DisbursementApprovalStats: ConvertDisbursementApprovalStats2Http(orderSvcRsp.DisbursementApprovalStats),
		DisbursementPaymentStats:  ConvertDisbursementPaymentStats2Http(orderSvcRsp.DisbursementPaymentStats),
		WorkflowStats:             ConvertWorkflowStats2Http(wfSvcRsp),
		WorkflowNodeStats:         ConvertWorkflowNodeStats2Http(wfNodeSvcRsp),
		SalesCompareStats:         ConvertSalesCompareStats2Http(salesSvcRsp),
	}
}

// ConvertCollectionStats2Http converts RPC CollectionStats to HTTP CollectionStats
func ConvertCollectionStats2Http(stats *order.CollectionStats) *admin_api.CollectionStats {
	if stats == nil {
		return nil
	}
	return &admin_api.CollectionStats{
		NewPendingReceipts:          ConvertFinancialStat2Http(stats.NewPendingReceipts),
		ApprovedReceipts:            ConvertFinancialStat2Http(stats.ApprovedReceipts),
		RejectedReceipts:            ConvertFinancialStat2Http(stats.RejectedReceipts),
		AverageApproveDurationHours: ConvertFinancialStat2Http(stats.AverageApproveDurationHours),
	}
}

// ConvertDisbursementApprovalStats2Http converts RPC DisbursementApprovalStats to HTTP DisbursementApprovalStats
func ConvertDisbursementApprovalStats2Http(stats *order.DisbursementApprovalStats) *admin_api.DisbursementApprovalStats {
	if stats == nil {
		return nil
	}
	return &admin_api.DisbursementApprovalStats{
		NewPendingDisbursements:     ConvertFinancialStat2Http(stats.NewPendingDisbursements),
		ApprovedDisbursements:       ConvertFinancialStat2Http(stats.ApprovedDisbursements),
		RejectedDisbursements:       ConvertFinancialStat2Http(stats.RejectedDisbursements),
		AverageApproveDurationHours: ConvertFinancialStat2Http(stats.AverageApproveDurationHours),
	}
}

// ConvertDisbursementPaymentStats2Http converts RPC DisbursementPaymentStats to HTTP DisbursementPaymentStats
func ConvertDisbursementPaymentStats2Http(stats *order.DisbursementPaymentStats) *admin_api.DisbursementPaymentStats {
	if stats == nil {
		return nil
	}
	return &admin_api.DisbursementPaymentStats{
		NewPendingPayments:          ConvertFinancialStat2Http(stats.NewPendingPayments),
		SuccessfulPayments:          ConvertFinancialStat2Http(stats.SuccessfulPayments),
		RejectedPayments:            ConvertFinancialStat2Http(stats.RejectedPayments),
		OverduePayments:             ConvertFinancialStat2Http(stats.OverduePayments),
		AveragePaymentDurationHours: ConvertFinancialStat2Http(stats.AveragePaymentDurationHours),
	}
}

// ConvertFinancialStat2Http converts RPC FinancialStat to HTTP PeriodComparisonStat
func ConvertFinancialStat2Http(stat *order.FinancialStat) *admin_api.PeriodComparisonStat {
	if stat == nil {
		return nil
	}
	return &admin_api.PeriodComparisonStat{
		CurrentPeriodCount:  stat.CurrentPeriodCount,
		PreviousPeriodCount: stat.PreviousPeriodCount,
		PercentageChange:    stat.PercentageChange,
		IsValid:             stat.IsValid,
	}
}

func ConvertWorkflowStats2Http(rsp *workflow.GetWorkflowStatsRsp) *admin_api.WorkflowStats {
	return &admin_api.WorkflowStats{
		AssignedWorkflows:         ConvertPeriodComparisonStat2Http(rsp.WorkflowStats.AssignedWorkflows),
		ReceivedWorkflows:         ConvertPeriodComparisonStat2Http(rsp.WorkflowStats.ReceivedWorkflows),
		CompletedWorkflows:        ConvertPeriodComparisonStat2Http(rsp.WorkflowStats.CompletedWorkflows),
		ServedCustomers:           ConvertPeriodComparisonStat2Http(rsp.WorkflowStats.ServedCustomers),
		AverageProcessingDuration: ConvertPeriodComparisonStat2Http(rsp.WorkflowStats.AverageProcessingDuration),
	}
}

func ConvertPeriodComparisonStat2Http(stat *workflow.PeriodComparisonStat) *admin_api.PeriodComparisonStat {
	return &admin_api.PeriodComparisonStat{
		CurrentPeriodCount:  stat.CurrentPeriodCount,
		PreviousPeriodCount: stat.PreviousPeriodCount,
		PercentageChange:    stat.PercentageChange,
		IsValid:             stat.IsValid,
	}
}

func ConvertWorkflowNodeStats2Http(rsp *workflow.GetWorkflowNodeStatsRsp) *admin_api.WorkflowNodeStats {
	return &admin_api.WorkflowNodeStats{
		AssignedWorkflowNodes:     ConvertPeriodComparisonStat2Http(rsp.WorkflowNodeStats.AssignedWorkflowNodes),
		ReceivedWorkflowNodes:     ConvertPeriodComparisonStat2Http(rsp.WorkflowNodeStats.ReceivedWorkflowNodes),
		CompletedWorkflowNodes:    ConvertPeriodComparisonStat2Http(rsp.WorkflowNodeStats.CompletedWorkflowNodes),
		AverageProcessingDuration: ConvertPeriodComparisonStat2Http(rsp.WorkflowNodeStats.AverageProcessingDuration),
	}
}

// ConvertSalesCompareStats2Http converts RPC SalesCompareData to HTTP SalesCompareStats
func ConvertSalesCompareStats2Http(rsp *order.GetSalesCompareDataRsp) *admin_api.SalesCompareStats {
	if rsp == nil {
		return nil
	}
	return &admin_api.SalesCompareStats{
		NumNew:                ConvertSalesStatItem2Http(rsp.NumNew),
		NumFirstPass:          ConvertSalesStatItem2Http(rsp.NumFirstPass),
		AmountFirstPass:       ConvertSalesAmountStatItem2Http(rsp.AmountFirstPass),
		NumDisbursementApply:  ConvertSalesStatItem2Http(rsp.NumDisbursementApply),
	}
}

// ConvertSalesStatItem2Http converts RPC SalesStatItem to HTTP SalesStatItem
func ConvertSalesStatItem2Http(stat *order.SalesStatItem) *admin_api.SalesStatItem {
	if stat == nil {
		return nil
	}
	return &admin_api.SalesStatItem{
		CurrentPeriodCount:  stat.CurrentPeriodCount,
		PreviousPeriodCount: stat.PreviousPeriodCount,
		PercentageChange:    stat.PercentageChange,
		IsValid:             stat.IsValid,
	}
}

// ConvertSalesAmountStatItem2Http converts RPC SalesAmountStatItem to HTTP SalesAmountStatItem
func ConvertSalesAmountStatItem2Http(stat *order.SalesAmountStatItem) *admin_api.SalesAmountStatItem {
	if stat == nil {
		return nil
	}
	return &admin_api.SalesAmountStatItem{
		CurrentPeriodAmount:  stat.CurrentPeriodAmount,
		PreviousPeriodAmount: stat.PreviousPeriodAmount,
		PercentageChange:     stat.PercentageChange,
		IsValid:              stat.IsValid,
	}
}
