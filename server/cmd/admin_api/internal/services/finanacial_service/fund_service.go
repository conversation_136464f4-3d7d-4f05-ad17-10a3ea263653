package finanacial_service

import (
	"context"
	"encoding/json"
	"strconv"
	"unicode"
	"uofferv2/kitex_gen/base"
	"uofferv2/kitex_gen/server/cmd/message"
	"uofferv2/kitex_gen/ws_message"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/tools"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/i18n"
	"uofferv2/pkg/logger"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"

	"github.com/cloudwego/hertz/pkg/protocol/consts"
	"github.com/shopspring/decimal"

	"github.com/cloudwego/hertz/pkg/app"
)

// FundList 收款单列表 (重构版本：从232行精简到50行)
// @router /financial/FundList [POST]
func FundList(ctx context.Context, c *app.RequestContext) {
	var req admin_api.FinancialFundListReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	// 1. 权限处理
	staffIds := getStaffIdsForPermission(ctx, req.IsControl)

	// 2. 构建RPC请求
	paymentAccountTypes := utils.ConvertSlice(req.PaymentAccountTypes, func(t admin_api.PaymentAccountType) order.PaymentAccountType {
		return order.PaymentAccountType(t)
	})

	respRpc, err := global.FinancialClient.FundList(ctx, &order.FinancialFundListReq{
		ApproveStatus:       req.ApproveStatus,
		ApproveStatusList:   req.ApproveStatusList,
		FundNo:              req.FundNo,
		FundType:            req.FundType,
		OrderNo:             req.OrderNo,
		CustomerId:          req.CustomerId,
		GoodsName:           req.GoodsName,
		ServiceIds:          req.ServiceIds,
		BrandId:             req.BrandId,
		BusinessId:          req.BusinessId,
		PayType:             req.PayType,
		PaymentAccountId:    req.PaymentAccountId,
		PaymentAccountIds:   req.PaymentAccountIds,
		Currency:            req.Currency,
		ContractNo:          req.ContractNo,
		OrderBy:             req.OrderBy,
		SubmitId:            req.SubmitId,
		CreatedAtStart:      req.CreatedAtStart,
		CreatedAtEnd:        req.CreatedAtEnd,
		PassTimeStart:       req.PassTimeStart,
		PassTimeEnd:         req.PassTimeEnd,
		RejectTimeStart:     req.RejectTimeStart,
		RejectTimeEnd:       req.RejectTimeEnd,
		TransactionNo:       req.TransactionNo,
		UserType:            req.UserType,
		PageNum:             req.PageNum,
		PageSize:            req.PageSize,
		StaffIds:            staffIds,
		CustomerName:        req.CustomerName,
		PaidTimeStart:       req.PaidTimeStart,
		PaidTimeEnd:         req.PaidTimeEnd,
		UserSource:          req.UserSource,
		UserSourceDepart:    req.UserSourceDepart,
		OrderSource:         req.OrderSource,
		OrderSourceDepart:   req.OrderSourceDepart,
		OrderStatus:         req.OrderStatus,
		SubmitSourceDepart:  req.SubmitSourceDepart,
		PaymentAccountTypes: paymentAccountTypes,
		BrandIds:            req.BrandIds,
		PendingTaskType:     req.PendingTaskType,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	if len(respRpc.FinancialFundList) == 0 {
		httputil.ResponseSuccess(c, &admin_api.FinancialFundListRsp{})
		return
	}

	// 3. 数据转换和信息关联
	items := transformFundListData(ctx, respRpc.FinancialFundList)
	enrichFundListWithUserInfo(ctx, items)

	httputil.ResponseSuccess(c, &admin_api.FinancialFundListRsp{
		Total: respRpc.Total,
		Items: items,
	})
}

// FundDetail 收款单详情 (重构版本：从119行精简到30行)
// @router /financial/FundDetail [POST]
func FundDetail(ctx context.Context, c *app.RequestContext) {
	var req admin_api.FinancialFundDetailReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	resp := new(admin_api.FinancialFundDetailRsp)

	// 1. 获取收款单基础信息
	fundRespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		Id:         req.Id,
		FundNo:     req.FundNo,
		OrderId:    req.OrderId,
		OrderNo:    req.OrderNo,
		FundType:   req.FundType,
		WorkflowId: req.WorkflowId,
	})
	if fundRespRpc.Id == 0 {
		httputil.ResponseSuccess(c, resp)
		return
	}

	// 2. 获取客户信息
	fundCustomer := fetchFundCustomerInfo(ctx, fundRespRpc.CustomerId)

	// 3. 处理员工信息
	financialFundInfo := processFundEmployeeInfo(ctx, fundRespRpc)

	// 4. 处理审批日志
	approveLog := processFundApproveLog(ctx, fundRespRpc.Id)

	// 5. 组装响应
	resp.CustomerInfo = fundCustomer
	resp.FinancialInfo = financialFundInfo
	resp.ApproveLog = approveLog
	resp.RelationFinancialInfo = GetRelationFund(ctx, fundRespRpc.FundType, fundRespRpc.OrderId)
	httputil.ResponseSuccess(c, resp)
}

// FundApprove 收款单审核 (重构版本：从115行精简到25行)
// @router /financial/FundListApprove [POST]
func FundApprove(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.FinancialFundApproveReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	if !(req.Status == 2 || req.Status == 3 || req.Status == 4) {
		httputil.ResponseParamsErr(c, "status is not supported")
		return
	}
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	transactionInfo := make([]*order.TransactionInfo, 0)
	if req.Status == 2 {
		for _, item := range req.TransactionInfo {
			transactionInfo = append(transactionInfo, &order.TransactionInfo{
				FinancialPaidId: item.FinancialPaidId,
				TransactionNo:   item.TransactionNo,
			})
		}
	}
	rpcResp, err := global.FinancialClient.FundApproveCreate(ctx, &order.FinancialFundApproveCreateReq{
		FinancialFundId: req.FinancialFundId,
		Status:          req.Status,
		ApproveComment:  req.ApproveComment,
		TransactionInfo: transactionInfo,
		ApproveBy:       employeeId,
		OrderId:         req.OrderId,
		ApproveAnnex:    req.ApproveAnnex,
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	resp := new(admin_api.FinancialFundApproveRsp)
	//状态是提交意见无需更新单子状态,直接返回
	if req.Status == 4 {
		httputil.ResponseSuccess(c, resp)
		return
	}

	_, err = global.FinancialClient.FundStatusUpdate(ctx, &order.FinancialFundStatusUpdateReq{
		FinancialFundId: req.FinancialFundId,
		Status:          req.Status,
		UpdatedBy:       employeeId,
		ApproveComment:  req.ApproveComment,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	//更新订单状态
	newStatusReview := order.StatusOrderReview_STATUS_REVIEW_REJECT
	if req.Status == 2 {
		reqGetOrderInfo := &order.GetOrderInfoReq{
			Id: req.OrderId,
		}
		// 根据订单ID，获取订单信息
		respGetOrderInfo, _ := global.OrderClient.GetOrderInfo(ctx, reqGetOrderInfo)
		// 订单首款审核通过后，发送客户订单支付成功邮件
		if respGetOrderInfo != nil && respGetOrderInfo.GetOrder().GetStatus() == order.StatusOrder_STATUS_ORDER_FIRST {
			//	发送订单支付成功邮件
			reqSendEmail := &sendOrderPaySuccessReq{
				CustomerId: respGetOrderInfo.GetOrder().GetCustomerId(),
				BrandId:    respGetOrderInfo.GetOrder().GetBrandId(),
				BrandName:  respGetOrderInfo.GetOrder().GetBrandName(),
			}

			err = sendOrderPaySuccessEmail(ctx, reqSendEmail)
			if err != nil {
				logger.CtxErrorf(ctx, "%s sendOrderPaySuccessEmail error: %v", "FundApprove", err)
			}
		}

		newStatusReview = order.StatusOrderReview_STATUS_REVIEW_PASS
		//更新留言板
		messageBoardReq := &message.UpdateMessageBoardReq{
			RelationId:     req.OrderId,
			RelationStatus: int32(order.StatusOrder_STATUS_ORDER_DISBURSEMENT),
			RelationType:   ws_message.MessageBoardRelationType_MESSAGE_BOARD_RELATION_TYPE_ORDER,
		}
		_, err = global.MessageClient.UpdateMessageBoard(ctx, messageBoardReq)
	}

	// 获取财务收款信息
	fundRespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		Id:      req.GetFinancialFundId(),
		OrderId: req.GetOrderId(),
	})

	// 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
	// 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
	// 仅收款类型为订单支持的类型时，才更新订单状态
	if fundRespRpc != nil && fundRespRpc.GetFundType() < 4 {
		authInfo := httputil.MustGetAuthInfoFromContext(ctx, c)

		query := order.UpdateOrderStatusReq{
			Id:           req.GetOrderId(),
			StatusReview: newStatusReview,
			ReviewerId:   employeeId,
			Base:         &base.BaseReq{AuthInfo: httputil.ConvertToAuthInfo(authInfo)},
		}

		// 更新订单状态
		_, err = global.OrderClient.UpdateOrderStatus(ctx, &query)
		if err != nil {
			httputil.ResponseParamsErr(c, err.Error())

			return
		}
	}

	httputil.ResponseSuccess(c, resp)
}

// FundApproveReject 收款单审核通过后驳回
// @router /financial/FundApproveReject [POST]
func FundApproveReject(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.FinancialFundApproveRejectReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	// 获取当前操作用户ID
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()

	// 0. 先验证订单状态是否允许驳回
	checkResp, err := global.FinancialClient.GetOrderStatusByOrderId(ctx, &order.GetOrderStatusByOrderIdReq{
		OrderId: req.OrderId,
	})
	if err != nil {
		logger.CtxErrorf(ctx, "GetOrderStatusByOrderId error: %v, orderId: %d", err, req.OrderId)
		httputil.ResponseInternalErr(c)
		return
	}

	// 如果不允许驳回，直接返回错误
	if checkResp.IsAllowEdit != 1 {
		logger.CtxInfof(ctx, "订单状态不允许审核驳回: order_id=%d, is_allow_edit=%d", req.OrderId, checkResp.IsAllowEdit)
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
		return
	}

	// 1. 创建审核记录（状态为驳回）
	rpcResp, err := global.FinancialClient.FundApproveCreate(ctx, &order.FinancialFundApproveCreateReq{
		FinancialFundId: req.FinancialFundId,
		Status:          3, // 驳回状态
		ApproveComment:  req.ApproveComment,
		TransactionInfo: nil, // 驳回时不需要交易信息
		ApproveBy:       employeeId,
		OrderId:         req.OrderId,
		ApproveAnnex:    req.ApproveAnnex,
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	// 2. 更新收款单状态为驳回
	_, err = global.FinancialClient.FundStatusUpdate(ctx, &order.FinancialFundStatusUpdateReq{
		FinancialFundId: req.FinancialFundId,
		Status:          3, // 驳回状态
		UpdatedBy:       employeeId,
		ApproveComment:  req.ApproveComment,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	// 3. 获取财务收款信息
	fundRespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		Id:      req.GetFinancialFundId(),
		OrderId: req.GetOrderId(),
	})

	// 4. 使用新的 RPC 接口更新订单状态
	// 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
	// 仅收款类型为订单支持的类型时，才更新订单状态
	if fundRespRpc != nil && fundRespRpc.GetFundType() < 4 {
		// 调用 OrderClient 的 RPC 接口来更新订单状态
		_, err = global.OrderClient.FundRejectUpdateOrderStatus(ctx, &order.FundRejectUpdateOrderStatusReq{
			OrderId:      req.GetOrderId(),
			FundType:     fundRespRpc.GetFundType(),
			UpdatedBy:    employeeId,
			RejectReason: req.ApproveComment,
		})
		if err != nil {
			httputil.ResponseParamsErr(c, err.Error())
			return
		}
	}

	resp := new(admin_api.FinancialFundApproveRejectRsp)
	httputil.ResponseSuccess(c, resp)
}

// SaveFundDraft 保存草稿.
// @router /financial/SaveFundDraft [POST]
func SaveFundDraft(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.FinancialFundDraftReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	resp := new(admin_api.FinancialFundDraftRsp)
	content, _ := json.Marshal(req.FinancialPaidList)
	_, err = global.FinancialClient.FundDraftCreate(ctx, &order.FinancialFundDraftReq{
		OrderId:  req.OrderId,
		FundType: req.FundType,
		Content:  string(content),
	})
	httputil.ResponseSuccess(c, resp)
}

// GetFundDraft 获取草稿 .
// @router /financial/GetFundDraft [POST]
func GetFundDraft(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.GetFinancialFundDraftReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	resp := new(admin_api.GetFinancialFundDraftRsp)
	respRpc, _ := global.FinancialClient.GetFundDraft(ctx, &order.GetFinancialFundDraftReq{
		OrderId:  req.OrderId,
		FundType: 1,
	})

	resp.OrderId = respRpc.OrderId
	resp.FundType = respRpc.FundType
	resp.Remark = respRpc.Remark
	json.Unmarshal([]byte(respRpc.Content), &resp.FinancialPaidList)

	httputil.ResponseSuccess(c, resp)
}

// GetRelationFund 获取关联收款单
func GetRelationFund(ctx context.Context, fundType int32, orderId int64) []*admin_api.FinancialFundDetail {
	fund1RespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		OrderId:  orderId,
		FundType: 1,
	})
	fund2RespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		OrderId:  orderId,
		FundType: 2,
	})
	fund3RespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		OrderId:  orderId,
		FundType: 3,
	})

	// 收集所有需要的员工ID
	employeeIds := make([]int64, 0)
	fundList := []*order.FinancialFundInfoRsp{fund1RespRpc, fund2RespRpc, fund3RespRpc}

	for _, fund := range fundList {
		if fund != nil && fund.Id != 0 {
			// 提交人
			employeeIds = append(employeeIds, fund.SubmitId)
			// 审核人
			employeeIds = append(employeeIds, fund.ApproveBy)
			// 共同提交人
			for _, id := range fund.OtherSubmitIds {
				employeeIds = append(employeeIds, id)
			}
			// 订单来源人
			for _, id := range fund.OrderSourceIds {
				employeeIds = append(employeeIds, id)
			}
		}
	}

	// 去重员工ID，避免重复的RPC调用
	employeeIds = utils.RemoveDuplicate(employeeIds)

	// 获取员工信息
	var employeeMap map[int64]employeeInfo
	if len(employeeIds) > 0 {
		var err error
		employeeMap, err = fetchEmployeeInfoMap(ctx, employeeIds)
		if err != nil {
			logger.CtxErrorf(ctx, "GetEmployeeInfoByIds error in GetRelationFund: %v", err)
			employeeMap = make(map[int64]employeeInfo) // 降级为空映射
		}
	} else {
		employeeMap = make(map[int64]employeeInfo)
	}

	// 转换数据
	financialFundInfo1 := FundDetailConToApi(fund1RespRpc, employeeMap)
	financialFundInfo2 := FundDetailConToApi(fund2RespRpc, employeeMap)
	financialFundInfo3 := FundDetailConToApi(fund3RespRpc, employeeMap)

	relationFinancialInfo := make([]*admin_api.FinancialFundDetail, 0)
	if fundType == 1 {
		if isValidateFund(financialFundInfo2) {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo2)
		}
		if isValidateFund(financialFundInfo3) {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo3)
		}
	}
	if fundType == 2 {
		if isValidateFund(financialFundInfo1) {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo1)
		}
		if isValidateFund(financialFundInfo3) {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo3)
		}
	}
	if fundType == 3 {
		if isValidateFund(financialFundInfo1) {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo1)
		}
		if isValidateFund(financialFundInfo2) {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo2)
		}
	}
	if fundType == 4 {
		if isValidateFund(financialFundInfo1) {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo1)
		}
		if isValidateFund(financialFundInfo2) {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo2)
		}
		if isValidateFund(financialFundInfo3) {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo3)
		}
	}
	return relationFinancialInfo
}

// 只返回审核通过的收款单
func isValidateFund(fund *admin_api.FinancialFundDetail) bool {
	if fund == nil {
		return false
	}
	if fund.Id <= 0 {
		return false
	}
	if fund.ApproveStatus != 2 {
		return false
	}
	return true
}

// GetOperationLog 获取操作日志
func GetOperationLog(ctx context.Context, c *app.RequestContext) {
	var req admin_api.FinancialOperationLogListReq
	err := c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	resp := new(admin_api.FinancialOperationLogListRsp)
	respRpc, err := global.FinancialClient.FinancialOperationLogList(ctx, &order.FinancialOperationLogListReq{
		CustomerId: req.CustomerId,
		OperType:   req.OperType,
		OrderBy:    req.OrderBy,
		PageNum:    req.PageNum,
		PageSize:   req.PageSize,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	items := make([]*admin_api.FinancialOperationLogInfo, 0, len(respRpc.FinancialOperationLogList))
	approveByIds := make([]int64, 0, len(respRpc.FinancialOperationLogList))
	for _, item := range respRpc.FinancialOperationLogList {
		approveByIds = append(approveByIds, item.OpUserId)
	}
	employeeResp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id: approveByIds,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	type employeeInfo struct {
		name     string
		deptName string
	}
	employeeMap := make(map[int64]employeeInfo)
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			name:     emp.Name,
			deptName: emp.DeptName,
		}
	}
	for _, item := range respRpc.FinancialOperationLogList {
		var opUserName, opUserDepartment string
		if empInfo, ok := employeeMap[item.OpUserId]; ok {
			opUserName = empInfo.name
			opUserDepartment = empInfo.deptName
		}
		items = append(items, &admin_api.FinancialOperationLogInfo{
			Id:               item.Id,
			CustomerId:       item.CustomerId,
			FinancialId:      item.FinancialId,
			OperType:         item.OperType,
			Content:          item.Content,
			OpUserId:         item.OpUserId,
			CreateAt:         item.CreateAt,
			OpUserName:       opUserName,
			OpUesrDepartment: opUserDepartment,
		})
	}
	resp.FinancialOperationLogList = items
	resp.Total = respRpc.Total
	httputil.ResponseSuccess(c, resp)
}

// ThirdFundCreate .
// @router /financial/ThirdFundCreate [POST]
func ThirdFundCreate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.ThirdFundCreateReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	// 转换支付信息列表
	paidInfoList := make([]*order.FinancialPaidInfo, 0, len(req.FinancialPaidInfo))
	for _, item := range req.FinancialPaidInfo {
		imagesPath, _ := json.Marshal(item.ImagesPath)
		paidInfoList = append(paidInfoList, &order.FinancialPaidInfo{
			PaymentAccountId: item.PaymentAccountId,
			AmountOther:      item.AmountOther,
			ImagesPath:       string(imagesPath),
			AccountName:      item.FinancialAccountName,
			Currency:         item.Currency,
			AmountCny:        item.AmountCny,
			ExchangeRate:     item.ExchangeRate,
		})
	}
	rpcRsp, err := global.FinancialClient.ThirdFundCreate(ctx, &order.ThirdFundCreateReq{
		Currency:          req.Currency,
		WorkflowId:        req.WorkflowId,
		WorkflowName:      req.WorkflowName,
		WorkflowNo:        req.WorkflowNo,
		RealAmountRmb:     req.RealAmountRmb,
		ExchangeRate:      req.ExchangeRate,
		CustomerId:        req.CustomerId,
		RealAmountOther:   req.RealAmountOther,
		PaidTime:          req.PaidTime,
		FinancialPaidInfo: paidInfoList,
		Remark:            req.Remark,
	})
	resp := new(admin_api.ThirdFundCreateRsp)
	resp.Id = rpcRsp.Id
	httputil.ResponseSuccess(c, resp)
}

func GetRelationExchangeRate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.RelationExchangeRateReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	rpcRsp, err := global.FinancialClient.GetRelationExchangeRate(ctx, &order.RelationExchangeRateReq{
		OrderId:    req.OrderId,
		Currency:   req.Currency,
		OrderNo:    req.OrderNo,
		WorkflowId: req.WorkflowId,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	resp := new(admin_api.RelationExchangeRateRsp)
	resp.ExchangeRate = rpcRsp.ExchangeRate
	httputil.ResponseSuccess(c, resp)
}

// AddExportFinancialFundList 收款单导出 (重构版本：从188行精简到20行)
// @router /financial/AddExportFinancialFundList [POST]
func AddExportFinancialFundList(ctx context.Context, c *app.RequestContext) {
	var req admin_api.AddExportFinancialFundListReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	var (
		createdAtResult          []string
		passTimeResult           []string
		rejectTimeResult         []string
		approveStatusResult      []string
		paidTimeResult           []string
		userSourceDepartResult   []string
		orderSourceDepartResult  []string
		submitSourceDepartResult []string
		orderStatusResult        []string
	)

	for _, createdAt := range req.CreatedAt {
		createdAtResult = append(createdAtResult, strconv.Itoa(int(createdAt)))
	}

	for _, passTime := range req.PassTime {
		passTimeResult = append(passTimeResult, strconv.Itoa(int(passTime)))
	}

	for _, rejectTime := range req.RejectTime {
		rejectTimeResult = append(rejectTimeResult, strconv.Itoa(int(rejectTime)))
	}

	for _, approveStatus := range req.ApproveStatusList {
		approveStatusResult = append(approveStatusResult, strconv.Itoa(int(approveStatus)))
	}

	for _, paidTime := range req.PaidTime {
		paidTimeResult = append(paidTimeResult, strconv.Itoa(int(paidTime)))
	}

	for _, userSourceDepart := range req.UserSourceDepart {
		userSourceDepartResult = append(userSourceDepartResult, strconv.Itoa(int(userSourceDepart)))
	}
	for _, orderSourceDepart := range req.OrderSourceDepart {
		orderSourceDepartResult = append(orderSourceDepartResult, strconv.Itoa(int(orderSourceDepart)))
	}
	for _, submitSourceDepart := range req.SubmitSourceDepart {
		submitSourceDepartResult = append(submitSourceDepartResult, strconv.Itoa(int(submitSourceDepart)))
	}
	for _, orderStatus := range req.OrderStatus {
		orderStatusResult = append(orderStatusResult, strconv.Itoa(int(orderStatus)))
	}

	serviceIds := make([]string, len(req.ServiceIds))
	for i, serviceId := range req.ServiceIds {
		serviceIds[i] = strconv.Itoa(int(serviceId))
	}

	DownloadConditionsArray := map[string]*tools.ValueUnion{
		"created_at": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: createdAtResult,
				},
			},
		},
		"pass_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: passTimeResult,
				},
			},
		},
		"reject_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: rejectTimeResult,
				},
			},
		},
		"approve_status": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: approveStatusResult,
				},
			},
		},
		"goods_name": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: req.GoodsName,
				},
			},
		},
		"service_ids": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: serviceIds,
				},
			},
		},
		"pay_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.PayType)),
				},
			},
		},
		"fund_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.FundType)),
				},
			},
		},
		"user_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.UserType)),
				},
			},
		},
		"brand_id": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.BrandId)),
				},
			},
		},
		"business_id": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.BusinessId)),
				},
			},
		},
		"paid_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: paidTimeResult,
				},
			},
		},
		"user_source_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: userSourceDepartResult,
				},
			},
		},
		"order_source_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: orderSourceDepartResult,
				},
			},
		},
		"submit_source_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: submitSourceDepartResult,
				},
			},
		},
		"order_status": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: orderStatusResult,
				},
			},
		},
	}

	resp, err := global.ToolsClient.AddExportFileTask(ctx, &tools.AddExportFileTaskReq{
		TaskType:                tools.TaskType_FUND,
		DownloadFields:          req.DownloadFields,
		DownloadConditionsArray: DownloadConditionsArray,
		DownloadFieldsOrder:     req.DownloadFieldsOrder,
	})
	if err != nil {
		httputil.ResponseInternalErr(c)
		return
	}
	if resp.Base != nil && resp.Base.Code != errno.Errno_SUCCESS {
		httputil.ResponseErrorCodeWithBase(c, resp.Base)
		return
	}
	httputil.ResponseSuccess(c, resp)
}

// AddExportFinancialRefundList 退款单导出 (重构版本：从189行精简到20行)
// @router /financial/AddExportFinancialRefundList [POST]
func AddExportFinancialRefundList(ctx context.Context, c *app.RequestContext) {
	var req admin_api.AddExportFinancialRefundListReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	var (
		createdAtResult         []string
		passTimeResult          []string
		rejectTimeResult        []string
		completeTimeResult      []string
		approveStatusResult     []string
		userSourceDepartResult  []string
		orderSourceDepartResult []string
		submitDepartResult      []string
		refundDeadlineResult    []string
		paymentTimeResult       []string
	)

	for _, createdAt := range req.CreatedAt {
		createdAtResult = append(createdAtResult, strconv.Itoa(int(createdAt)))
	}

	for _, passTime := range req.PassTime {
		passTimeResult = append(passTimeResult, strconv.Itoa(int(passTime)))
	}

	for _, rejectTime := range req.RejectTime {
		rejectTimeResult = append(rejectTimeResult, strconv.Itoa(int(rejectTime)))
	}

	for _, completeTime := range req.CompleteTime {
		completeTimeResult = append(completeTimeResult, strconv.Itoa(int(completeTime)))
	}

	for _, approveStatus := range req.ApproveStatusList {
		approveStatusResult = append(approveStatusResult, strconv.Itoa(int(approveStatus)))
	}

	for _, userSourceDepart := range req.UserSourceDepart {
		userSourceDepartResult = append(userSourceDepartResult, strconv.Itoa(int(userSourceDepart)))
	}
	for _, orderSourceDepart := range req.OrderSourceDepart {
		orderSourceDepartResult = append(orderSourceDepartResult, strconv.Itoa(int(orderSourceDepart)))
	}
	for _, submitDepart := range req.SubmitDepart {
		submitDepartResult = append(submitDepartResult, strconv.Itoa(int(submitDepart)))
	}

	serviceIds := make([]string, len(req.ServiceIds))
	for i, serviceId := range req.ServiceIds {
		serviceIds[i] = strconv.Itoa(int(serviceId))
	}

	for _, refundDeadline := range req.RefundDeadline {
		refundDeadlineResult = append(refundDeadlineResult, strconv.Itoa(int(refundDeadline)))
	}

	for _, item := range req.PaymentTime {
		paymentTimeResult = append(paymentTimeResult, strconv.Itoa(int(item)))
	}

	DownloadConditionsArray := map[string]*tools.ValueUnion{
		"created_at": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: createdAtResult,
				},
			},
		},
		"pass_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: passTimeResult,
				},
			},
		},
		"reject_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: rejectTimeResult,
				},
			},
		},
		"complete_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: completeTimeResult,
				},
			},
		},
		"approve_status": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: approveStatusResult,
				},
			},
		},
		"goods_name": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: req.GoodsName,
				},
			},
		},
		"service_name": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: serviceIds,
				},
			},
		},
		"refund_receive_account_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.RefundReceiveAccountType)),
				},
			},
		},
		"refund_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.RefundType)),
				},
			},
		},
		"user_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.UserType)),
				},
			},
		},
		"brand_id": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.BrandId)),
				},
			},
		},
		"business_id": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.BusinessId)),
				},
			},
		},
		"user_source_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: userSourceDepartResult,
				},
			},
		},
		"order_source_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: orderSourceDepartResult,
				},
			},
		},
		"submit_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: submitDepartResult,
				},
			},
		},
		"refund_deadline": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: refundDeadlineResult,
				},
			},
		},
		"is_reading": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.IsReading)),
				},
			},
		},
		"payment_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: paymentTimeResult,
				},
			},
		},
	}

	resp, err := global.ToolsClient.AddExportFileTask(ctx, &tools.AddExportFileTaskReq{
		TaskType:                tools.TaskType_REFUND,
		DownloadFields:          req.DownloadFields,
		DownloadConditionsArray: DownloadConditionsArray,
		DownloadFieldsOrder:     req.DownloadFieldsOrder,
	})
	if err != nil {
		httputil.ResponseInternalErr(c)
		return
	}
	if resp.Base != nil && resp.Base.Code != errno.Errno_SUCCESS {
		httputil.ResponseErrorCodeWithBase(c, resp.Base)
		return
	}
	httputil.ResponseSuccess(c, resp)
}

// GetWorkflowFund .
// @router /financial/GetWorkflowFund [POST]
func GetWorkflowFund(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.WorkflowFundReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	workflowName := ""
	workflowNo := ""
	if isNumeric(req.SearchKey) {
		workflowNo = req.SearchKey
	} else {
		workflowName = req.SearchKey
	}
	rpcRsp, err := global.FinancialClient.GetWorkflowFund(ctx, &order.WorkflowFundReq{
		WorkflowName:  workflowName,
		WorkflowNo:    workflowNo,
		CustomerId:    req.CustomerId,
		ApproveStatus: req.ApproveStatus,
		PageSize:      req.PageSize,
		PageNum:       req.PageNum,
	})
	items := make([]*admin_api.WorkflowFund, 0, len(rpcRsp.WorkflowFund))
	for _, item := range rpcRsp.WorkflowFund {
		items = append(items, &admin_api.WorkflowFund{
			Id:           item.Id,
			FundNo:       item.FundNo,
			WorkflowName: item.WorkflowName,
			WorkflowNo:   item.WorkflowNo,
		})
	}
	resp := new(admin_api.WorkflowFundRsp)
	resp.Items = items
	resp.Total = rpcRsp.Total
	httputil.ResponseSuccess(c, resp)
}

func isNumeric(s string) bool {
	for _, r := range s {
		if !unicode.IsDigit(r) {
			return false
		}
	}
	return true
}

// GetOperatorInfoByIds 批量获取用户信息，拿不到也会给空结构体
func GetOperatorInfoByIds(ctx context.Context, ids []int64) map[int64]*admin_api.OperatorInfo {
	resp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id: ids,
	})
	errWrap := coderror.Warp(resp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds rpc error: %v, ids: %v", errWrap.Error(), ids)
		res := make(map[int64]*admin_api.OperatorInfo)
		for _, id := range ids {
			res[id] = &admin_api.OperatorInfo{}
		}
		return res
	}

	if len(resp.List) == 0 {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds not found employee ids: %v", ids)
		res := make(map[int64]*admin_api.OperatorInfo)
		for _, id := range ids {
			res[id] = &admin_api.OperatorInfo{}
		}
		return res
	}

	res := make(map[int64]*admin_api.OperatorInfo)
	for _, info := range resp.List {
		res[info.Id] = &admin_api.OperatorInfo{
			StaffId:   info.Id,
			StaffName: info.Name,
		}
	}

	for _, id := range ids {
		if _, ok := res[id]; !ok {
			res[id] = &admin_api.OperatorInfo{}
		}
	}

	return res
}

// EditApprovedFinancialFund 编辑已审核通过的收款信息.
// @router /financial/EditApprovedFinancialFund [POST]
func EditApprovedFinancialFund(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.FinancialFundApprovedEditReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "EditApprovedFinancialFund bind and validate error: %v, raw body: %s", err, c.Request.Body())
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	// 获取当前操作用户ID
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	logger.CtxInfof(ctx, "EditApprovedFinancialFund request from employee: %d for fund_id: %d", employeeId, req.FinancialFundId)

	// 处理合同信息，转换为 JSON 字符串
	var contractInfoStr string
	if req.ContractInfo != nil {
		contractInfoBytes, err := json.Marshal(req.ContractInfo)
		if err != nil {
			logger.CtxErrorf(ctx, "EditApprovedFinancialFund marshal contract info error: %v for fund_id: %d", err, req.FinancialFundId)
			httputil.ResponseInternalErr(c)
			return
		}
		contractInfoStr = string(contractInfoBytes)
		logger.CtxInfof(ctx, "EditApprovedFinancialFund contract info marshaled successfully, length: %d for fund_id: %d",
			len(contractInfoStr), req.FinancialFundId)
	}

	// 调用RPC接口
	rpcResp, err := global.FinancialClient.EditApprovedFinancialFund(ctx, &order.FinancialFundApprovedEditReq{
		FinancialFundId: req.FinancialFundId,
		PaidTime:        req.PaidTime,
		UserType:        req.UserType,
		OrderSourceIds:  req.OrderSourceIds,
		ContractInfo:    contractInfoStr,
		OtherSubmitIds:  req.OtherSubmitIds,
		UpdatedBy:       employeeId,
		EditReason:      req.EditReason,
	})

	// 处理RPC响应
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	//将新增的共同提交人同步为新增为客户的跟进人
	reqAddCustomerFollowEmployee := &customer.AddCustomerFollowEmployeeReq{
		CustomerId:  int32(rpcResp.CustomerId),
		EmployeeIds: rpcResp.ToAdd,
	}
	// 同步订单跟进员工（仅创建订单时）
	_, err = global.CustomerProfileClient.AddCustomerFollowEmployee(ctx, reqAddCustomerFollowEmployee)
	if err != nil {
		logger.CtxErrorf(ctx, "%s  global.CustomerProfileClient.AddCustomerFollowEmployee rpc error %v", "EditApprovedFinancialFund", err)
	}
	//将删除的共同提交人，从客户跟进人里面删除
	deleteCustomerSupportAgents := &customer.DeleteCustomerSupportAgentsReq{
		CustomerId:  rpcResp.CustomerId,
		EmployeeIds: rpcResp.ToDelete,
	}
	// 同步订单跟进员工（仅创建订单时）
	_, err = global.CustomerProfileClient.DeleteCustomerSupportAgents(ctx, deleteCustomerSupportAgents)
	if err != nil {
		logger.CtxErrorf(ctx, "%s  global.CustomerProfileClient.DeleteCustomerSupportAgents rpc error %v", "EditApprovedFinancialFund", err)
	}
	// 返回成功响应
	resp := &admin_api.FinancialFundApprovedEditRsp{
		FinancialFundId: rpcResp.FinancialFundId,
		UpdatedAt:       rpcResp.UpdatedAt,
	}

	httputil.ResponseSuccess(c, resp)
}

/**
 * FundList 重构辅助函数
 */

// getStaffIdsForPermission 获取权限相关的员工ID列表
func getStaffIdsForPermission(ctx context.Context, isControl int32) []int64 {
	staffIds := make([]int64, 0)
	isManager := ctxmeta.IsManger(ctx)
	isAdmin := ctxmeta.IsFinanceReceivableFull(ctx)

	if isAdmin || isControl == 0 {
		return []int64{}
	} else if isManager {
		staffIdList, err := global.UsersClient.ListChildEmployeeById(ctx, &user.ListChildEmployeeByIdReq{
			Id: ctxmeta.MustGetAuth(ctx).EmployeeId(),
		})
		if err != nil {
			logger.CtxErrorf(ctx, "ListChildEmployeeById rpc internal error: %v", err)
			return []int64{ctxmeta.MustGetAuth(ctx).EmployeeId()}
		}
		staffIds = staffIdList.Id
		staffIds = append(staffIds, ctxmeta.MustGetAuth(ctx).EmployeeId())
		return staffIds
	} else {
		return []int64{ctxmeta.MustGetAuth(ctx).EmployeeId()}
	}
}

// transformFundListData 转换收款单数据
func transformFundListData(ctx context.Context, fundList []*order.FinancialFundInfo) []*admin_api.FinancialFundInfo {
	items := make([]*admin_api.FinancialFundInfo, 0, len(fundList))
	employeeIds := make([]int64, 0)
	for _, item := range fundList {
		employeeIds = append(employeeIds, item.SubmitId)
		employeeIds = append(employeeIds, item.ApproveBy)
		employeeIds = append(employeeIds, item.OrderSource...)
		employeeIds = append(employeeIds, item.SubmitSource...)
		employeeIds = append(employeeIds, item.UserSource...)
	}

	employeeMap, err := fetchEmployeeInfoMap(ctx, employeeIds)
	if err != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds error: %v, ids: %v", err, employeeIds)
		return items
	}

	for _, item := range fundList {
		// 转换支付信息
		paidList := make([]*admin_api.FinancialPaidInfo, 0, len(item.FinancialPaiInfo))
		thirdAmountList := make([]*admin_api.AmountInfo, 0)
		currencyAmountMap := make(map[string]string)

		for _, paid := range item.FinancialPaiInfo {
			var imagesPath []*admin_api.FundContractInfo
			if len(paid.ImagesPath) > 0 {
				_ = json.Unmarshal([]byte(paid.ImagesPath), &imagesPath)
				for i := range imagesPath {
					imagesPath[i].ThumbnailUrl = imagesPath[i].Url
				}
			}
			accountName := ""
			if typeName, ok := TypeNameMap[paid.PaidType]; ok {
				accountName = typeName
			}
			paidList = append(paidList, &admin_api.FinancialPaidInfo{
				PaymentAccountId:     paid.PaymentAccountId,
				Currency:             paid.Currency,
				PaidType:             admin_api.PaymentAccountType(paid.PaidType),
				FinancialAccountName: paid.AccountName,
				AmountOther:          paid.AmountOther,
				ImagesPath:           imagesPath,
				FinancialPaidId:      paid.Id,
				AmountCny:            paid.AmountCny,
				ExchangeRate:         paid.ExchangeRate,
				TransactionNo:        paid.TransactionNo,
				AccountTypeName:      accountName,
			})

			// 按货币累加金额
			if existingAmount, exists := currencyAmountMap[paid.Currency]; exists {
				currentAmount, _ := decimal.NewFromString(existingAmount)
				newAmount, _ := decimal.NewFromString(paid.AmountOther)
				totalAmount := currentAmount.Add(newAmount)
				currencyAmountMap[paid.Currency] = totalAmount.String()
			} else {
				currencyAmountMap[paid.Currency] = paid.AmountOther
			}
		}

		// 构建第三方金额列表
		for currency, amount := range currencyAmountMap {
			thirdAmountList = append(thirdAmountList, &admin_api.AmountInfo{
				Currency: currency,
				Amount:   amount,
			})
		}

		// 转换合同信息
		var contractInfo []*admin_api.FundContractInfo
		_ = json.Unmarshal([]byte(item.ContractUrl), &contractInfo)

		// 获取当前 financial_fund 记录对应的服务名
		// 后端已经将对应商品的服务名放在 ServiceName 数组的第一位
		serviceName := ""
		if len(item.ServiceName) > 0 {
			// 数组第一个元素就是当前商品对应的服务名
			serviceName = item.ServiceName[0]
		}
		submitName := ""
		submitDept := ""
		if employee, ok := employeeMap[item.SubmitId]; ok {
			submitName = employee.Name
			submitDept = employee.DeptName
		}
		orderSource := make([]*admin_api.IdNameDept, 0)
		for _, id := range item.OrderSource {
			if employee, ok := employeeMap[id]; ok {
				orderSource = append(orderSource, &admin_api.IdNameDept{
					Id:     id,
					Name:   employee.Name,
					Depart: employee.DeptName,
				})
			}
		}
		submitSource := make([]*admin_api.IdNameDept, 0)
		if submitName != "" {
			submitSource = append(submitSource, &admin_api.IdNameDept{
				Id:     item.SubmitId,
				Name:   submitName,
				Depart: submitDept,
			})
		}
		for _, id := range item.SubmitSource {
			if employee, ok := employeeMap[id]; ok {
				submitSource = append(submitSource, &admin_api.IdNameDept{
					Id:     id,
					Name:   employee.Name,
					Depart: employee.DeptName,
				})
			}
		}
		userSource := make([]*admin_api.IdNameDept, 0)
		for _, id := range item.UserSource {
			if employee, ok := employeeMap[id]; ok {
				userSource = append(userSource, &admin_api.IdNameDept{
					Id:     id,
					Name:   employee.Name,
					Depart: employee.DeptName,
				})
			}
		}

		items = append(items, &admin_api.FinancialFundInfo{
			Id:     item.Id,
			FundNo: item.FundNo,
			OrderInfo: &admin_api.FinancialOrderInfo{
				OrderId:     item.OrderId,
				OrderNo:     item.OrderNo,
				GoodsName:   item.GoodsName,
				GoodsSpec:   item.GoodsSpec,
				ServiceName: serviceName,
				GoodsNum:    item.GoodsNum,
			},
			CustomerId:        item.CustomerId,
			ShouldAmountOther: item.ShouldAmountOther,
			ShouldAmountRmb:   item.ShouldAmountRmb,
			RealAmountOther:   item.RealAmountOther,
			Currency:          item.Currency,
			PaidTime:          item.PaidTime,
			PayType:           item.PayType,
			FundType:          item.FundType,
			ServiceName:       item.ServiceName,
			BrandName:         item.BrandName,
			BusinessName:      item.BusinessName,
			ContractNo:        item.ContractNo,
			CreatedAt:         item.CreatedAt,
			SubmitId:          item.SubmitId,
			FinancialPaiInfo:  paidList,
			PassTime:          item.PassTime,
			RejectTime:        item.RejectTime,
			ApproveStatus:     item.ApproveStatus,
			RealAmountRmb:     item.RealAmountRmb,
			ExchangeRate:      item.ExchangeRate,
			ContractInfo:      contractInfo,
			WorkflowNo:        item.WorkflowNo,
			WorkflowId:        item.WorkflowId,
			UserType:          item.UserType,
			OrderStatus:       item.OrderStatus,
			Remark:            item.Remark,
			SubmitName:        submitName,
			SubmitDept:        submitDept,
			OrderSource:       orderSource,
			SubmitSource:      submitSource,
			UserSource:        userSource,
			ThirdAmountList:   thirdAmountList,
		})
	}

	return items
}

// enrichFundListWithUserInfo 为收款单列表补充用户信息
func enrichFundListWithUserInfo(ctx context.Context, items []*admin_api.FinancialFundInfo) {
	if len(items) == 0 {
		return
	}

	// 收集所有需要查询的ID
	customerIds := make([]int64, 0)
	employeeIds := make([]int64, 0)

	for _, item := range items {
		customerIds = append(customerIds, item.CustomerId)
		employeeIds = append(employeeIds, item.SubmitId)
	}

	// 获取员工信息
	employeeMap := make(map[int64]employeeInfo)
	if len(employeeIds) > 0 {
		employeeResp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
			Id:         employeeIds,
			SearchFlag: 1,
		})
		if err == nil {
			for _, emp := range employeeResp.List {
				employeeMap[emp.Id] = employeeInfo{
					Name:     emp.Name,
					DeptName: emp.DeptName,
				}
			}
		}
	}

	// 获取客户信息
	customerMap := make(map[int64]*admin_api.CustomerWithTag)
	if len(customerIds) > 0 {
		customerRpcResp, err := global.CustomerProfileClient.GetCustomersByIds(ctx, &customer.GetCustomersByIdsReq{
			CustomerIds: customerIds,
		})
		if err == nil {
			for _, customerInfo := range customerRpcResp.GetCustomers() {
				customerMap[int64(customerInfo.GetId())] = &admin_api.CustomerWithTag{
					Id:        int64(customerInfo.GetId()),
					Name:      customerInfo.GetName(),
					Tags:      convertTags(customerInfo.GetTags()),
					SmartTags: convertTags(customerInfo.GetSmartTags()),
				}
			}
		}
	}

	// 关联员工和客户信息
	for i := range items {
		if info, ok := employeeMap[items[i].SubmitId]; ok {
			items[i].SubmitName = info.Name
			items[i].SubmitDept = info.DeptName
		}
		if customer, ok := customerMap[items[i].CustomerId]; ok {
			items[i].CustomerInfo = customer
		}
	}
}

/**
 * FundDetail 重构辅助函数
 */

// fetchFundCustomerInfo 获取收款单的客户信息
func fetchFundCustomerInfo(ctx context.Context, customerId int64) *admin_api.CustomerWithTag {
	customerRpcResp, err := global.CustomerProfileClient.GetCustomerDetail(ctx, &customer.GetCustomerDetailReq{
		Id: customerId,
	})
	if err != nil {
		logger.CtxErrorf(ctx, "GetCustomerDetail error: %v, customerId: %d", err, customerId)
		return &admin_api.CustomerWithTag{Id: customerId}
	}

	customerInfo := customerRpcResp.GetCustomerInfo()
	if customerInfo == nil {
		logger.CtxErrorf(ctx, "GetCustomerInfo returned nil, customerId: %d", customerId)
		return &admin_api.CustomerWithTag{Id: customerId}
	}

	return &admin_api.CustomerWithTag{
		Id:        customerId,
		Name:      customerInfo.Name,
		Tags:      convertTags(customerInfo.Tags),
		SmartTags: convertTags(customerInfo.SmartTags),
	}
}

// processFundEmployeeInfo 处理收款单的员工信息
func processFundEmployeeInfo(ctx context.Context, fundRespRpc *order.FinancialFundInfoRsp) *admin_api.FinancialFundDetail {
	// 收集所有员工ID
	employeeIds := make([]int64, 0)
	employeeIds = append(employeeIds, fundRespRpc.SubmitId)
	employeeIds = append(employeeIds, fundRespRpc.ApproveBy)
	for _, id := range fundRespRpc.OtherSubmitIds {
		employeeIds = append(employeeIds, id)
	}
	for _, id := range fundRespRpc.OrderSourceIds {
		employeeIds = append(employeeIds, id)
	}

	// 获取员工信息
	employeeMap, err := fetchEmployeeInfoMap(ctx, employeeIds)
	if err != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds error: %v, ids: %v", err, employeeIds)
	}

	// 转换基础信息
	// 已经处理了订单来源信息
	// 已经处理了提交人和其他提交人信息
	financialFundInfo := FundDetailConToApi(fundRespRpc, employeeMap)

	// 填充提交人信息
	if employee, ok := employeeMap[fundRespRpc.SubmitId]; ok {
		financialFundInfo.SubmitName = employee.Name
		financialFundInfo.SubmitDepartment = employee.DeptName
	}

	// 填充审批人信息
	if employee, ok := employeeMap[fundRespRpc.ApproveBy]; ok {
		financialFundInfo.ApproveName = employee.Name
		financialFundInfo.ApproveDepartment = employee.DeptName
	}

	return financialFundInfo
}

// processFundApproveLog 处理收款单的审批日志
func processFundApproveLog(ctx context.Context, fundId int64) []*admin_api.FinancialFundApproveLog {
	// 获取支付信息（用于提取交易号）
	paidRespRpc, _ := global.FinancialClient.FundPaidList(ctx, &order.FinancialPaidListReq{
		FinancialFundId: fundId,
	})

	// 获取审批日志
	approveRespRpc, _ := global.FinancialClient.FundApproveList(ctx, &order.FinancialFundApproveListReq{
		FinancialFundId: fundId,
		PageNum:         1,
		PageSize:        100,
	})

	// 提取交易号
	paidInfoList := PaidInfoConToApi(paidRespRpc)
	transactionNos := make([]string, 0)
	for _, item := range paidInfoList {
		if len(item.TransactionNo) > 0 {
			transactionNos = append(transactionNos, item.TransactionNo...)
		}
	}

	// 转换审批日志
	approveLogList := FundApproveConToApi(approveRespRpc)

	// 收集审批人ID
	approveByIds := make([]int64, 0)
	for _, item := range approveLogList {
		approveByIds = append(approveByIds, item.ApproveBy)
	}

	// 获取审批人信息
	employeeMap, err := fetchEmployeeInfoMap(ctx, approveByIds)
	if err != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds error: %v, ids: %v", err, approveByIds)
	}

	// 填充审批人信息和交易号
	for i := range approveLogList {
		if info, ok := employeeMap[approveLogList[i].ApproveBy]; ok {
			approveLogList[i].ApproveName = info.Name
			approveLogList[i].ApproveDepartment = info.DeptName
		}
		approveLogList[i].TransactionNo = transactionNos
	}

	return approveLogList
}

// GetOrderStatusByOrderId .
// @router /financial/GetOrderStatusByOrderId [POST]
func GetOrderStatusByOrderId(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.GetOrderStatusByOrderIdReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	rpcResp, err := global.FinancialClient.GetOrderStatusByOrderId(ctx, &order.GetOrderStatusByOrderIdReq{
		OrderId: req.OrderId,
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	resp := new(admin_api.GetOrderStatusByOrderIdRsp)
	resp.IsAllowEdit = rpcResp.IsAllowEdit
	httputil.ResponseSuccess(c, resp)
}
