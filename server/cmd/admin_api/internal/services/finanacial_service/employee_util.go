package finanacial_service

import (
	"context"

	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/admin_api/internal/global"
)

// employeeInfo defines a shared structure for employee details.
// It is intended for internal use within the finanacial_service package.
type employeeInfo struct {
	Name     string
	DeptName string
}

// fetchEmployeeInfoMap retrieves employee details for a given list of IDs and returns them as a map.
func fetchEmployeeInfoMap(ctx context.Context, ids []int64) (map[int64]employeeInfo, error) {
	if len(ids) == 0 {
		return make(map[int64]employeeInfo), nil
	}

	uniqueIds := utils.RemoveDuplicate(ids)

	employeeResp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id:         uniqueIds,
		SearchFlag: 1,
	})
	if err != nil {
		return nil, err
	}

	employeeMap := make(map[int64]employeeInfo, len(employeeResp.List))
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			Name:     emp.Name,
			DeptName: emp.DeptName,
		}
	}

	return employeeMap, nil
}
