package workflow_service

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"

	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"
)

// ListWorkflowThirdFund .
// @router /workflow/ListWorkflowThirdFund [POST]
func ListWorkflowThirdFund(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.ListWorkflowThirdFundReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "ListWorkflowThirdFund invalid request param: %v", err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	resp := new(admin_api.ListWorkflowThirdFundRsp)

	workflowRsp, err := global.WorkflowClient.CurrentThirdFundWorkflow(ctx, &workflow.CurrentThirdFundWorkflowReq{
		ApproveStatus: workflow.ThirdFundWorkflowStatus(req.Status),
		PageNum:       req.PageNum,
		PageSize:      req.PageSize,
	})
	errWrap := coderror.Warp(workflowRsp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "ListWorkflowThirdFund CurrentThirdFundWorkflow error: %v", errWrap)
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	customerIds := make([]int64, 0, len(workflowRsp.Items))
	for _, item := range workflowRsp.Items {
		customerIds = append(customerIds, item.WorkflowInfo.WorkflowInfo.CustomerId)
	}

	customerMap, err := GetCustomerInfoByIds(ctx, customerIds)
	if err != nil {
		logger.CtxErrorf(ctx, "ListWorkflowThirdFund GetCustomerInfoByIds error: %v", err)
		httputil.ResponseInternalErr(c)
		return
	}

	for _, workflowItem := range workflowRsp.Items {
		customerId := workflowItem.WorkflowInfo.WorkflowInfo.CustomerId
		if _, ok := customerMap[customerId]; !ok {
			logger.CtxErrorf(ctx, "ListWorkflowThirdFund customerId: %d not found", customerId)
			continue
		}
		item := &admin_api.ThirdFundWorkflowEntity{
			WorkflowId:   workflowItem.WorkflowInfo.WorkflowInfo.Id,
			WorkflowNo:   workflowItem.WorkflowInfo.WorkflowInfo.WorkflowNo,
			Name:         workflowItem.WorkflowInfo.WorkflowInfo.WorkflowName,
			Timestamp:    workflowItem.OperationTime,
			CustomerId:   workflowItem.WorkflowInfo.WorkflowInfo.CustomerId,
			CustomerName: customerMap[customerId].Name,
		}
		resp.Items = append(resp.Items, item)
	}

	resp.Total = int64(workflowRsp.Total)

	httputil.ResponseSuccess(c, resp)
}
