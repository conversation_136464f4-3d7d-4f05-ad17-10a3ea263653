package order_service

import (
	"encoding/json"
	"fmt"
)

// OrderSource 表示订单来源的枚举类型，使用 int64 作为底层类型。
// 枚举值为负数，避免与系统其他状态码冲突。
type OrderSource int64

// 订单来源枚举定义
const (
	OrderSourceSelf     OrderSource = -1 // 客户本人
	OrderSourceReferral OrderSource = -2 // 客户推荐
	OrderSourceFriends  OrderSource = -3 // 亲朋好友
	OrderSourceVisit    OrderSource = -4 // 店访
	OrderSourcePartner  OrderSource = -5 // 合作方
	OrderSourceWechat   OrderSource = -6 // 微信朋友圈
)

// orderSourceNames 维护 OrderSource 枚举值与可读描述的映射关系
var orderSourceNames = map[OrderSource]string{
	OrderSourceSelf:     "客户本人",
	OrderSourceReferral: "客户推荐",
	OrderSourceFriends:  "亲朋好友",
	OrderSourceVisit:    "店访",
	OrderSourcePartner:  "合作方",
	OrderSourceWechat:   "微信朋友圈",
}

// String 返回 OrderSource 的可读字符串描述
func (s OrderSource) String() string {
	if name, ok := orderSourceNames[s]; ok {
		return name
	}

	return fmt.Sprintf("未知来源(%d)", int64(s))
}

// IsValid 校验 OrderSource 是否为预定义的合法值
func (s OrderSource) IsValid() bool {
	_, ok := orderSourceNames[s]

	return ok
}

// ParseOrderSource 将字符串解析为 OrderSource 枚举值
func ParseOrderSource(name string) (OrderSource, error) {
	for k, v := range orderSourceNames {
		if v == name {
			return k, nil
		}
	}

	return 0, fmt.Errorf("无效的订单来源: %s", name)
}

// MarshalJSON 实现 json.Marshaler 接口，序列化为字符串描述
func (s OrderSource) MarshalJSON() ([]byte, error) {
	return json.Marshal(s.String())
}

// UnmarshalJSON 实现 json.Unmarshaler 接口，从字符串反序列化
func (s *OrderSource) UnmarshalJSON(data []byte) error {
	var str string

	if err := json.Unmarshal(data, &str); err != nil {
		return err
	}

	v, err := ParseOrderSource(str)
	if err != nil {
		return err
	}

	*s = v

	return nil
}

// OrderSourceSet 用于批量处理订单来源集合（如权限校验）
type OrderSourceSet map[OrderSource]struct{}

// NewOrderSourceSet 创建包含指定来源的集合
func NewOrderSourceSet(sources ...OrderSource) OrderSourceSet {
	set := make(OrderSourceSet)
	for _, s := range sources {
		set[s] = struct{}{}
	}

	return set
}

// Contains 检查集合中是否包含指定来源
func (s OrderSourceSet) Contains(source OrderSource) bool {
	_, exists := s[source]

	return exists
}
