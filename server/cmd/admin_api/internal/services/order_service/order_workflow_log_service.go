package order_service

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"
)

// CloseOrderWorkflowLog .
// @router /order/CloseOrderWorkflowLog [POST]
func CloseOrderWorkflowLog(ctx context.Context, c *app.RequestContext) {
	funcName := getFuncName(1)

	var err error
	var req admin_api.CloseOrderWorkflowLogReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	query := &order.CloseOrderWorkflowLogReq{
		Ids:      req.Ids,
		ClosedBy: ctxmeta.MustGetAuth(ctx).EmployeeId(),
		Base:     nil,
	}

	respRpc, err := global.OrderClient.CloseOrderWorkflowLog(ctx, query)
	errWrap := coderror.Warp(respRpc, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.CloseOrderWorkflowLog rpc error: %v", funcName, err)
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	resp := new(admin_api.CloseOrderWorkflowLogRsp)

	httputil.ResponseSuccess(c, resp)
}

// GetOrderWorkflowLog .
// @router /order/GetOrderWorkflowLog [POST]
func GetOrderWorkflowLog(ctx context.Context, c *app.RequestContext) {
	funcName := getFuncName(1)

	var err error
	var req admin_api.GetOrderWorkflowLogReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	orderBySli := make([]*order.OrderBy, 0, len(req.GetOrderBy()))
	for _, item := range req.GetOrderBy() {
		orderByItem := &order.OrderBy{}
		orderByItem.Field = item.GetField()
		orderByItem.Type = order.OrderByType(item.GetType())
		orderBySli = append(orderBySli, orderByItem)
	}

	query := &order.GetOrderWorkflowLogReq{
		PageNum:     req.GetPageNum(),
		PageSize:    req.GetPageSize(),
		UpdaterIds:  req.GetUpdaterIds(),
		CustomerIds: req.GetCustomerIds(),
		OrderBy:     orderBySli,
		Base:        nil,
	}

	// TODO LIUSHUANG 数据权限
	// 数据权限
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	if employeeId > 0 {
		_, _, _, customerIds, employeeIds, returnBreak := getDataPermissionIds(ctx, c, funcName, employeeId)
		if returnBreak {
			return
		}

		if len(req.GetUpdaterIds()) == 0 {
			query.UpdaterIds = employeeIds
		}

		if len(req.GetCustomerIds()) == 0 {
			query.CustomerIds = customerIds
		}
	}

	respRpc, err := global.OrderClient.GetOrderWorkflowLog(ctx, query)
	errWrap := coderror.Warp(respRpc, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetOrderWorkflowLog rpc error: %v", funcName, err)
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	customerIdsMap := make(map[int64]struct{})
	for _, item := range respRpc.GetItems() {
		customerId := item.GetCustomerId()
		if customerId > 0 {
			customerIdsMap[customerId] = struct{}{}
		}
	}

	customerIds := make([]int64, 0)
	for customerId := range customerIdsMap {
		if customerId > 0 {
			customerIds = append(customerIds, customerId)
		}
	}

	// 获取客户信息
	rpcResp, err := global.CustomerProfileClient.GetCustomersByIds(ctx, &customer.GetCustomersByIdsReq{
		CustomerIds: customerIds,
	})
	if err != nil {
		httputil.ResponseInternalErr(c)
		return
	}

	customerMap := make(map[int64]*admin_api.CustomerEntity)
	for _, item := range rpcResp.GetCustomers() {
		// 客户信息
		customerMap[int64(item.GetId())] = customerEntityApiToSrv(item)
	}

	resp := new(admin_api.GetOrderWorkflowLogRsp)

	items := make([]*admin_api.OrderWorkflowLogItem, 0, len(respRpc.GetItems()))
	for _, item := range respRpc.GetItems() {
		customerInfo := customerMap[item.GetCustomerId()]
		items = append(items, getOrderWorkflowLogItemSrvToApi(item, customerInfo))
	}

	resp.Total = respRpc.GetTotal()
	resp.Items = items

	httputil.ResponseSuccess(c, resp)
}
