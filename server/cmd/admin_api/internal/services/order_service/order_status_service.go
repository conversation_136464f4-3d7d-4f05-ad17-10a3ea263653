package order_service

import (
	"context"
	"uofferv2/kitex_gen/base"
	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/message"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/ws_message"
	"uofferv2/pkg/i18n"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"

	"github.com/cloudwego/hertz/pkg/app"
)

// getOrderInfoForCurrentStatus 获取订单信息
//
//	Description: 获取订单信息，并判断是否与操作的当前订单状态匹配，不匹配直接抛 httputil.ResponseError
//	param ctx
//	param c
//	param funcName
//	param orderId
//	param currentStatus 调用方法的订单状态，用于判断是否与操作的当前订单状态匹配（传0不判断）
//	return resp
//	return returnBreak true：responseErr，请求处直接返回，不做任何处理，防止接口出现多条 response
func getOrderInfoForCurrentStatus(ctx context.Context, c *app.RequestContext, funcName string, orderId int64, currentStatus order.StatusOrder) (respRpc *order.GetOrderInfoRsp, returnBreak bool) {
	// 获取订单信息
	respRpc, returnBreak = getOrderInfo(ctx, c, orderId, "", funcName)
	if returnBreak {
		return
	}

	oldOrderStatus := respRpc.GetOrder().GetStatus()

	// 订单状态
	// 当前操作订单状态 != 实际订单状态
	// 非目标状态 -> 操作状态不被允许
	if currentStatus > 0 && currentStatus != oldOrderStatus {
		logger.CtxInfof(ctx, "%s OrderStatusNotPermission order_id: %d"+
			"old_order_status: %d", funcName, orderId, oldOrderStatus)
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
		returnBreak = true
		return
	}

	return
}

// DisbursementOrder 撤销订单审核
//
//	Description:
//		可操作订单状态：1%已下定金|2%支付待确认|3%尾款待支付|5%支款订单
//	 	可操作审核状态：3%草稿待审核|4%驳回待审核
//	param ctx
//	param c
//	param funcName
//	param orderId
//	param currentStatus 调用方法的订单状态，用于判断是否与操作的当前订单状态匹配
//	return returnBreak true：responseErr，请求处直接返回，不做任何处理，防止接口出现多条 response
func DisbursementOrder(ctx context.Context, c *app.RequestContext, funcName string, orderId int64, currentStatus order.StatusOrder) (returnBreak bool) {
	// 获取订单信息
	respRpc, returnBreak := getOrderInfoForCurrentStatus(ctx, c, funcName, orderId, currentStatus)
	if returnBreak {
		return
	}

	oldOrderInfo := respRpc.GetOrder()
	oldOrderStatus := oldOrderInfo.GetStatus()
	oldStatusReview := oldOrderInfo.GetStatusReview()

	// 订单状态
	// 当前操作订单状态 != 实际订单状态
	// 非目标状态 -> 操作状态不被允许
	if oldOrderStatus != currentStatus {
		logger.CtxInfof(ctx, "%s OrderStatusNotPermission order_id: %d"+
			"old_order_status: %d", funcName, orderId, oldOrderStatus)
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
		returnBreak = true
		return
	}

	// 撤销订单审核
	// 审核状态：3%草稿待审核|4%驳回待审核 -> 2%草稿|5%审核驳回
	// 非3%草稿待审核|4%驳回待审核 -> 操作状态不被允许
	if oldStatusReview != order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT &&
		oldStatusReview != order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT {
		logger.CtxInfof(ctx, "%s OrderStatusNotPermission order_id: %d"+
			"old_order_status_review: %d", funcName, orderId, oldStatusReview)
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
		returnBreak = true
		return
	}

	// 3%草稿待审核 -> 2%草稿
	newStatusReview := order.StatusOrderReview_STATUS_REVIEW_DRAFT
	// 4%驳回待审核 -> 5%审核驳回
	if oldStatusReview == order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT {
		newStatusReview = order.StatusOrderReview_STATUS_REVIEW_REJECT
	}

	authInfo := httputil.MustGetAuthInfoFromContext(ctx, c)

	query := order.UpdateOrderStatusReq{
		Id:           orderId,
		Status:       oldOrderStatus,
		StatusReview: newStatusReview,
		Base:         &base.BaseReq{AuthInfo: httputil.ConvertToAuthInfo(authInfo)},
	}
	// 更新订单状态
	respUpdateRpc, err := global.OrderClient.UpdateOrderStatus(ctx, &query)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.UpdateOrderStatus rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		returnBreak = true
		return
	}
	/*
		//更新留言板
		messageBoardReq := &message.UpdateMessageBoardReq{
			RelationId:     orderId,
			RelationStatus: int32(newStatusReview),
			RelationType:   ws_message.MessageBoardRelationType_MESSAGE_BOARD_RELATION_TYPE_ORDER,
		}
		_, err = global.MessageClient.UpdateMessageBoard(ctx, messageBoardReq)

	*/

	if respUpdateRpc.GetBase().GetCode() != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.UpdateOrderStatus rpc base error %v", funcName, respUpdateRpc.GetBase())
		httputil.ResponseErrorCodeWithBase(c, respUpdateRpc.GetBase())
		returnBreak = true
		return
	}

	return
}

// CloseOrder 关闭订单
//
//	Description:
//		可操作订单状态：1%已下定金|2%支付待确认
//	 	可操作审核状态：2%草稿|5%审核驳回
//	param ctx
//	param c
//	param funcName
//	param orderId
//	param currentStatus 调用方法的订单状态，用于判断是否与操作的当前订单状态匹配
//	return returnBreak true：responseErr，请求处直接返回，不做任何处理，防止接口出现多条 response
func CloseOrder(ctx context.Context, c *app.RequestContext, funcName string, orderId int64, currentStatus order.StatusOrder) (returnBreak bool) {
	// 获取订单信息
	respRpc, returnBreak := getOrderInfoForCurrentStatus(ctx, c, funcName, orderId, currentStatus)
	if returnBreak {
		return
	}

	oldOrderInfo := respRpc.GetOrder()
	oldOrderStatus := oldOrderInfo.GetStatus()
	oldStatusReview := oldOrderInfo.GetStatusReview()

	// 订单状态
	// 当前操作订单状态 != 实际订单状态
	// 非目标状态 -> 操作状态不被允许
	if oldOrderStatus != currentStatus {
		logger.CtxInfof(ctx, "%s OrderStatusNotPermission order_id: %d"+
			"old_order_status: %d", funcName, orderId, oldOrderStatus)
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
		returnBreak = true
		return
	}

	// 关闭订单
	// 审核状态：2%草稿|5%审核驳回
	// 非2%草稿|5%审核驳回 -> 操作状态不被允许
	if oldStatusReview != order.StatusOrderReview_STATUS_REVIEW_DRAFT &&
		oldStatusReview != order.StatusOrderReview_STATUS_REVIEW_REJECT {
		logger.CtxInfof(ctx, "%s OrderStatusNotPermission order_id: %d"+
			"old_order_status_review: %d", funcName, orderId, oldStatusReview)
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
		returnBreak = true
		return
	}

	authInfo := httputil.MustGetAuthInfoFromContext(ctx, c)

	query := order.UpdateOrderStatusReq{
		Id:           orderId,
		Status:       order.StatusOrder_STATUS_ORDER_CLOSE,
		StatusReview: oldStatusReview,
		Base:         &base.BaseReq{AuthInfo: httputil.ConvertToAuthInfo(authInfo)},
	}
	// 更新订单状态
	respUpdateRpc, err := global.OrderClient.UpdateOrderStatus(ctx, &query)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.UpdateOrderStatus rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		returnBreak = true
		return
	}

	//更新留言板
	messageBoardReq := &message.UpdateMessageBoardReq{
		RelationId:     orderId,
		RelationStatus: int32(order.StatusOrder_STATUS_ORDER_CLOSE),
		RelationType:   ws_message.MessageBoardRelationType_MESSAGE_BOARD_RELATION_TYPE_ORDER,
	}
	_, err = global.MessageClient.UpdateMessageBoard(ctx, messageBoardReq)

	if respUpdateRpc.GetBase().GetCode() != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.UpdateOrderStatus rpc base error %v", funcName, respUpdateRpc.GetBase())
		httputil.ResponseErrorCodeWithBase(c, respUpdateRpc.GetBase())
		returnBreak = true
		return
	}

	return
}

// ReviewRejectOrder 订单审核驳回
//
//	Description:
//		可操作订单状态：1%已下定金|2%支付待确认|3%尾款待支付|5%支款订单
//	 	可操作审核状态：3%草稿待审核|4%驳回待审核 -> 5%审核驳回
//	param ctx
//	param c
//	param funcName
//	param orderId 订单ID
//	param currentStatus 调用方法的订单状态，用于判断是否与操作的当前订单状态匹配（传0不判断）
//	return returnBreak true：responseErr，请求处直接返回，不做任何处理，防止接口出现多条 response
func ReviewRejectOrder(ctx context.Context, c *app.RequestContext, funcName string, orderId int64, currentStatus order.StatusOrder) (returnBreak bool) {
	// 获取订单信息
	respRpc, returnBreak := getOrderInfoForCurrentStatus(ctx, c, funcName, orderId, currentStatus)
	if returnBreak {
		return
	}

	oldOrderInfo := respRpc.GetOrder()
	oldOrderStatus := oldOrderInfo.GetStatus()
	oldStatusReview := oldOrderInfo.GetStatusReview()

	// 订单状态
	// 当前操作订单状态 != 实际订单状态
	// 非目标状态 -> 操作状态不被允许
	if oldOrderStatus != currentStatus {
		logger.CtxInfof(ctx, "%s OrderStatusNotPermission order_id: %d"+
			"old_order_status: %d", funcName, orderId, oldOrderStatus)
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
		returnBreak = true
		return
	}

	// 撤销订单审核
	// 审核状态：3%草稿待审核|4%驳回待审核 -> 5%审核驳回
	// 非3%草稿待审核|4%驳回待审核 -> 操作状态不被允许
	if oldStatusReview != order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT &&
		oldStatusReview != order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT {
		logger.CtxInfof(ctx, "%s OrderStatusNotPermission order_id: %d"+
			"old_order_status_review: %d", funcName, orderId, oldStatusReview)
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
		returnBreak = true
		return
	}

	// 3%草稿待审核|4%驳回待审核 -> 5%审核驳回
	newStatusReview := order.StatusOrderReview_STATUS_REVIEW_REJECT

	authInfo := httputil.MustGetAuthInfoFromContext(ctx, c)

	query := order.UpdateOrderStatusReq{
		Id:           orderId,
		Status:       oldOrderStatus,
		StatusReview: newStatusReview,
		Base:         &base.BaseReq{AuthInfo: httputil.ConvertToAuthInfo(authInfo)},
	}
	// 更新订单状态
	respUpdateRpc, err := global.OrderClient.UpdateOrderStatus(ctx, &query)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.UpdateOrderStatus rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		returnBreak = true
		return
	}

	if respUpdateRpc.GetBase().GetCode() != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.UpdateOrderStatus rpc base error %v", funcName, respUpdateRpc.GetBase())
		httputil.ResponseErrorCodeWithBase(c, respUpdateRpc.GetBase())
		returnBreak = true
		return
	}

	return
}

// ReviewPassOrder 订单审核通过
//
//	Description:
//		可操作订单状态：1%已下定金|2%支付待确认|3%尾款待支付|5%支款订单
//	 	可操作审核状态：3%草稿待审核|4%驳回待审核 -> 1%审核通过
//	param ctx
//	param c
//	param funcName
//	param orderId
//	param currentStatus 调用方法的订单状态，用于判断是否与操作的当前订单状态匹配（传0不判断）
//	param reviewComment 审核意见
//	param transactionNo 交易单号
//	return returnBreak true：responseErr，请求处直接返回，不做任何处理，防止接口出现多条 response
func ReviewPassOrder(ctx context.Context, c *app.RequestContext, funcName string, orderId int64, currentStatus order.StatusOrder, reviewComment string, transactionNo string) (returnBreak bool) {
	// 获取订单信息
	respRpc, returnBreak := getOrderInfoForCurrentStatus(ctx, c, funcName, orderId, currentStatus)
	if returnBreak {
		return
	}

	oldOrderInfo := respRpc.GetOrder()
	oldOrderStatus := oldOrderInfo.GetStatus()
	oldStatusReview := oldOrderInfo.GetStatusReview()

	// 订单状态
	// 当前操作订单状态 != 实际订单状态
	// 非目标状态 -> 操作状态不被允许
	if oldOrderStatus != currentStatus {
		logger.CtxInfof(ctx, "%s OrderStatusNotPermission order_id: %d"+
			"old_order_status: %d", funcName, orderId, oldOrderStatus)
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
		returnBreak = true
		return
	}

	// 撤销订单审核
	// 审核状态：3%草稿待审核|4%驳回待审核 -> 1%审核通过
	// 非3%草稿待审核|4%驳回待审核 -> 操作状态不被允许
	if oldStatusReview != order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT &&
		oldStatusReview != order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT {
		logger.CtxInfof(ctx, "%s OrderStatusNotPermission order_id: %d"+
			"old_order_status_review: %d", funcName, orderId, oldStatusReview)
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
		returnBreak = true
		return
	}

	// 3%草稿待审核|4%驳回待审核 -> 1%审核通过
	newStatusReview := order.StatusOrderReview_STATUS_REVIEW_PASS

	authInfo := httputil.MustGetAuthInfoFromContext(ctx, c)

	query := order.UpdateOrderStatusReq{
		Id:           orderId,
		Status:       oldOrderStatus,
		StatusReview: newStatusReview,
		// TODO: 等protobuf代码生成后启用这些字段
		// ReviewComment: reviewComment,
		// TransactionNo: transactionNo,
		Base: &base.BaseReq{AuthInfo: httputil.ConvertToAuthInfo(authInfo)},
	}
	// 更新订单状态
	respUpdateRpc, err := global.OrderClient.UpdateOrderStatus(ctx, &query)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.UpdateOrderStatus rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		returnBreak = true
		return
	}

	if respUpdateRpc.GetBase().GetCode() != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.UpdateOrderStatus rpc base error %v", funcName, respUpdateRpc.GetBase())
		httputil.ResponseErrorCodeWithBase(c, respUpdateRpc.GetBase())
		returnBreak = true
		return
	}

	return
}
