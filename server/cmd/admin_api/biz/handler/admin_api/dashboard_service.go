// Code generated by hertz generator.

package admin_api

import (
	"context"

	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	dashboardservice "uofferv2/server/cmd/admin_api/internal/services/dashboard_service"

	"github.com/cloudwego/hertz/pkg/app"
)

var _ = admin_api.HealthCheckReq{} // 使代码自动编译通过

// GetPendingTaskStats .
// @router /dashboard/GetPendingTaskStats [POST]
func GetPendingTaskStats(ctx context.Context, c *app.RequestContext) {
	dashboardservice.GetPendingTaskStats(ctx, c)
}

// GetKeyDataStats .
// @router /dashboard/GetKeyDataStats [POST]
func GetKeyDataStats(ctx context.Context, c *app.RequestContext) {
	dashboardservice.GetKeyDataStats(ctx, c)
}
