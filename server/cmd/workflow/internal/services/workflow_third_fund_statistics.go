package services

import (
	"context"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/cross_models"
)

type WorkflowThirdFundStatisticsService struct {
}

func NewWorkflowThirdFundStatisticsService() *WorkflowThirdFundStatisticsService {
	return &WorkflowThirdFundStatisticsService{}
}

func (s *WorkflowThirdFundStatisticsService) CurrentThirdFundWorkflow(ctx context.Context, req *workflow.CurrentThirdFundWorkflowReq) (*workflow.CurrentThirdFundWorkflowRsp, error) {
	resp := &workflow.CurrentThirdFundWorkflowRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	records, err := cross_models.NewFinancialFundDao().GetThirdFundByApproveStatus(ctx, int32(req.ApproveStatus))
	if err != nil {
		logger.CtxErrorf(ctx, "GetThirdFundWorkflowIds failed, err: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	var workflowIds []int64
	timeMap := make(map[int64]int64)
	for _, record := range records {
		workflowIds = append(workflowIds, record.WorkflowID)
		if record.ApproveStatus == int32(workflow.ThirdFundWorkflowStatus_ThirdFundWorkflowStatusApprove) && record.PassTime != nil {
			timeMap[record.WorkflowID] = record.PassTime.UnixMilli()
		} else if record.ApproveStatus == int32(workflow.ThirdFundWorkflowStatus_ThirdFundWorkflowStatusReject) && record.RejectTime != nil {
			timeMap[record.WorkflowID] = record.RejectTime.UnixMilli()
		}
	}

	listReq := &workflow.ListWorkflowReq{
		WorkflowStatus: workflow.WorkflowStatus_WORKFLOW_STATUS_IN_SERVICE,
		WorkflowIds:    workflowIds,
		BusinessInfo: []*workflow.BusinessInfo{
			{
				BusinessType:    workflow.WorkflowBusinessType_WORKFLOW_BUSINESS_TYPE_APPLICATION,
				TemplateId:      100,
				CurrentNodeName: "收取第三方申请费",
			},
			{
				BusinessType:    workflow.WorkflowBusinessType_WORKFLOW_BUSINESS_TYPE_APPLICATION,
				TemplateId:      101,
				CurrentNodeName: "收取第三方申请费",
			},
		},
		PageNum:  int32(req.PageNum),
		PageSize: int32(req.PageSize),
	}

	listResp, err := ListWorkflowV1(ctx, listReq)
	if err != nil {
		logger.CtxErrorf(ctx, "CurrentThirdFundWorkflow ListWorkflowV1 failed, err: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	for _, workflowInfo := range listResp.Items {
		item := &workflow.WorkflowThirdFundEntity{
			WorkflowInfo:  workflowInfo,
			OperationTime: timeMap[workflowInfo.WorkflowInfo.Id],
		}
		resp.Items = append(resp.Items, item)
	}
	resp.Total = listResp.Total
	return resp, nil
}
