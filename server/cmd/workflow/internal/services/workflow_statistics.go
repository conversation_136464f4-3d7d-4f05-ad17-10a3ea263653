package services

import (
	"context"
	"encoding/json"
	"time"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/models"
)

// WfStat 工作流统计数据结构
type WfStat struct {
	AssignedWorkflows     int64
	ReceivedWorkflows     int64
	CompletedWorkflows    int64
	ServedCustomers       int64
	AverageProcessingTime int64
}

type WfNodeStat struct {
	AssignedWorkflowNodes     int64
	ReceivedWorkflowNodes     int64
	CompletedWorkflowNodes    int64
	AverageProcessingDuration int64
}

// StatField 定义统计字段类型，用于消除重复代码
type StatField struct {
	current  int64
	previous int64
	target   *workflow.PeriodComparisonStat
}

// GetWorkflowStatistics 获取工作流统计信息
func GetWorkflowStatistics(ctx context.Context, req *workflow.GetStatsRequest) (resp *workflow.GetWorkflowStatsRsp, err error) {
	// 初始化响应结构
	resp = &workflow.GetWorkflowStatsRsp{
		WorkflowStats: &workflow.WorkflowStats{
			AssignedWorkflows:         &workflow.PeriodComparisonStat{IsValid: true},
			ReceivedWorkflows:         &workflow.PeriodComparisonStat{IsValid: true},
			CompletedWorkflows:        &workflow.PeriodComparisonStat{IsValid: true},
			ServedCustomers:           &workflow.PeriodComparisonStat{IsValid: true},
			AverageProcessingDuration: &workflow.PeriodComparisonStat{IsValid: true},
		},
	}

	// 获取基础参数
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	startDate := time.UnixMilli(req.StartDate)
	endDate := time.UnixMilli(req.EndDate)

	// 计算时间范围（天数）
	daysDiff := int(endDate.Sub(startDate).Hours() / 24)
	previousStartDate := startDate.AddDate(0, 0, -daysDiff)

	// 获取当前期间和前一期间的统计数据
	currentWfStat, err := getWorkflowStatForPeriod(ctx, employeeId, startDate, endDate)
	if err != nil {
		logger.CtxErrorf(ctx, "获取当前期间工作流统计失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	previousWfStat, err := getWorkflowStatForPeriod(ctx, employeeId, previousStartDate, startDate)
	if err != nil {
		logger.CtxErrorf(ctx, "获取前一期间工作流统计失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 批量设置统计字段
	statFields := []StatField{
		{currentWfStat.AssignedWorkflows, previousWfStat.AssignedWorkflows, resp.WorkflowStats.AssignedWorkflows},
		{currentWfStat.ReceivedWorkflows, previousWfStat.ReceivedWorkflows, resp.WorkflowStats.ReceivedWorkflows},
		{currentWfStat.CompletedWorkflows, previousWfStat.CompletedWorkflows, resp.WorkflowStats.CompletedWorkflows},
		{currentWfStat.ServedCustomers, previousWfStat.ServedCustomers, resp.WorkflowStats.ServedCustomers},
		{currentWfStat.AverageProcessingTime, previousWfStat.AverageProcessingTime, resp.WorkflowStats.AverageProcessingDuration},
	}

	// 统一设置所有统计字段
	for _, field := range statFields {
		setStatField(field.target, field.current, field.previous)
	}

	return resp, nil
}

// getWorkflowStatForPeriod 获取指定时间段的工作流统计数据
func getWorkflowStatForPeriod(ctx context.Context, employeeId int64, startDate, endDate time.Time) (*WfStat, error) {
	stats, err := models.GetWorkflowStatByEmployeeInDateRange(ctx, employeeId, startDate, endDate)
	if err != nil {
		return nil, err
	}

	return calculateWorkflowStat(ctx, stats)
}

// setStatField 设置统计字段的当前值、历史值、有效性和百分比变化
func setStatField(target *workflow.PeriodComparisonStat, current, previous int64) {
	target.CurrentPeriodCount = current
	target.PreviousPeriodCount = previous

	// 如果前一期间数据为0，标记为无效
	if previous == 0 {
		target.IsValid = false
		target.PercentageChange = 0
	} else {
		target.IsValid = true
		target.PercentageChange = calculatePercentageChange(current, previous)
	}
}

// calculatePercentageChange 安全地计算百分比变化
func calculatePercentageChange(current, previous int64) float64 {
	if previous == 0 {
		return 0
	}
	return float64(current-previous) / float64(previous)
}

// calculateWorkflowStat 计算工作流统计数据
func calculateWorkflowStat(ctx context.Context, stats []*model.WorkflowStatisticsDaily) (*WfStat, error) {
	var (
		assignedWorkflows  int
		receivedWorkflows  int
		completedWorkflows int
		totalProcessTime   int
		allCustomerIDs     []int64
	)

	for _, stat := range stats {
		// 解析客户ID列表
		var customerIDs []int64
		if err := json.Unmarshal([]byte(stat.CustomerIds), &customerIDs); err != nil {
			logger.CtxErrorf(ctx, "解析客户ID失败: %v", err)
			return nil, err
		}

		// 累加各项统计数据
		allCustomerIDs = append(allCustomerIDs, customerIDs...)
		assignedWorkflows += int(stat.DispatchedWfCount)
		receivedWorkflows += int(stat.AcceptedWfCount)
		completedWorkflows += int(stat.CompletedWfCount)
		totalProcessTime += int(stat.TotalProcessTime)
	}

	// 去重客户ID
	uniqueCustomerIDs := RemoveDuplicates(allCustomerIDs, true)

	// 计算平均处理时长
	var averageProcessingTime int64
	if completedWorkflows > 0 {
		averageProcessingTime = int64(totalProcessTime / completedWorkflows)
	}

	return &WfStat{
		AssignedWorkflows:     int64(assignedWorkflows),
		ReceivedWorkflows:     int64(receivedWorkflows),
		CompletedWorkflows:    int64(completedWorkflows),
		ServedCustomers:       int64(len(uniqueCustomerIDs)),
		AverageProcessingTime: averageProcessingTime,
	}, nil
}

// GetWorkflowNodeStatistics 获取工作流节点统计信息
func GetWorkflowNodeStatistics(ctx context.Context, req *workflow.GetStatsRequest) (resp *workflow.GetWorkflowNodeStatsRsp, err error) {
	resp = &workflow.GetWorkflowNodeStatsRsp{
		WorkflowNodeStats: &workflow.WorkflowNodeStats{
			AssignedWorkflowNodes:     &workflow.PeriodComparisonStat{IsValid: true},
			ReceivedWorkflowNodes:     &workflow.PeriodComparisonStat{IsValid: true},
			CompletedWorkflowNodes:    &workflow.PeriodComparisonStat{IsValid: true},
			AverageProcessingDuration: &workflow.PeriodComparisonStat{IsValid: true},
		},
	}

	// TODO: 实现工作流节点统计逻辑
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	startDate := time.UnixMilli(req.StartDate)
	endDate := time.UnixMilli(req.EndDate)

	// 计算时间范围（天数）
	daysDiff := int(endDate.Sub(startDate).Hours() / 24)
	previousStartDate := startDate.AddDate(0, 0, -daysDiff)

	currentWfNodeStat, err := getWorkflowNodeStatForPeriod(ctx, employeeId, startDate, endDate)
	if err != nil {
		logger.CtxErrorf(ctx, "获取当前期间工作流节点统计失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	previousWfNodeStat, err := getWorkflowNodeStatForPeriod(ctx, employeeId, previousStartDate, startDate)
	if err != nil {
		logger.CtxErrorf(ctx, "获取前一期间工作流节点统计失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	statFields := []StatField{
		{currentWfNodeStat.AssignedWorkflowNodes, previousWfNodeStat.AssignedWorkflowNodes, resp.WorkflowNodeStats.AssignedWorkflowNodes},
		{currentWfNodeStat.ReceivedWorkflowNodes, previousWfNodeStat.ReceivedWorkflowNodes, resp.WorkflowNodeStats.ReceivedWorkflowNodes},
		{currentWfNodeStat.CompletedWorkflowNodes, previousWfNodeStat.CompletedWorkflowNodes, resp.WorkflowNodeStats.CompletedWorkflowNodes},
		{currentWfNodeStat.AverageProcessingDuration, previousWfNodeStat.AverageProcessingDuration, resp.WorkflowNodeStats.AverageProcessingDuration},
	}

	for _, field := range statFields {
		setStatField(field.target, field.current, field.previous)
	}

	return resp, nil
}

func getWorkflowNodeStatForPeriod(ctx context.Context, employeeId int64, startDate, endDate time.Time) (*WfNodeStat, error) {
	stats, err := models.GetWorkflowNodeStatisticsDailyByEmployeeInDateRange(ctx, employeeId, startDate, endDate)
	if err != nil {
		return nil, err
	}

	return calculateWorkflowNodeStat(ctx, stats)
}

func calculateWorkflowNodeStat(ctx context.Context, stats []*model.WorkflowNodeStatisticsDaily) (*WfNodeStat, error) {
	var (
		totalProcessTime         int
		assignedWorkflowNodeIDs  []int64
		receivedWorkflowNodeIDs  []int64
		completedWorkflowNodeIDs []int64
	)

	for _, stat := range stats {
		var dispatchedNodeIDs []int64
		var acceptedNodeIDs []int64
		var completedNodeIDs []int64
		if err := json.Unmarshal([]byte(stat.DispatchedNodeIds), &dispatchedNodeIDs); err != nil {
			logger.CtxErrorf(ctx, "解析新增节点IDs失败: %v", err)
			return nil, err
		}
		if err := json.Unmarshal([]byte(stat.ProcessingNodeIds), &acceptedNodeIDs); err != nil {
			logger.CtxErrorf(ctx, "解析处理中的节点IDs失败: %v", err)
			return nil, err
		}
		if err := json.Unmarshal([]byte(stat.CompletedNodeIds), &completedNodeIDs); err != nil {
			logger.CtxErrorf(ctx, "解析完成的节点IDs失败: %v", err)
			return nil, err
		}
		totalProcessTime += int(stat.TotalProcessTime)
		assignedWorkflowNodeIDs = append(assignedWorkflowNodeIDs, dispatchedNodeIDs...)
		receivedWorkflowNodeIDs = append(receivedWorkflowNodeIDs, acceptedNodeIDs...)
		completedWorkflowNodeIDs = append(completedWorkflowNodeIDs, completedNodeIDs...)
	}

	uniqueAssignedWorkflowNodeIDs := RemoveDuplicates(assignedWorkflowNodeIDs, true)
	uniqueReceivedWorkflowNodeIDs := RemoveDuplicates(receivedWorkflowNodeIDs, true)
	uniqueCompletedWorkflowNodeIDs := RemoveDuplicates(completedWorkflowNodeIDs, true)

	var averageProcessingTime int64
	if len(uniqueCompletedWorkflowNodeIDs) > 0 {
		averageProcessingTime = int64(totalProcessTime / len(uniqueCompletedWorkflowNodeIDs))
	}

	return &WfNodeStat{
		AssignedWorkflowNodes:     int64(len(uniqueAssignedWorkflowNodeIDs)),
		ReceivedWorkflowNodes:     int64(len(uniqueReceivedWorkflowNodeIDs)),
		CompletedWorkflowNodes:    int64(len(uniqueCompletedWorkflowNodeIDs)),
		AverageProcessingDuration: averageProcessingTime,
	}, nil
}
