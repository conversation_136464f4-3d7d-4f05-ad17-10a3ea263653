package models

import (
	"context"
	"testing"
	"time"

	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/dao/model"

	"github.com/stretchr/testify/assert"
	"gorm.io/plugin/soft_delete"
)

// TestGenerateOrderBySQL 测试 OrderBy 转换为 SQL 子句功能
func TestGenerateOrderBySQL(t *testing.T) {
	tests := []struct {
		name     string
		orderBy  []*workflow.WorkflowOrderBy
		expected string
	}{
		{
			name:     "空切片",
			orderBy:  []*workflow.WorkflowOrderBy{},
			expected: "",
		},
		{
			name:     "nil 输入",
			orderBy:  nil,
			expected: "",
		},
		{
			name: "单个 ASC 排序",
			orderBy: []*workflow.WorkflowOrderBy{
				{
					Field: "created_at",
					Type:  workflow.WorkflowOrderByType_ORDER_BY_ASC,
				},
			},
			expected: " created_at ASC ",
		},
		{
			name: "单个 DESC 排序",
			orderBy: []*workflow.WorkflowOrderBy{
				{
					Field: "updated_at",
					Type:  workflow.WorkflowOrderByType_ORDER_BY_DESC,
				},
			},
			expected: " updated_at DESC ",
		},
		{
			name: "多个排序字段",
			orderBy: []*workflow.WorkflowOrderBy{
				{
					Field: "status",
					Type:  workflow.WorkflowOrderByType_ORDER_BY_ASC,
				},
				{
					Field: "created_at",
					Type:  workflow.WorkflowOrderByType_ORDER_BY_DESC,
				},
				{
					Field: "id",
					Type:  workflow.WorkflowOrderByType_ORDER_BY_ASC,
				},
			},
			expected: " status ASC, created_at DESC, id ASC ",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GenerateOrderBySQL(tt.orderBy)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestSplitToInt64 测试字符串转换为 int64 切片功能
func TestSplitToInt64(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name       string
		processors string
		expected   []int64
		expectErr  bool
	}{
		{
			name:       "空字符串",
			processors: "",
			expected:   []int64{},
			expectErr:  false,
		},
		{
			name:       "单个数字",
			processors: "123",
			expected:   []int64{123},
			expectErr:  false,
		},
		{
			name:       "多个数字用逗号分隔",
			processors: "1,2,3,4,5",
			expected:   []int64{1, 2, 3, 4, 5},
			expectErr:  false,
		},
		{
			name:       "包含空格的数字",
			processors: " 1 , 2 , 3 ",
			expected:   []int64{1, 2, 3},
			expectErr:  false,
		},
		{
			name:       "包含空字段",
			processors: "1,,3,,5",
			expected:   []int64{1, 3, 5},
			expectErr:  false,
		},
		{
			name:       "负数",
			processors: "-1,-2,3",
			expected:   []int64{-1, -2, 3},
			expectErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := SplitToInt64(ctx, tt.processors)

			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

// TestIntSlice2Str 测试 int64 切片转换为字符串功能
func TestIntSlice2Str(t *testing.T) {
	tests := []struct {
		name     string
		ids      []int64
		expected string
	}{
		{
			name:     "空切片",
			ids:      []int64{},
			expected: "",
		},
		{
			name:     "nil 切片",
			ids:      nil,
			expected: "",
		},
		{
			name:     "单个元素",
			ids:      []int64{123},
			expected: "123",
		},
		{
			name:     "多个元素",
			ids:      []int64{1, 2, 3, 4, 5},
			expected: "1,2,3,4,5",
		},
		{
			name:     "包含负数",
			ids:      []int64{-1, 0, 1},
			expected: "-1,0,1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IntSlice2Str(tt.ids)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestTaskModel2Entity 测试 WorkflowNodeTask 模型转换为实体功能
func TestTaskModel2Entity(t *testing.T) {
	now := time.Now()

	tests := []struct {
		name     string
		src      *model.WorkflowNodeTask
		expected *workflow.WorkflowNodeTaskEntity
	}{
		{
			name:     "nil 输入",
			src:      nil,
			expected: nil,
		},
		{
			name: "基本模型转换",
			src: &model.WorkflowNodeTask{
				ID:             123,
				Name:           "测试任务",
				Status:         1, // 使用数字值而不是枚举
				CreatedAt:      now,
				StartedAt:      &now,
				UpdatedAt:      now,
				DeletedAt:      soft_delete.DeletedAt(0),
				CompletedAt:    &now,
				DueAt:          &now,
				WorkflowNodeID: 456,
			},
			expected: &workflow.WorkflowNodeTaskEntity{
				Id:             123,
				Name:           "测试任务",
				Status:         workflow.WorkflowNodeTaskStatus(1),
				CreatedAt:      now.UnixMilli(),
				StartedAt:      now.UnixMilli(),
				UpdatedAt:      now.UnixMilli(),
				DeletedAt:      0,
				CompletedAt:    now.UnixMilli(),
				DueAt:          now.UnixMilli(),
				WorkflowNodeId: 456,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := TaskModel2Entity(tt.src)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestTaskModelList2EntityList 测试任务模型列表转换为实体列表功能
func TestTaskModelList2EntityList(t *testing.T) {
	now := time.Now()

	tests := []struct {
		name     string
		src      []*model.WorkflowNodeTask
		expected []*workflow.WorkflowNodeTaskEntity
	}{
		{
			name:     "nil 输入",
			src:      nil,
			expected: nil,
		},
		{
			name:     "空切片",
			src:      []*model.WorkflowNodeTask{},
			expected: []*workflow.WorkflowNodeTaskEntity{},
		},
		{
			name: "多个任务转换",
			src: []*model.WorkflowNodeTask{
				{
					ID:             1,
					Name:           "任务1",
					Status:         int32(workflow.WorkflowNodeTaskStatus_WORKFLOW_NODE_TASK_STATUS_INCOMPLETE),
					CreatedAt:      now,
					UpdatedAt:      now,
					WorkflowNodeID: 100,
				},
				{
					ID:             2,
					Name:           "任务2",
					Status:         int32(workflow.WorkflowNodeTaskStatus_WORKFLOW_NODE_TASK_STATUS_COMPLETED),
					CreatedAt:      now,
					UpdatedAt:      now,
					WorkflowNodeID: 200,
				},
			},
			expected: []*workflow.WorkflowNodeTaskEntity{
				{
					Id:             1,
					Name:           "任务1",
					Status:         workflow.WorkflowNodeTaskStatus_WORKFLOW_NODE_TASK_STATUS_INCOMPLETE,
					CreatedAt:      now.UnixMilli(),
					StartedAt:      0,
					UpdatedAt:      now.UnixMilli(),
					DeletedAt:      0,
					CompletedAt:    0,
					DueAt:          0,
					WorkflowNodeId: 100,
				},
				{
					Id:             2,
					Name:           "任务2",
					Status:         workflow.WorkflowNodeTaskStatus_WORKFLOW_NODE_TASK_STATUS_COMPLETED,
					CreatedAt:      now.UnixMilli(),
					StartedAt:      0,
					UpdatedAt:      now.UnixMilli(),
					DeletedAt:      0,
					CompletedAt:    0,
					DueAt:          0,
					WorkflowNodeId: 200,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := TaskModelList2EntityList(tt.src)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestNodeModel2Entity 测试工作流节点模型转换为实体功能
func TestNodeModel2Entity(t *testing.T) {
	tests := []struct {
		name     string
		src      *model.WorkflowNode
		expected *workflow.SimpleCurrentNodeEntity
	}{
		{
			name:     "nil 输入",
			src:      nil,
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NodeModel2Entity(tt.src, nil, nil, nil)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestRemoveDuplicates 测试数组去重功能
func TestRemoveDuplicates(t *testing.T) {
	tests := []struct {
		name       string
		arr        []int64
		removeZero bool
		expected   []int64
	}{
		{
			name:       "空切片",
			arr:        []int64{},
			removeZero: false,
			expected:   []int64{},
		},
		{
			name:       "无重复元素",
			arr:        []int64{1, 2, 3, 4, 5},
			removeZero: false,
			expected:   []int64{1, 2, 3, 4, 5},
		},
		{
			name:       "有重复元素",
			arr:        []int64{1, 2, 2, 3, 3, 4, 5, 5},
			removeZero: false,
			expected:   []int64{1, 2, 3, 4, 5},
		},
		{
			name:       "包含0且不移除0",
			arr:        []int64{0, 1, 0, 2, 0, 3},
			removeZero: false,
			expected:   []int64{0, 1, 2, 3},
		},
		{
			name:       "包含0且移除0",
			arr:        []int64{0, 1, 0, 2, 0, 3},
			removeZero: true,
			expected:   []int64{1, 2, 3},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := RemoveDuplicates(tt.arr, tt.removeZero)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGetUnixTime 测试时间转换为 Unix 时间戳功能
func TestGetUnixTime(t *testing.T) {
	now := time.Now()

	tests := []struct {
		name     string
		input    interface{}
		expected int64
	}{
		{
			name:     "nil 输入",
			input:    nil,
			expected: 0,
		},
		{
			name:     "time.Time 值",
			input:    now,
			expected: now.UnixMilli(),
		},
		{
			name:     "*time.Time 指针",
			input:    &now,
			expected: now.UnixMilli(),
		},
		{
			name:     "*time.Time nil 指针",
			input:    (*time.Time)(nil),
			expected: 0,
		},
		{
			name:     "其他类型",
			input:    "not a time",
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getUnixTime(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGetUnixTimeWithSpecificTimes 测试特定时间的转换
func TestGetUnixTimeWithSpecificTimes(t *testing.T) {
	// 测试特定的时间值
	testTime := time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)
	expectedMilli := testTime.UnixMilli()

	// 测试 time.Time 值
	result1 := getUnixTime(testTime)
	assert.Equal(t, expectedMilli, result1)

	// 测试 *time.Time 指针
	result2 := getUnixTime(&testTime)
	assert.Equal(t, expectedMilli, result2)

	// 测试零时间
	zeroTime := time.Time{}
	result3 := getUnixTime(zeroTime)
	assert.Equal(t, zeroTime.UnixMilli(), result3)
}

// 基准测试

// BenchmarkGenerateOrderBySQL 测试 OrderBy SQL 生成性能
func BenchmarkGenerateOrderBySQL(b *testing.B) {
	orderBy := []*workflow.WorkflowOrderBy{
		{Field: "created_at", Type: workflow.WorkflowOrderByType_ORDER_BY_DESC},
		{Field: "updated_at", Type: workflow.WorkflowOrderByType_ORDER_BY_ASC},
		{Field: "status", Type: workflow.WorkflowOrderByType_ORDER_BY_DESC},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GenerateOrderBySQL(orderBy)
	}
}

// BenchmarkSplitToInt64 测试字符串分割转换性能
func BenchmarkSplitToInt64(b *testing.B) {
	ctx := context.Background()
	processors := "1,2,3,4,5,6,7,8,9,10"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		SplitToInt64(ctx, processors)
	}
}

// BenchmarkIntSlice2Str 测试整数切片转字符串性能
func BenchmarkIntSlice2Str(b *testing.B) {
	ids := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		IntSlice2Str(ids)
	}
}

// BenchmarkRemoveDuplicates 测试去重功能性能
func BenchmarkRemoveDuplicates(b *testing.B) {
	arr := []int64{1, 2, 3, 4, 5, 1, 2, 3, 4, 5, 1, 2, 3, 4, 5}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		RemoveDuplicates(arr, false)
	}
}
