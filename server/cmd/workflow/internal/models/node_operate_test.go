package models

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"uofferv2/kitex_gen/server/cmd/workflow"
)

// MockDB 模拟 gorm.DB
type MockDB struct {
	mock.Mock
}

func (m *MockDB) WithContext(ctx context.Context) *gorm.DB {
	args := m.Called(ctx)
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Model(value interface{}) *gorm.DB {
	args := m.Called(value)
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Clauses(conds ...clause.Expression) *gorm.DB {
	args := m.Called(conds)
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Where(query interface{}, args ...interface{}) *gorm.DB {
	allArgs := []interface{}{query}
	allArgs = append(allArgs, args...)
	mockArgs := m.Called(allArgs...)
	return mockArgs.Get(0).(*gorm.DB)
}

func (m *MockDB) Updates(values interface{}) *gorm.DB {
	args := m.Called(values)
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Scan(dest interface{}) *gorm.DB {
	args := m.Called(dest)
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Select(query interface{}, args ...interface{}) *gorm.DB {
	allArgs := []interface{}{query}
	allArgs = append(allArgs, args...)
	mockArgs := m.Called(allArgs...)
	return mockArgs.Get(0).(*gorm.DB)
}

func (m *MockDB) Joins(query string, args ...interface{}) *gorm.DB {
	allArgs := []interface{}{query}
	allArgs = append(allArgs, args...)
	mockArgs := m.Called(allArgs...)
	return mockArgs.Get(0).(*gorm.DB)
}

func (m *MockDB) Group(name string) *gorm.DB {
	args := m.Called(name)
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Order(value interface{}) *gorm.DB {
	args := m.Called(value)
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Table(name string, args ...interface{}) *gorm.DB {
	allArgs := []interface{}{name}
	allArgs = append(allArgs, args...)
	mockArgs := m.Called(allArgs...)
	return mockArgs.Get(0).(*gorm.DB)
}

func (m *MockDB) Find(dest interface{}, conds ...interface{}) *gorm.DB {
	allArgs := []interface{}{dest}
	allArgs = append(allArgs, conds...)
	mockArgs := m.Called(allArgs...)
	return mockArgs.Get(0).(*gorm.DB)
}

func (m *MockDB) First(dest interface{}, conds ...interface{}) *gorm.DB {
	allArgs := []interface{}{dest}
	allArgs = append(allArgs, conds...)
	mockArgs := m.Called(allArgs...)
	return mockArgs.Get(0).(*gorm.DB)
}

// Error 模拟GORM错误字段
var Error error

func createMockDao() *WorkflowNodeDao {
	return &WorkflowNodeDao{
		db: &gorm.DB{},
	}
}

// 模拟参数验证函数
func validateNodeId(nodeId int64) error {
	if nodeId <= 0 {
		return fmt.Errorf("invalid workflow_node_id: %d", nodeId)
	}
	return nil
}

func validateWorkflowAndNodeId(workflowId, nodeId int64) error {
	if workflowId <= 0 || nodeId <= 0 {
		return fmt.Errorf("invalid workflow_id: %d, node_id: %d", workflowId, nodeId)
	}
	return nil
}

func validateEmployeeInMap(employeeId int64, employeeMap map[int64]*workflow.EmployeeData, fieldName string) error {
	if _, ok := employeeMap[employeeId]; !ok {
		return fmt.Errorf("invalid %s: %d", fieldName, employeeId)
	}
	return nil
}

func validateWorkflowId(workflowId int64) error {
	if workflowId <= 0 {
		return fmt.Errorf("invalid workflow_id: %d", workflowId)
	}
	return nil
}

func validateWorkflowAndNodeIdForPause(workflowId, workflowNodeId int64) error {
	if workflowId <= 0 || workflowNodeId <= 0 {
		return fmt.Errorf("invalid workflow_id: %d, workflow_node_id: %d", workflowId, workflowNodeId)
	}
	return nil
}

func validateTerminateWorkflowParams(workflowId int64) error {
	if workflowId <= 0 {
		return fmt.Errorf("invalid workflow_node_id: %d", workflowId)
	}
	return nil
}

func validateTerminateNodeParams(nodeId int64) error {
	if nodeId <= 0 {
		return fmt.Errorf("invalid node_id: %d", nodeId)
	}
	return nil
}

// 测试 FinishWorkflowNodeV1 的参数验证逻辑
func TestFinishWorkflowNodeV1_ParameterValidation(t *testing.T) {
	tests := []struct {
		name        string
		nodeId      int64
		expectError bool
		errorMsg    string
	}{
		{
			name:        "invalid node id - zero",
			nodeId:      0,
			expectError: true,
			errorMsg:    "invalid workflow_node_id: 0",
		},
		{
			name:        "invalid node id - negative",
			nodeId:      -1,
			expectError: true,
			errorMsg:    "invalid workflow_node_id: -1",
		},
		{
			name:        "valid node id - positive",
			nodeId:      123,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateNodeId(tt.nodeId)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// 测试 ReceiveWorkflowNodeV1 的参数验证逻辑
func TestReceiveWorkflowNodeV1_ParameterValidation(t *testing.T) {
	tests := []struct {
		name        string
		nodeId      int64
		expectError bool
		errorMsg    string
	}{
		{
			name:        "invalid node id - zero",
			nodeId:      0,
			expectError: true,
			errorMsg:    "invalid workflow_node_id: 0",
		},
		{
			name:        "invalid node id - negative",
			nodeId:      -1,
			expectError: true,
			errorMsg:    "invalid workflow_node_id: -1",
		},
		{
			name:        "valid node id",
			nodeId:      123,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateNodeId(tt.nodeId)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// 测试 TransferWorkflowNodeV1 的参数验证逻辑
func TestTransferWorkflowNodeV1_ParameterValidation(t *testing.T) {
	employeeMap := map[int64]*workflow.EmployeeData{
		100: {
			Id:       100,
			DeptName: "IT部门",
			RoleName: "开发工程师",
		},
		200: {
			Id:       200,
			DeptName: "运营部门",
			RoleName: "运营专员",
		},
	}

	tests := []struct {
		name        string
		workflowId  int64
		nodeId      int64
		updateId    int64
		transferId  int64
		expectError bool
		errorMsg    string
	}{
		{
			name:        "invalid workflow id - zero",
			workflowId:  0,
			nodeId:      123,
			updateId:    100,
			transferId:  200,
			expectError: true,
			errorMsg:    "invalid workflow_id: 0, node_id: 123",
		},
		{
			name:        "invalid node id - zero",
			workflowId:  456,
			nodeId:      0,
			updateId:    100,
			transferId:  200,
			expectError: true,
			errorMsg:    "invalid workflow_id: 456, node_id: 0",
		},
		{
			name:        "invalid update id - not found",
			workflowId:  456,
			nodeId:      123,
			updateId:    999,
			transferId:  200,
			expectError: true,
			errorMsg:    "invalid update_id: 999",
		},
		{
			name:        "invalid transfer id - not found",
			workflowId:  456,
			nodeId:      123,
			updateId:    100,
			transferId:  999,
			expectError: true,
			errorMsg:    "invalid transfer_id: 999",
		},
		{
			name:        "valid parameters",
			workflowId:  456,
			nodeId:      123,
			updateId:    100,
			transferId:  200,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 验证工作流和节点ID
			err := validateWorkflowAndNodeId(tt.workflowId, tt.nodeId)
			if err != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				return
			}

			// 验证更新者ID
			err = validateEmployeeInMap(tt.updateId, employeeMap, "update_id")
			if err != nil && tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				return
			}

			// 验证转移者ID
			err = validateEmployeeInMap(tt.transferId, employeeMap, "transfer_id")
			if err != nil && tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				return
			}

			if !tt.expectError {
				assert.NoError(t, err)
			}
		})
	}
}

// 测试 JSON 序列化逻辑
func TestTransferWorkflowNodeV1_JSONSerialization(t *testing.T) {
	transferId := int64(200)
	processorIds := []int64{transferId}

	processorIdsJson, err := json.Marshal(processorIds)
	assert.NoError(t, err)
	assert.Equal(t, "[200]", string(processorIdsJson))

	// 测试反序列化
	var deserializedIds []int64
	err = json.Unmarshal(processorIdsJson, &deserializedIds)
	assert.NoError(t, err)
	assert.Equal(t, processorIds, deserializedIds)
}

// 测试 RestartWorkflowNodesV1 的参数验证逻辑
func TestRestartWorkflowNodesV1_ParameterValidation(t *testing.T) {
	tests := []struct {
		name           string
		workflowId     int64
		workflowNodeId int64
		expectError    bool
		errorMsg       string
	}{
		{
			name:           "invalid workflow id - zero",
			workflowId:     0,
			workflowNodeId: 123,
			expectError:    true,
			errorMsg:       "invalid workflow_id: 0",
		},
		{
			name:           "invalid workflow id - negative",
			workflowId:     -1,
			workflowNodeId: 123,
			expectError:    true,
			errorMsg:       "invalid workflow_id: -1",
		},
		{
			name:           "valid workflow id with specific node",
			workflowId:     456,
			workflowNodeId: 123,
			expectError:    false,
		},
		{
			name:           "valid workflow id without specific node",
			workflowId:     456,
			workflowNodeId: 0,
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateWorkflowId(tt.workflowId)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// 测试 SuspendWorkflowNodeV1 的参数验证逻辑
func TestSuspendWorkflowNodeV1_ParameterValidation(t *testing.T) {
	tests := []struct {
		name        string
		nodeId      int64
		pauseReason string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "invalid node id - zero",
			nodeId:      0,
			pauseReason: "系统维护",
			expectError: true,
			errorMsg:    "invalid workflow_node_id: 0",
		},
		{
			name:        "invalid node id - negative",
			nodeId:      -1,
			pauseReason: "系统维护",
			expectError: true,
			errorMsg:    "invalid workflow_node_id: -1",
		},
		{
			name:        "valid node id with reason",
			nodeId:      123,
			pauseReason: "系统维护",
			expectError: false,
		},
		{
			name:        "valid node id without reason",
			nodeId:      123,
			pauseReason: "",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateNodeId(tt.nodeId)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// 测试 PauseWorkflowNodeV1 的参数验证逻辑
func TestPauseWorkflowNodeV1_ParameterValidation(t *testing.T) {
	tests := []struct {
		name           string
		workflowId     int64
		workflowNodeId int64
		pauseReason    string
		expectError    bool
		errorMsg       string
	}{
		{
			name:           "invalid workflow id - zero",
			workflowId:     0,
			workflowNodeId: 123,
			pauseReason:    "系统维护",
			expectError:    true,
			errorMsg:       "invalid workflow_id: 0, workflow_node_id: 123",
		},
		{
			name:           "invalid workflow node id - zero",
			workflowId:     456,
			workflowNodeId: 0,
			pauseReason:    "系统维护",
			expectError:    true,
			errorMsg:       "invalid workflow_id: 456, workflow_node_id: 0",
		},
		{
			name:           "both ids negative",
			workflowId:     -1,
			workflowNodeId: -1,
			pauseReason:    "系统维护",
			expectError:    true,
			errorMsg:       "invalid workflow_id: -1, workflow_node_id: -1",
		},
		{
			name:           "valid parameters with reason",
			workflowId:     456,
			workflowNodeId: 123,
			pauseReason:    "系统维护",
			expectError:    false,
		},
		{
			name:           "valid parameters without reason",
			workflowId:     456,
			workflowNodeId: 123,
			pauseReason:    "",
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateWorkflowAndNodeIdForPause(tt.workflowId, tt.workflowNodeId)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// 测试 TerminateWorkflow 的参数验证逻辑
func TestTerminateWorkflow_ParameterValidation(t *testing.T) {
	tests := []struct {
		name        string
		workflowId  int64
		expectError bool
		errorMsg    string
	}{
		{
			name:        "invalid workflow id - zero",
			workflowId:  0,
			expectError: true,
			errorMsg:    "invalid workflow_node_id: 0",
		},
		{
			name:        "invalid workflow id - negative",
			workflowId:  -1,
			expectError: true,
			errorMsg:    "invalid workflow_node_id: -1",
		},
		{
			name:        "valid workflow id",
			workflowId:  456,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateTerminateWorkflowParams(tt.workflowId)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// 测试 TerminateWorkflowNode 的参数验证逻辑
func TestTerminateWorkflowNode_ParameterValidation(t *testing.T) {
	tests := []struct {
		name        string
		nodeId      int64
		expectError bool
		errorMsg    string
	}{
		{
			name:        "invalid node id - zero",
			nodeId:      0,
			expectError: true,
			errorMsg:    "invalid node_id: 0",
		},
		{
			name:        "invalid node id - negative",
			nodeId:      -1,
			expectError: true,
			errorMsg:    "invalid node_id: -1",
		},
		{
			name:        "valid node id",
			nodeId:      123,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateTerminateNodeParams(tt.nodeId)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// 测试中文名称处理逻辑
func TestTerminateWorkflowNode_ChineseNameProcessing(t *testing.T) {
	tests := []struct {
		name           string
		abnormalNameCn string
		shouldInclude  bool
	}{
		{
			name:           "non-empty chinese name",
			abnormalNameCn: "异常终止",
			shouldInclude:  true,
		},
		{
			name:           "empty chinese name",
			abnormalNameCn: "",
			shouldInclude:  false,
		},
		{
			name:           "whitespace only chinese name",
			abnormalNameCn: "   ",
			shouldInclude:  false,
		},
		{
			name:           "tab and newline chinese name",
			abnormalNameCn: "\t\n",
			shouldInclude:  false,
		},
		{
			name:           "chinese name with leading/trailing spaces",
			abnormalNameCn: "  异常终止  ",
			shouldInclude:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			trimmed := strings.TrimSpace(tt.abnormalNameCn)
			shouldInclude := trimmed != ""
			assert.Equal(t, tt.shouldInclude, shouldInclude, "Chinese name processing logic should match expected result")
		})
	}
}

// 测试参数验证边界条件
func TestParameterValidation_BoundaryConditions(t *testing.T) {
	t.Run("maximum int64 value", func(t *testing.T) {
		maxInt64 := int64(9223372036854775807)
		err := validateNodeId(maxInt64)
		assert.NoError(t, err)
	})

	t.Run("minimum valid positive value", func(t *testing.T) {
		err := validateNodeId(1)
		assert.NoError(t, err)
	})

	t.Run("minimum invalid value", func(t *testing.T) {
		err := validateNodeId(-9223372036854775808)
		assert.Error(t, err)
	})
}

// 基准测试
func BenchmarkParameterValidation_FinishWorkflowNodeV1(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		nodeId := int64(i + 1)
		_ = validateNodeId(nodeId)
	}
}

func BenchmarkParameterValidation_TransferWorkflowNodeV1(b *testing.B) {
	employeeMap := map[int64]*workflow.EmployeeData{
		100: {Id: 100, DeptName: "IT部门", RoleName: "开发工程师"},
		200: {Id: 200, DeptName: "运营部门", RoleName: "运营专员"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		workflowId := int64(i + 1)
		nodeId := int64(i + 1)

		_ = validateWorkflowAndNodeId(workflowId, nodeId)
		_ = validateEmployeeInMap(100, employeeMap, "update_id")
		_ = validateEmployeeInMap(200, employeeMap, "transfer_id")
	}
}

func BenchmarkParameterValidation_JSONSerialization(b *testing.B) {
	processorIds := []int64{100, 200, 300}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = json.Marshal(processorIds)
	}
}

func BenchmarkParameterValidation_ChineseNameProcessing(b *testing.B) {
	abnormalNameCn := "  异常终止  "

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = strings.TrimSpace(abnormalNameCn)
	}
}
