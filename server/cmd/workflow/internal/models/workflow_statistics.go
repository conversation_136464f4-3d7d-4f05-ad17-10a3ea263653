package models

import (
	"context"
	"time"

	"uofferv2/pkg/dao/model"
)

func GetWorkflowNodeStatByEmployeeInDateRange(ctx context.Context, employeeID int64, startDate, endDate time.Time) ([]*model.WorkflowNodeStatisticsDaily, error) {
	return Q.WorkflowNodeStatisticsDaily.WithContext(ctx).
		Where(Q.WorkflowNodeStatisticsDaily.EmployeeID.Eq(employeeID)).
		Where(Q.WorkflowNodeStatisticsDaily.StatisticalDate.Gte(startDate)).
		Where(Q.WorkflowNodeStatisticsDaily.StatisticalDate.Lte(endDate)).
		Find()
}

func GetWorkflowStatByEmployeeInDateRange(ctx context.Context, employeeID int64, startDate, endDate time.Time) ([]*model.WorkflowStatisticsDaily, error) {
	return Q.WorkflowStatisticsDaily.WithContext(ctx).
		Where(Q.WorkflowStatisticsDaily.EmployeeID.Eq(employeeID)).
		Where(Q.WorkflowStatisticsDaily.StatisticalDate.Gte(startDate)).
		Where(Q.WorkflowStatisticsDaily.StatisticalDate.Lte(endDate)).
		Find()
}

// CreateWorkflowStatisticsDaily creates a new workflow statistics daily record
func CreateWorkflowStatisticsDaily(ctx context.Context, stats *model.WorkflowStatisticsDaily) error {
	return Q.WorkflowStatisticsDaily.WithContext(ctx).Create(stats)
}

// GetWorkflowStatisticsDailyByID retrieves a workflow statistics daily record by ID
func GetWorkflowStatisticsDailyByID(ctx context.Context, id int64) (*model.WorkflowStatisticsDaily, error) {
	return Q.WorkflowStatisticsDaily.WithContext(ctx).Where(Q.WorkflowStatisticsDaily.ID.Eq(id)).First()
}

// GetWorkflowStatisticsDailyByEmployeeAndDate retrieves workflow statistics for an employee on a specific date
func GetWorkflowStatisticsDailyByEmployeeAndDate(ctx context.Context, employeeID int64, date time.Time) (*model.WorkflowStatisticsDaily, error) {
	return Q.WorkflowStatisticsDaily.WithContext(ctx).
		Where(Q.WorkflowStatisticsDaily.EmployeeID.Eq(employeeID)).
		Where(Q.WorkflowStatisticsDaily.StatisticalDate.Eq(date)).
		First()
}

// UpdateWorkflowStatisticsDaily updates a workflow statistics daily record
func UpdateWorkflowStatisticsDaily(ctx context.Context, id int64, updates map[string]interface{}) error {
	_, err := Q.WorkflowStatisticsDaily.WithContext(ctx).Where(Q.WorkflowStatisticsDaily.ID.Eq(id)).Updates(updates)
	return err
}

// DeleteWorkflowStatisticsDaily deletes a workflow statistics daily record by ID
func DeleteWorkflowStatisticsDaily(ctx context.Context, id int64) error {
	_, err := Q.WorkflowStatisticsDaily.WithContext(ctx).Where(Q.WorkflowStatisticsDaily.ID.Eq(id)).Delete()
	return err
}

// GetWorkflowStatisticsDailyByEmployeeInDateRange retrieves workflow statistics for an employee within a date range
func GetWorkflowStatisticsDailyByEmployeeInDateRange(ctx context.Context, employeeID int64, startDate, endDate time.Time) ([]*model.WorkflowStatisticsDaily, error) {
	return Q.WorkflowStatisticsDaily.WithContext(ctx).
		Where(Q.WorkflowStatisticsDaily.EmployeeID.Eq(employeeID)).
		Where(Q.WorkflowStatisticsDaily.StatisticalDate.Gte(startDate)).
		Where(Q.WorkflowStatisticsDaily.StatisticalDate.Lte(endDate)).
		Order(Q.WorkflowStatisticsDaily.StatisticalDate).
		Find()
}

// GetWorkflowStatisticsDailyByDateRange retrieves all workflow statistics within a date range
func GetWorkflowStatisticsDailyByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*model.WorkflowStatisticsDaily, error) {
	return Q.WorkflowStatisticsDaily.WithContext(ctx).
		Where(Q.WorkflowStatisticsDaily.StatisticalDate.Gte(startDate)).
		Where(Q.WorkflowStatisticsDaily.StatisticalDate.Lte(endDate)).
		Order(Q.WorkflowStatisticsDaily.StatisticalDate, Q.WorkflowStatisticsDaily.EmployeeID).
		Find()
}

// UpsertWorkflowStatisticsDaily creates or updates workflow statistics daily record
func UpsertWorkflowStatisticsDaily(ctx context.Context, stats *model.WorkflowStatisticsDaily) error {
	return Q.WorkflowStatisticsDaily.WithContext(ctx).Save(stats)
}

// BatchUpsertWorkflowStatisticsDaily creates or updates multiple workflow statistics daily records in batches
func BatchUpsertWorkflowStatisticsDaily(ctx context.Context, statsList []*model.WorkflowStatisticsDaily) error {
	if len(statsList) == 0 {
		return nil
	}
	return Q.WorkflowStatisticsDaily.WithContext(ctx).Save(statsList...)
}

// GetWorkflowStatisticsDailyByPage retrieves workflow statistics with pagination
func GetWorkflowStatisticsDailyByPage(ctx context.Context, offset, limit int) ([]*model.WorkflowStatisticsDaily, int64, error) {
	return Q.WorkflowStatisticsDaily.WithContext(ctx).
		Order(Q.WorkflowStatisticsDaily.ID.Desc()).
		FindByPage(offset, limit)
}

// BatchCreateWorkflowStatisticsDaily creates multiple workflow statistics daily records in batches
func BatchCreateWorkflowStatisticsDaily(ctx context.Context, statsList []*model.WorkflowStatisticsDaily, batchSize int) error {
	return Q.WorkflowStatisticsDaily.WithContext(ctx).CreateInBatches(statsList, batchSize)
}

// CreateWorkflowNodeStatisticsDaily creates a new workflow node statistics record
func CreateWorkflowNodeStatisticsDaily(ctx context.Context, stats *model.WorkflowNodeStatisticsDaily) error {
	return Q.WorkflowNodeStatisticsDaily.WithContext(ctx).Create(stats)
}

// GetWorkflowNodeStatisticsDailyByID retrieves a workflow node statistics record by ID
func GetWorkflowNodeStatisticsDailyByID(ctx context.Context, id int64) (*model.WorkflowNodeStatisticsDaily, error) {
	return Q.WorkflowNodeStatisticsDaily.WithContext(ctx).Where(Q.WorkflowNodeStatisticsDaily.ID.Eq(id)).First()
}

// GetWorkflowNodeStatisticsDailyByEmployeeAndDate retrieves workflow node statistics for an employee on a specific date
func GetWorkflowNodeStatisticsDailyByEmployeeAndDate(ctx context.Context, employeeID int64, date time.Time) (*model.WorkflowNodeStatisticsDaily, error) {
	return Q.WorkflowNodeStatisticsDaily.WithContext(ctx).
		Where(Q.WorkflowNodeStatisticsDaily.EmployeeID.Eq(employeeID)).
		Where(Q.WorkflowNodeStatisticsDaily.StatisticalDate.Eq(date)).
		First()
}

// UpdateWorkflowNodeStatisticsDaily updates a workflow node statistics record
func UpdateWorkflowNodeStatisticsDaily(ctx context.Context, id int64, updates map[string]interface{}) error {
	_, err := Q.WorkflowNodeStatisticsDaily.WithContext(ctx).Where(Q.WorkflowNodeStatisticsDaily.ID.Eq(id)).Updates(updates)
	return err
}

// DeleteWorkflowNodeStatisticsDaily deletes a workflow node statistics record by ID
func DeleteWorkflowNodeStatisticsDaily(ctx context.Context, id int64) error {
	_, err := Q.WorkflowNodeStatisticsDaily.WithContext(ctx).Where(Q.WorkflowNodeStatisticsDaily.ID.Eq(id)).Delete()
	return err
}

// GetWorkflowNodeStatisticsDailyByEmployeeInDateRange retrieves workflow node statistics for an employee within a date range
func GetWorkflowNodeStatisticsDailyByEmployeeInDateRange(ctx context.Context, employeeID int64, startDate, endDate time.Time) ([]*model.WorkflowNodeStatisticsDaily, error) {
	return Q.WorkflowNodeStatisticsDaily.WithContext(ctx).
		Where(Q.WorkflowNodeStatisticsDaily.EmployeeID.Eq(employeeID)).
		Where(Q.WorkflowNodeStatisticsDaily.StatisticalDate.Gte(startDate)).
		Where(Q.WorkflowNodeStatisticsDaily.StatisticalDate.Lte(endDate)).
		Order(Q.WorkflowNodeStatisticsDaily.StatisticalDate).
		Find()
}

// GetWorkflowNodeStatisticsDailyByDateRange retrieves all workflow node statistics within a date range
func GetWorkflowNodeStatisticsDailyByDateRange(ctx context.Context, startDate, endDate time.Time) ([]*model.WorkflowNodeStatisticsDaily, error) {
	return Q.WorkflowNodeStatisticsDaily.WithContext(ctx).
		Where(Q.WorkflowNodeStatisticsDaily.StatisticalDate.Gte(startDate)).
		Where(Q.WorkflowNodeStatisticsDaily.StatisticalDate.Lte(endDate)).
		Order(Q.WorkflowNodeStatisticsDaily.StatisticalDate, Q.WorkflowNodeStatisticsDaily.EmployeeID).
		Find()
}

// UpsertWorkflowNodeStatisticsDaily creates or updates workflow node statistics record
func UpsertWorkflowNodeStatisticsDaily(ctx context.Context, stats *model.WorkflowNodeStatisticsDaily) error {
	return Q.WorkflowNodeStatisticsDaily.WithContext(ctx).Save(stats)
}

// GetWorkflowNodeStatisticsDailyByPage retrieves workflow node statistics with pagination
func GetWorkflowNodeStatisticsDailyByPage(ctx context.Context, offset, limit int) ([]*model.WorkflowNodeStatisticsDaily, int64, error) {
	return Q.WorkflowNodeStatisticsDaily.WithContext(ctx).
		Order(Q.WorkflowNodeStatisticsDaily.ID.Desc()).
		FindByPage(offset, limit)
}

// BatchCreateWorkflowNodeStatisticsDaily creates multiple workflow node statistics records in batches
func BatchCreateWorkflowNodeStatisticsDaily(ctx context.Context, statsList []*model.WorkflowNodeStatisticsDaily, batchSize int) error {
	return Q.WorkflowNodeStatisticsDaily.WithContext(ctx).CreateInBatches(statsList, batchSize)
}
