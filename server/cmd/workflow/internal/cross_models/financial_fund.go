package cross_models

import (
	"context"

	"uofferv2/pkg/dao"
	"uofferv2/pkg/dao/model"
)

type FinancialFundDao struct {
	tx *dao.Query
}

func NewFinancialFundDao() *FinancialFundDao {
	return &FinancialFundDao{tx: Q}
}

func NewFinancialFundDaoWithTx(tx *dao.Query) *FinancialFundDao {
	return &FinancialFundDao{tx: tx}
}

// GetThirdFundWorkflowIds 跟进审批结果获取第三方申请费
func (d *FinancialFundDao) GetThirdFundByApproveStatus(ctx context.Context, approveStatus int32) ([]*model.FinancialFund, error) {
	table := d.tx.FinancialFund

	return table.WithContext(ctx).
		Where(table.ApproveStatus.Eq(approveStatus)).Where(table.FundType.Eq(4)).
		Find()
}
