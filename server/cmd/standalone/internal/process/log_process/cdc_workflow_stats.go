// Package log_process 提供工单统计相关的CDC处理逻辑
// 主要功能包括：
// 1. 工单统计数据的实时同步处理
// 2. 员工工单维度统计：被派单数、接收数、服务完成数、客户数量、平均处理时长
// 3. 员工任务维度统计：新增任务、处理中任务、完成任务、平均处理时长
// 4. 支持按天、周、月的环比统计功能
//
// 该包是工单统计功能的核心处理模块，基于现有的workflow服务架构进行扩展
// TODO: 后期迁移到stats_process包中

package log_process

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strconv"
	"sync"
	"time"

	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/standalone/internal/models"
	"uofferv2/server/cmd/standalone/internal/process/canal"

	"uofferv2/kitex_gen/server/cmd/workflow"
)

const (
	// MySQLDateTimeFormat MySQL数据库datetime字段的时间格式（带毫秒）
	MySQLDateTimeFormat = "2006-01-02 15:04:05.000"
	// MySQLDateTimeFormatNoMS MySQL数据库datetime字段的时间格式（不带毫秒）
	MySQLDateTimeFormatNoMS = "2006-01-02 15:04:05"
)

// parseDateTime 解析MySQL datetime格式的时间字符串，支持带毫秒和不带毫秒的格式
func parseDateTime(timeStr string) (time.Time, error) {
	// 先尝试带毫秒的格式
	if t, err := time.Parse(MySQLDateTimeFormat, timeStr); err == nil {
		return t, nil
	}
	// 再尝试不带毫秒的格式
	if t, err := time.Parse(MySQLDateTimeFormatNoMS, timeStr); err == nil {
		return t, nil
	}
	// 如果都失败，返回最后一次尝试的错误
	return time.Time{}, fmt.Errorf("无法解析时间格式: %s", timeStr)
}

func (c *WorkflowLog) doWorkflowStatisticsInsert(ctx context.Context, canalData *canal.CanalData) error {
	var err error

	var wfStatList []*model.WorkflowStatisticsDaily
	var statisticalDate time.Time

	for _, newData := range canalData.Data {
		var workflowID int64
		{
			workflowIDStr := newData[model.WorkflowColumns.ID()]
			if workflowIDStr == "" {
				logger.CtxErrorf(ctx, "更新工单日志，解析工单id为空")
				continue
			}
			workflowID, err = strconv.ParseInt(workflowIDStr, 10, 64)
			if err != nil {
				logger.CtxErrorf(ctx, "解析工单id失败 %v", err)
				continue
			}
		}
		var customerID int64
		{
			customerIDStr := newData[model.WorkflowColumns.CustomerID()]
			if customerIDStr == "" {
				logger.CtxErrorf(ctx, "更新工单日志，解析客户id为空")
				continue
			}
			customerID, err = strconv.ParseInt(customerIDStr, 10, 64)
			if err != nil {
				logger.CtxErrorf(ctx, "解析客户id失败 %v", err)
				continue
			}
		}
		var processorIDs []int64
		{
			processorIDsStr := newData[model.WorkflowColumns.Processors()]
			if processorIDsStr == "" {
				logger.CtxErrorf(ctx, "更新工单日志，解析处理器id为空")
				continue
			}
			err = json.Unmarshal([]byte(processorIDsStr), &processorIDs)
			if err != nil {
				logger.CtxErrorf(ctx, "解析处理器id失败 %v", err)
				continue
			}
		}
		var createdAtStr string
		{
			createdAtStr = newData[model.WorkflowColumns.CreatedAt()]
			if createdAtStr == "" {
				logger.CtxErrorf(ctx, "更新工单日志，解析创建时间为空")
				continue
			}
			createdAt, err := parseDateTime(createdAtStr)
			if err != nil {
				logger.CtxErrorf(ctx, "解析创建时间失败 %v", err)
				continue
			}
			statisticalDate = time.Date(createdAt.Year(), createdAt.Month(), createdAt.Day(), 0, 0, 0, 0, createdAt.Location())
		}

		for _, processorID := range processorIDs {
			// 查询或创建统计记录
			stat, err := models.GetWorkflowStatisticsDailyByEmployeeAndDate(ctx, processorID, statisticalDate)
			if err != nil {
				logger.CtxErrorf(ctx, "查询或创建统计记录失败 %v", err)
				continue
			}
			err = updateDispatchedStats(ctx, stat, workflowID, customerID)
			if err != nil {
				logger.CtxErrorf(ctx, "更新派单统计失败 %v", err)
				continue
			}
			wfStatList = append(wfStatList, stat)
		}
	}
	err = models.BatchUpsertWorkflowStatisticsDaily(ctx, wfStatList)
	if err != nil {
		logger.CtxErrorf(ctx, "保存工单统计记录失败 %v", err)
		return err
	}
	return nil
}

func (c *WorkflowLog) doWorkflowNodeStatisticsUpdate(ctx context.Context, canalData *canal.CanalData) error {
	var err error
	var workflowStats []*model.WorkflowStatisticsDaily
	var nodeStats []*model.WorkflowNodeStatisticsDaily
	for idx, newData := range canalData.Data {
		oldData := canalData.Old[idx]
		if len(oldData) == 0 {
			logger.CtxErrorf(ctx, "旧数据不存在")
			continue
		}

		var nodeType int64
		{
			nodeTypeStr := newData[model.WorkflowNodeColumns.Type()]
			if nodeTypeStr == "" {
				logger.CtxErrorf(ctx, "解析节点类型为空")
				continue
			}
			nodeType, err = strconv.ParseInt(nodeTypeStr, 10, 32)
			if err != nil {
				logger.CtxErrorf(ctx, "解析节点类型失败 %v", err)
				continue
			}
		}
		logger.CtxInfof(ctx, "节点类型: %d", nodeType)

		var newStatus int64
		{
			newStatusStr := newData[model.WorkflowNodeColumns.Status()]
			if newStatusStr == "" {
				logger.CtxErrorf(ctx, "解析新状态为空")
				continue
			}
			newStatus, err = strconv.ParseInt(newStatusStr, 10, 32)
			if err != nil {
				logger.CtxErrorf(ctx, "解析新状态失败 %v", err)
				continue
			}
		}
		logger.CtxInfof(ctx, "节点新状态: %d", newStatus)

		var oldStatus int64
		{
			oldStatusStr := oldData[model.WorkflowNodeColumns.Status()]
			if oldStatusStr == "" {
				logger.CtxErrorf(ctx, "解析旧状态为空")
				continue
			}
			oldStatus, err = strconv.ParseInt(oldStatusStr, 10, 32)
			if err != nil {
				logger.CtxErrorf(ctx, "解析旧状态失败 %v", err)
				continue
			}
		}
		logger.CtxInfof(ctx, "节点旧状态: %d", oldStatus)

		var workflowID int64
		{
			workflowIDStr := newData[model.WorkflowNodeColumns.WorkflowID()]
			if workflowIDStr == "" {
				logger.CtxErrorf(ctx, "解析工单id为空")
				continue
			}
			workflowID, err = strconv.ParseInt(workflowIDStr, 10, 64)
			if err != nil {
				logger.CtxErrorf(ctx, "解析工单id失败 %v", err)
				continue
			}
		}
		logger.CtxInfof(ctx, "工单id: %d", workflowID)

		var nodeID int64
		{
			nodeIDStr := newData[model.WorkflowNodeColumns.ID()]
			if nodeIDStr == "" {
				logger.CtxErrorf(ctx, "解析节点id为空")
				continue
			}
			nodeID, err = strconv.ParseInt(nodeIDStr, 10, 64)
			if err != nil {
				logger.CtxErrorf(ctx, "解析节点id失败 %v", err)
				continue
			}
		}
		logger.CtxInfof(ctx, "节点id: %d", nodeID)

		// var oldNodeStatus int64
		// {
		// 	oldNodeStatusStr := oldData[model.WorkflowNodeColumns.Status()]
		// 	if oldNodeStatusStr == "" {
		// 		logger.CtxErrorf(ctx, "解析旧节点状态为空")
		// 		continue
		// 	}
		// 	oldNodeStatus, err = strconv.ParseInt(oldNodeStatusStr, 10, 64)
		// 	if err != nil {
		// 		logger.CtxErrorf(ctx, "解析旧节点状态失败 %v", err)
		// 		continue
		// 	}
		// }

		var nodeBranch int64
		{
			nodeBranchStr := newData[model.WorkflowNodeColumns.Branch()]
			if nodeBranchStr == "" {
				logger.CtxErrorf(ctx, "解析节点分支为空")
				continue
			}
			nodeBranch, err = strconv.ParseInt(nodeBranchStr, 10, 64)
			if err != nil {
				logger.CtxErrorf(ctx, "解析节点分支失败 %v", err)
				continue
			}
		}
		logger.CtxInfof(ctx, "节点分支: %d", nodeBranch)

		var processedBy int64
		{
			processedByStr := newData[model.WorkflowNodeColumns.ProcessedBy()]
			if processedByStr == "" {
				logger.CtxErrorf(ctx, "解析处理人id为空")
				continue
			}
			processedBy, err = strconv.ParseInt(processedByStr, 10, 64)
			if err != nil {
				logger.CtxErrorf(ctx, "解析处理人id失败 %v", err)
				continue
			}
		}
		logger.CtxInfof(ctx, "处理人id: %d", processedBy)

		// var oldProcessedBy int64
		// {
		// 	oldProcessedByStr := oldData[model.WorkflowNodeColumns.ProcessedBy()]
		// 	if oldProcessedByStr == "" {
		// 		logger.CtxErrorf(ctx, "解析旧处理人id为空")
		// 		continue
		// 	}
		// 	oldProcessedBy, err = strconv.ParseInt(oldProcessedByStr, 10, 64)
		// 	if err != nil {
		// 		logger.CtxErrorf(ctx, "解析旧处理人id失败 %v", err)
		// 		continue
		// 	}
		// }

		// 工单审核+完成统计
		{
			// 开始节点从进行中变为已完成代表已接收状态
			if nodeType == int64(workflow.WorkflowNodeType_WORKFLOW_NODE_TYPE_START) &&
				oldStatus < int64(workflow.WorkflowNodeStatus_WORKFLOW_NODE_STATUS_COMPLETED) &&
				newStatus == int64(workflow.WorkflowNodeStatus_WORKFLOW_NODE_STATUS_COMPLETED) {
				wf, err := models.GetWorkflowInfoById(ctx, workflowID)
				if err != nil {
					logger.CtxErrorf(ctx, "获取工单信息失败 %v", err)
					continue
				}
				stat, err := c.AcceptWorkflowStatistics(ctx, processedBy, wf)
				if err != nil {
					logger.CtxErrorf(ctx, "接受工单统计失败 %v", err)
					continue
				}
				workflowStats = append(workflowStats, stat)
			}

			// 结束节点从进行中变为已完成表示工单完成
			if nodeType == int64(workflow.WorkflowNodeType_WORKFLOW_NODE_TYPE_END) &&
				oldStatus < int64(workflow.WorkflowNodeStatus_WORKFLOW_NODE_STATUS_COMPLETED) &&
				newStatus == int64(workflow.WorkflowNodeStatus_WORKFLOW_NODE_STATUS_COMPLETED) {
				wf, err := models.GetWorkflowInfoById(ctx, workflowID)
				if err != nil {
					logger.CtxErrorf(ctx, "获取工单信息失败 %v", err)
					continue
				}
				stat, err := c.CompleteWorkflowStatistics(ctx, processedBy, wf)
				if err != nil {
					logger.CtxErrorf(ctx, "完成工单统计失败 %v", err)
					continue
				}
				workflowStats = append(workflowStats, stat)
			}
		}

		// 工单节点统计(新增节点、接收、完成)
		{
			if nodeBranch != 0 {
				var nodeDispatchedAt time.Time
				{
					dispatchedAtStr := newData[model.WorkflowNodeColumns.DispatchedAt()]
					if dispatchedAtStr == "" {
						logger.CtxErrorf(ctx, "解析派单时间为空")
						continue
					}
					nodeDispatchedAt, err = parseDateTime(dispatchedAtStr)
					if err != nil {
						logger.CtxErrorf(ctx, "解析派单时间失败 %v", err)
						continue
					}
				}

				logger.CtxInfof(ctx, "派单时间: %s", nodeDispatchedAt)
				if newStatus < int64(workflow.WorkflowNodeStatus_WORKFLOW_NODE_STATUS_COMPLETED) {
					dStat, err := c.DispatchWorkflowNodeStatistics(ctx, processedBy, workflowID, nodeID, nodeDispatchedAt)
					if err != nil {
						logger.CtxErrorf(ctx, "派单节点统计失败 %v", err)
						continue
					}
					nodeStats = append(nodeStats, dStat)
				}
				var nodeAcceptedAt time.Time
				{
					acceptedAtStr := newData[model.WorkflowNodeColumns.StartedAt()]
					if acceptedAtStr == "" {
						logger.CtxErrorf(ctx, "解析接收时间为空")
						continue
					}
					nodeAcceptedAt, err = parseDateTime(acceptedAtStr)
					if err != nil {
						logger.CtxErrorf(ctx, "解析接收时间失败 %v", err)
						continue
					}
				}
				logger.CtxInfof(ctx, "接收时间: %s", nodeAcceptedAt)
				if newStatus > int64(workflow.WorkflowNodeStatus_WORKFLOW_NODE_STATUS_PENDING) &&
					newStatus < int64(workflow.WorkflowNodeStatus_WORKFLOW_NODE_STATUS_COMPLETED) {
					aStat, err := c.AcceptWorkflowNodeStatistics(ctx, processedBy, workflowID, nodeID, nodeAcceptedAt)
					if err != nil {
						logger.CtxErrorf(ctx, "接收节点统计失败 %v", err)
						continue
					}
					nodeStats = append(nodeStats, aStat)
				}
				var nodeCompletedAt time.Time
				{
					completedAtStr := newData[model.WorkflowNodeColumns.CompletedAt()]
					if completedAtStr == "" {
						logger.CtxErrorf(ctx, "解析完成时间为空")
						continue
					}
					nodeCompletedAt, err = parseDateTime(completedAtStr)
					if err != nil {
						logger.CtxErrorf(ctx, "解析完成时间失败 %v", err)
						continue
					}
				}
				logger.CtxInfof(ctx, "完成时间: %s", nodeCompletedAt)
				if newStatus == int64(workflow.WorkflowNodeStatus_WORKFLOW_NODE_STATUS_COMPLETED) {
					cStat, err := c.CompleteWorkflowNodeStatistics(ctx, processedBy, workflowID, nodeID, nodeAcceptedAt, nodeCompletedAt)
					if err != nil {
						logger.CtxErrorf(ctx, "完成节点统计失败 %v", err)
						continue
					}
					nodeStats = append(nodeStats, cStat)
				}
			}
		}
	}

	// 使用协程并发保存统计记录
	var wg sync.WaitGroup
	var workflowStatsErr, nodeStatsErr error

	// 保存工单统计记录
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 对工单统计数据根据employeeID和statisticalDate进行聚合
		aggregatedWorkflowStats := aggregateWorkflowStats(ctx, workflowStats)
		workflowStatsErr = models.BatchUpsertWorkflowStatisticsDaily(ctx, aggregatedWorkflowStats)
		if workflowStatsErr != nil {
			logger.CtxErrorf(ctx, "保存工单统计记录失败 %v", workflowStatsErr)
		}
	}()

	// 保存节点统计记录
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 对节点统计数据根据employeeID和statisticalDate进行聚合
		aggregatedNodeStats := aggregateNodeStats(ctx, nodeStats)
		nodeStatsErr = models.BatchUpsertWorkflowNodeStatisticsDaily(ctx, aggregatedNodeStats)
		if nodeStatsErr != nil {
			logger.CtxErrorf(ctx, "保存工单节点统计记录失败 %v", nodeStatsErr)
		}
	}()

	// 等待所有协程完成
	wg.Wait()

	// 检查是否有错误发生
	if workflowStatsErr != nil || nodeStatsErr != nil {
		if workflowStatsErr != nil && nodeStatsErr != nil {
			logger.CtxErrorf(ctx, "工单统计和节点统计都失败: %v; %v", workflowStatsErr, nodeStatsErr)
			return fmt.Errorf("工单统计和节点统计都失败: %v; %v", workflowStatsErr, nodeStatsErr)
		}
		if workflowStatsErr != nil {
			logger.CtxErrorf(ctx, "保存工单统计记录失败 %v", workflowStatsErr)
			return workflowStatsErr
		}
		logger.CtxErrorf(ctx, "保存工单节点统计记录失败 %v", nodeStatsErr)
		return nodeStatsErr
	}
	return nil
}

func (c *WorkflowLog) CompleteWorkflowStatistics(ctx context.Context, employeeID int64, wf *model.Workflow) (*model.WorkflowStatisticsDaily, error) {
	customerID := wf.CustomerID
	if wf.Status != int32(workflow.WorkflowStatus_WORKFLOW_STATUS_COMPLETED) {
		logger.CtxErrorf(ctx, "工单状态不是已完成")
		return nil, nil
	}
	completedAt := wf.CompletedAt.UnixMilli()
	receivedAt := wf.ReceivedAt.UnixMilli()
	processTime := completedAt - receivedAt
	statisticalDate := time.Date(time.UnixMilli(completedAt).Year(), time.UnixMilli(completedAt).Month(), time.UnixMilli(completedAt).Day(), 0, 0, 0, 0, time.UnixMilli(completedAt).Location())
	// 查询或创建统计记录
	stat, err := models.GetWorkflowStatisticsDailyByEmployeeAndDate(ctx, employeeID, statisticalDate)
	if err != nil {
		logger.CtxErrorf(ctx, "查询统计记录失败 %v", err)
		return nil, err
	}

	err = updateCompletedStats(ctx, stat, wf.ID, customerID, processTime)
	if err != nil {
		logger.CtxErrorf(ctx, "更新完成统计失败 %v", err)
		return nil, err
	}

	return stat, nil
}

func (c *WorkflowLog) AcceptWorkflowStatistics(ctx context.Context, employeeID int64, wf *model.Workflow) (*model.WorkflowStatisticsDaily, error) {
	customerID := wf.CustomerID
	if wf.Status != int32(workflow.WorkflowStatus_WORKFLOW_STATUS_IN_SERVICE) {
		logger.CtxErrorf(ctx, "工单: %d 状态不是进行中, 无法统计", wf.ID)
		return nil, nil
	}
	receiveAt := wf.ReceivedAt
	if receiveAt.IsZero() {
		logger.CtxErrorf(ctx, "工单: %d 接收时间为空, 无法统计", wf.ID)
		return nil, nil
	}

	statisticalDate := time.Date(receiveAt.Year(), receiveAt.Month(), receiveAt.Day(), 0, 0, 0, 0, receiveAt.Location())

	// 查询或创建统计记录
	stat, err := models.GetWorkflowStatisticsDailyByEmployeeAndDate(ctx, employeeID, statisticalDate)
	if err != nil {
		logger.CtxErrorf(ctx, "查询统计记录失败 %v", err)
		return nil, err
	}
	err = updateAcceptedStats(ctx, stat, wf.ID, customerID)
	if err != nil {
		logger.CtxErrorf(ctx, "更新接收统计失败 %v", err)
		return nil, err
	}

	return stat, nil
}

func (c *WorkflowLog) DispatchWorkflowNodeStatistics(ctx context.Context, employeeID int64, workflowID int64, nodeID int64, dispatchedAt time.Time) (*model.WorkflowNodeStatisticsDaily, error) {
	statisticalDate := time.Date(dispatchedAt.Year(), dispatchedAt.Month(), dispatchedAt.Day(), 0, 0, 0, 0, dispatchedAt.Location())
	stat, err := models.GetWorkflowNodeStatisticsDailyByEmployeeAndDate(ctx, employeeID, statisticalDate)
	if err != nil {
		logger.CtxErrorf(ctx, "查询统计记录失败 %v", err)
		return nil, err
	}
	err = updateNodeDispatchedStats(ctx, stat, nodeID)
	if err != nil {
		logger.CtxErrorf(ctx, "更新派单统计失败 %v", err)
		return nil, err
	}
	return stat, nil
}

func (c *WorkflowLog) AcceptWorkflowNodeStatistics(ctx context.Context, employeeID int64, workflowID int64, nodeID int64, acceptedAt time.Time) (*model.WorkflowNodeStatisticsDaily, error) {
	statisticalDate := time.Date(acceptedAt.Year(), acceptedAt.Month(), acceptedAt.Day(), 0, 0, 0, 0, acceptedAt.Location())
	stat, err := models.GetWorkflowNodeStatisticsDailyByEmployeeAndDate(ctx, employeeID, statisticalDate)
	if err != nil {
		logger.CtxErrorf(ctx, "查询统计记录失败 %v", err)
		return nil, err
	}
	err = updateNodeAcceptedStats(ctx, stat, nodeID)
	if err != nil {
		logger.CtxErrorf(ctx, "更新接收统计失败 %v", err)
		return nil, err
	}
	return stat, nil
}

func (c *WorkflowLog) CompleteWorkflowNodeStatistics(ctx context.Context, employeeID int64, workflowID int64, nodeID int64, acceptedAt, completedAt time.Time) (*model.WorkflowNodeStatisticsDaily, error) {
	statisticalDate := time.Date(completedAt.Year(), completedAt.Month(), completedAt.Day(), 0, 0, 0, 0, completedAt.Location())
	processTime := completedAt.UnixMilli() - acceptedAt.UnixMilli()
	stat, err := models.GetWorkflowNodeStatisticsDailyByEmployeeAndDate(ctx, employeeID, statisticalDate)
	if err != nil {
		logger.CtxErrorf(ctx, "查询统计记录失败 %v", err)
		return nil, err
	}
	err = updateNodeCompletedStats(ctx, stat, nodeID, processTime)
	if err != nil {
		logger.CtxErrorf(ctx, "更新完成统计失败 %v", err)
		return nil, err
	}
	return stat, nil
}

// updateDispatchedStats 更新派单统计
func updateDispatchedStats(ctx context.Context, stat *model.WorkflowStatisticsDaily, workflowID, customerID int64) error {
	// 更新派单工单列表
	var dispatchedWfIDs []int64
	if err := json.Unmarshal([]byte(stat.DispatchedWfIds), &dispatchedWfIDs); err != nil {
		logger.CtxErrorf(ctx, "failed to unmarshal dispatched wf ids: %v", err)
		return err
	}
	// 排序，去重
	slices.Sort(dispatchedWfIDs)
	dispatchedWfIDs = slices.Compact(dispatchedWfIDs)
	// 新工单需要统计
	if !slices.Contains(dispatchedWfIDs, workflowID) {
		dispatchedWfIDs = append(dispatchedWfIDs, workflowID)
		stat.DispatchedWfCount = int64(len(dispatchedWfIDs))
		dispatchedWfIdsBytes, _ := json.Marshal(dispatchedWfIDs)
		stat.DispatchedWfIds = string(dispatchedWfIdsBytes)
	}

	return updateCustomerStats(ctx, stat, customerID)
}

// updateCompletedStats 更新完成统计
func updateCompletedStats(ctx context.Context, stat *model.WorkflowStatisticsDaily, workflowID, customerID, processTime int64) error {
	// 更新完成工单列表
	var completedWfIDs []int64
	if err := json.Unmarshal([]byte(stat.CompletedWfIds), &completedWfIDs); err != nil {
		logger.CtxErrorf(ctx, "failed to unmarshal completed wf ids: %v", err)
		return err
	}
	// 排序，去重
	slices.Sort(completedWfIDs)
	completedWfIDs = slices.Compact(completedWfIDs)
	// 新工单需要统计
	if !slices.Contains(completedWfIDs, workflowID) {
		completedWfIDs = append(completedWfIDs, workflowID)
		stat.CompletedWfCount = int64(len(completedWfIDs))
		completedWfIdsBytes, _ := json.Marshal(completedWfIDs)
		stat.CompletedWfIds = string(completedWfIdsBytes)
		stat.TotalProcessTime += processTime
		logger.CtxErrorf(ctx, "工单:%d 完成，总处理时间:%d", workflowID, processTime)
	}

	return updateCustomerStats(ctx, stat, customerID)
}

// updateAcceptedStats 更新接收统计
func updateAcceptedStats(ctx context.Context, stat *model.WorkflowStatisticsDaily, workflowID, customerID int64) error {
	// 更新接收工单列表
	var acceptedWfIDs []int64
	if err := json.Unmarshal([]byte(stat.AcceptedWfIds), &acceptedWfIDs); err != nil {
		logger.CtxErrorf(ctx, "failed to unmarshal accepted wf ids: %v", err)
		return err
	}
	// 排序，去重
	slices.Sort(acceptedWfIDs)
	acceptedWfIDs = slices.Compact(acceptedWfIDs)
	// 新工单需要统计
	if !slices.Contains(acceptedWfIDs, workflowID) {
		acceptedWfIDs = append(acceptedWfIDs, workflowID)
		stat.AcceptedWfCount = int64(len(acceptedWfIDs))
		acceptedWfIdsBytes, _ := json.Marshal(acceptedWfIDs)
		stat.AcceptedWfIds = string(acceptedWfIdsBytes)
	}

	return updateCustomerStats(ctx, stat, customerID)
}

func updateCustomerStats(ctx context.Context, stat *model.WorkflowStatisticsDaily, customerID int64) error {
	// 更新客户列表
	var customerIDs []int64
	if err := json.Unmarshal([]byte(stat.CustomerIds), &customerIDs); err != nil {
		logger.CtxErrorf(ctx, "failed to unmarshal customer ids: %v", err)
		return err
	}
	// 排序，去重
	slices.Sort(customerIDs)
	customerIDs = slices.Compact(customerIDs)
	// 新客户需要统计
	if !slices.Contains(customerIDs, customerID) {
		customerIDs = append(customerIDs, customerID)
		stat.CustomerCount = int64(len(customerIDs))
		customerIdsBytes, _ := json.Marshal(customerIDs)
		stat.CustomerIds = string(customerIdsBytes)
	}

	return nil
}

func updateNodeDispatchedStats(ctx context.Context, stat *model.WorkflowNodeStatisticsDaily, nodeID int64) error {
	var dispatchedNodeIDs []int64
	if err := json.Unmarshal([]byte(stat.DispatchedNodeIds), &dispatchedNodeIDs); err != nil {
		logger.CtxErrorf(ctx, "failed to unmarshal dispatched node ids: %v", err)
		return err
	}
	slices.Sort(dispatchedNodeIDs)
	dispatchedNodeIDs = slices.Compact(dispatchedNodeIDs)
	if !slices.Contains(dispatchedNodeIDs, nodeID) {
		dispatchedNodeIDs = append(dispatchedNodeIDs, nodeID)
		dispatchedNodeIdsBytes, _ := json.Marshal(dispatchedNodeIDs)
		stat.DispatchedNodeIds = string(dispatchedNodeIdsBytes)
		stat.DispatchedNodeCount = int64(len(dispatchedNodeIDs))
		logger.CtxInfof(ctx, "节点:%d 派单，总派单数:%d", nodeID, stat.DispatchedNodeCount)
	}

	return nil
}

func updateNodeAcceptedStats(ctx context.Context, stat *model.WorkflowNodeStatisticsDaily, nodeID int64) error {
	var acceptedNodeIDs []int64
	if err := json.Unmarshal([]byte(stat.ProcessingNodeIds), &acceptedNodeIDs); err != nil {
		logger.CtxErrorf(ctx, "failed to unmarshal accepted node ids: %v", err)
		return err
	}
	slices.Sort(acceptedNodeIDs)
	acceptedNodeIDs = slices.Compact(acceptedNodeIDs)
	if !slices.Contains(acceptedNodeIDs, nodeID) {
		acceptedNodeIDs = append(acceptedNodeIDs, nodeID)
		acceptedNodeIdsBytes, _ := json.Marshal(acceptedNodeIDs)
		stat.ProcessingNodeIds = string(acceptedNodeIdsBytes)
		stat.ProcessingNodeCount = int64(len(acceptedNodeIDs))
		logger.CtxInfof(ctx, "节点:%d 接收，总接收数:%d", nodeID, stat.ProcessingNodeCount)
	}
	return nil
}

func updateNodeCompletedStats(ctx context.Context, stat *model.WorkflowNodeStatisticsDaily, nodeID, processTime int64) error {
	var completedNodeIDs []int64
	if err := json.Unmarshal([]byte(stat.CompletedNodeIds), &completedNodeIDs); err != nil {
		logger.CtxErrorf(ctx, "failed to unmarshal completed node ids: %v", err)
		return err
	}
	slices.Sort(completedNodeIDs)
	completedNodeIDs = slices.Compact(completedNodeIDs)
	if !slices.Contains(completedNodeIDs, nodeID) {
		completedNodeIDs = append(completedNodeIDs, nodeID)
		completedNodeIdsBytes, _ := json.Marshal(completedNodeIDs)
		stat.CompletedNodeIds = string(completedNodeIdsBytes)
		stat.CompletedNodeCount = int64(len(completedNodeIDs))
		stat.TotalProcessTime += processTime
		logger.CtxInfof(ctx, "节点:%d 完成，总处理时间:%d", nodeID, processTime)
	}
	logger.CtxInfof(ctx, "节点:%d 完成，总完成数:%d", nodeID, stat.CompletedNodeCount)
	return nil
}

// aggregateWorkflowStats 对工单统计数据根据employeeID和statisticalDate进行聚合
func aggregateWorkflowStats(ctx context.Context, workflowStats []*model.WorkflowStatisticsDaily) []*model.WorkflowStatisticsDaily {
	return aggregateWorkflowStatsImpl(ctx, workflowStats)
}

// aggregateNodeStats 对节点统计数据根据employeeID和statisticalDate进行聚合
func aggregateNodeStats(ctx context.Context, nodeStats []*model.WorkflowNodeStatisticsDaily) []*model.WorkflowNodeStatisticsDaily {
	return aggregateNodeStatsImpl(ctx, nodeStats)
}

// aggregateWorkflowStatsImpl 工单统计数据的具体聚合实现
func aggregateWorkflowStatsImpl(ctx context.Context, workflowStats []*model.WorkflowStatisticsDaily) []*model.WorkflowStatisticsDaily {
	aggregateMap := make(map[string]*model.WorkflowStatisticsDaily, len(workflowStats))

	for _, stat := range workflowStats {
		if stat == nil {
			continue
		}

		key := generateAggregateKey(stat.EmployeeID, stat.StatisticalDate)

		if existingStat, exists := aggregateMap[key]; exists {
			if err := mergeWorkflowStats(ctx, existingStat, stat); err != nil {
				logger.CtxErrorf(ctx, "聚合工单统计数据失败: %v", err)
				continue
			}
		} else {
			aggregateMap[key] = cloneWorkflowStat(stat)
		}
	}

	result := make([]*model.WorkflowStatisticsDaily, 0, len(aggregateMap))
	for _, stat := range aggregateMap {
		result = append(result, stat)
	}

	logger.CtxInfof(ctx, "工单统计数据聚合完成: 原始%d条, 聚合后%d条", len(workflowStats), len(result))
	return result
}

// aggregateNodeStatsImpl 节点统计数据的具体聚合实现
func aggregateNodeStatsImpl(ctx context.Context, nodeStats []*model.WorkflowNodeStatisticsDaily) []*model.WorkflowNodeStatisticsDaily {
	aggregateMap := make(map[string]*model.WorkflowNodeStatisticsDaily, len(nodeStats))

	for _, stat := range nodeStats {
		if stat == nil {
			continue
		}

		key := generateAggregateKey(stat.EmployeeID, stat.StatisticalDate)

		if existingStat, exists := aggregateMap[key]; exists {
			if err := mergeNodeStats(ctx, existingStat, stat); err != nil {
				logger.CtxErrorf(ctx, "聚合节点统计数据失败: %v", err)
				continue
			}
		} else {
			aggregateMap[key] = cloneNodeStat(stat)
		}
	}

	result := make([]*model.WorkflowNodeStatisticsDaily, 0, len(aggregateMap))
	for _, stat := range aggregateMap {
		result = append(result, stat)
	}

	logger.CtxInfof(ctx, "节点统计数据聚合完成: 原始%d条, 聚合后%d条", len(nodeStats), len(result))
	return result
}

// generateAggregateKey 生成聚合key
func generateAggregateKey(employeeID int64, statisticalDate time.Time) string {
	return fmt.Sprintf("%d_%s", employeeID, statisticalDate.Format("2006-01-02"))
}

// cloneWorkflowStat 克隆工单统计对象
func cloneWorkflowStat(stat *model.WorkflowStatisticsDaily) *model.WorkflowStatisticsDaily {
	now := time.Now()
	newStat := &model.WorkflowStatisticsDaily{
		EmployeeID:        stat.EmployeeID,
		StatisticalDate:   stat.StatisticalDate,
		CustomerCount:     stat.CustomerCount,
		DispatchedWfCount: stat.DispatchedWfCount,
		AcceptedWfCount:   stat.AcceptedWfCount,
		CompletedWfCount:  stat.CompletedWfCount,
		TotalProcessTime:  stat.TotalProcessTime,
		CustomerIds:       stat.CustomerIds,
		DispatchedWfIds:   stat.DispatchedWfIds,
		AcceptedWfIds:     stat.AcceptedWfIds,
		CompletedWfIds:    stat.CompletedWfIds,
		CreatedAt:         stat.CreatedAt,
		UpdatedAt:         now,
	}
	if stat.ID != 0 {
		newStat.ID = stat.ID
	}
	return newStat
}

// cloneNodeStat 克隆节点统计对象
func cloneNodeStat(stat *model.WorkflowNodeStatisticsDaily) *model.WorkflowNodeStatisticsDaily {
	now := time.Now()
	newStat := &model.WorkflowNodeStatisticsDaily{
		EmployeeID:          stat.EmployeeID,
		StatisticalDate:     stat.StatisticalDate,
		DispatchedNodeCount: stat.DispatchedNodeCount,
		ProcessingNodeCount: stat.ProcessingNodeCount,
		CompletedNodeCount:  stat.CompletedNodeCount,
		TotalProcessTime:    stat.TotalProcessTime,
		DispatchedNodeIds:   stat.DispatchedNodeIds,
		ProcessingNodeIds:   stat.ProcessingNodeIds,
		CompletedNodeIds:    stat.CompletedNodeIds,
		CreatedAt:           stat.CreatedAt,
		UpdatedAt:           now,
	}
	if stat.ID != 0 {
		newStat.ID = stat.ID
	}
	return newStat
}

// IDListMerger 用于处理ID列表的合并操作
type IDListMerger struct {
	ctx context.Context
}

// NewIDListMerger 创建ID列表合并器
func NewIDListMerger(ctx context.Context) *IDListMerger {
	return &IDListMerger{ctx: ctx}
}

// MergeAndUpdate 合并两个JSON ID列表并更新目标字段
func (m *IDListMerger) MergeAndUpdate(targetJSON, sourceJSON *string, targetCount *int64) error {
	targetIDs, err := m.parseIDList(*targetJSON)
	if err != nil {
		return fmt.Errorf("解析目标ID列表失败: %w", err)
	}

	sourceIDs, err := m.parseIDList(*sourceJSON)
	if err != nil {
		return fmt.Errorf("解析源ID列表失败: %w", err)
	}

	mergedIDs := mergeInt64Slices(targetIDs, sourceIDs)
	mergedJSON, err := json.Marshal(mergedIDs)
	if err != nil {
		return fmt.Errorf("序列化合并后ID列表失败: %w", err)
	}

	*targetJSON = string(mergedJSON)
	*targetCount = int64(len(mergedIDs))
	return nil
}

// parseIDList 解析JSON字符串为ID列表
func (m *IDListMerger) parseIDList(jsonStr string) ([]int64, error) {
	var ids []int64
	if jsonStr == "" || jsonStr == "null" {
		return ids, nil
	}

	if err := json.Unmarshal([]byte(jsonStr), &ids); err != nil {
		logger.CtxErrorf(m.ctx, "解析ID列表JSON失败: %v", err)
		return nil, err
	}
	return ids, nil
}

// mergeWorkflowStats 合并两个工单统计记录
func mergeWorkflowStats(ctx context.Context, target, source *model.WorkflowStatisticsDaily) error {
	merger := NewIDListMerger(ctx)

	// 合并客户ID列表
	if err := merger.MergeAndUpdate(&target.CustomerIds, &source.CustomerIds, &target.CustomerCount); err != nil {
		return fmt.Errorf("合并客户ID列表失败: %w", err)
	}

	// 合并派单工单ID列表
	if err := merger.MergeAndUpdate(&target.DispatchedWfIds, &source.DispatchedWfIds, &target.DispatchedWfCount); err != nil {
		return fmt.Errorf("合并派单工单ID列表失败: %w", err)
	}

	// 合并接收工单ID列表
	if err := merger.MergeAndUpdate(&target.AcceptedWfIds, &source.AcceptedWfIds, &target.AcceptedWfCount); err != nil {
		return fmt.Errorf("合并接收工单ID列表失败: %w", err)
	}

	// 合并完成工单ID列表
	if err := merger.MergeAndUpdate(&target.CompletedWfIds, &source.CompletedWfIds, &target.CompletedWfCount); err != nil {
		return fmt.Errorf("合并完成工单ID列表失败: %w", err)
	}

	// 累加总处理时长
	target.TotalProcessTime += source.TotalProcessTime
	target.UpdatedAt = time.Now()

	return nil
}

// mergeNodeStats 合并两个节点统计记录
func mergeNodeStats(ctx context.Context, target, source *model.WorkflowNodeStatisticsDaily) error {
	merger := NewIDListMerger(ctx)

	// 合并派单节点ID列表
	if err := merger.MergeAndUpdate(&target.DispatchedNodeIds, &source.DispatchedNodeIds, &target.DispatchedNodeCount); err != nil {
		return fmt.Errorf("合并派单节点ID列表失败: %w", err)
	}

	// 合并处理中节点ID列表
	if err := merger.MergeAndUpdate(&target.ProcessingNodeIds, &source.ProcessingNodeIds, &target.ProcessingNodeCount); err != nil {
		return fmt.Errorf("合并处理中节点ID列表失败: %w", err)
	}

	// 合并完成节点ID列表
	if err := merger.MergeAndUpdate(&target.CompletedNodeIds, &source.CompletedNodeIds, &target.CompletedNodeCount); err != nil {
		return fmt.Errorf("合并完成节点ID列表失败: %w", err)
	}

	// 累加总处理时长
	target.TotalProcessTime += source.TotalProcessTime
	target.UpdatedAt = time.Now()

	return nil
}

// mergeInt64Slices 合并两个int64切片，去重并排序
func mergeInt64Slices(slice1, slice2 []int64) []int64 {
	if len(slice1) == 0 {
		return removeDuplicatesAndSort(slice2)
	}
	if len(slice2) == 0 {
		return removeDuplicatesAndSort(slice1)
	}

	// 预分配足够的容量以减少重新分配
	result := make([]int64, 0, len(slice1)+len(slice2))
	result = append(result, slice1...)
	result = append(result, slice2...)

	return removeDuplicatesAndSort(result)
}

// removeDuplicatesAndSort 去重并排序
func removeDuplicatesAndSort(slice []int64) []int64 {
	if len(slice) <= 1 {
		return slice
	}

	slices.Sort(slice)
	return slices.Compact(slice)
}
