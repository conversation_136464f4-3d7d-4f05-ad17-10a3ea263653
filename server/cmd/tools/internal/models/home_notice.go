package models

import (
	"context"

	"uofferv2/pkg/dao"
	"uofferv2/pkg/dao/model"
)

type HomeNoticeDao struct {
	tx *dao.Query
}

func NewHomeNoticeDao() *HomeNoticeDao {
	return &HomeNoticeDao{tx: Q}
}

func NewHomeNoticeDaoWithTx(tx *dao.Query) *HomeNoticeDao {
	return &HomeNoticeDao{tx: tx}
}

func (d *HomeNoticeDao) QueryHomeNoticeByPage(ctx context.Context, noticeType int32, offset int, limit int) ([]*model.HomeNotice, int64, error) {
	table := d.tx.HomeNotice
	query := table.WithContext(ctx)
	if noticeType != 0 {
		query = query.Where(table.NoticeType.Eq(noticeType))
	}
	query = query.Order(table.UpdatedAt.Desc())

	return query.FindByPage(offset, limit)
}

func (d *HomeNoticeDao) GetHomeNoticeByID(ctx context.Context, id int64) (*model.HomeNotice, error) {
	table := d.tx.HomeNotice
	return table.WithContext(ctx).Where(table.ID.Eq(id)).First()
}

func (d *HomeNoticeDao) SaveHomeNotice(ctx context.Context, notice *model.HomeNotice) error {
	table := d.tx.HomeNotice
	return table.WithContext(ctx).Save(notice)
}

func (d *HomeNoticeDao) CreateHomeNotice(ctx context.Context, notice *model.HomeNotice) error {
	table := d.tx.HomeNotice
	return table.WithContext(ctx).Create(notice)
}

func (d *HomeNoticeDao) DeleteHomeNotice(ctx context.Context, id int64) error {
	table := d.tx.HomeNotice
	_, err := table.WithContext(ctx).Where(table.ID.Eq(id)).Delete()
	return err
}
