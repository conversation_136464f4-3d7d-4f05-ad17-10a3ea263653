package services

import (
	"context"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/tools"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/tools/internal/models"
)

type HomeNoticeService struct{}

func NewHomeNoticeService() *HomeNoticeService {
	return &HomeNoticeService{}
}

func (s *HomeNoticeService) ListHomeNotice(ctx context.Context, req *tools.ListHomeNoticeReq) (resp *tools.ListHomeNoticeRsp, err error) {
	resp = &tools.ListHomeNoticeRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	offset := (req.PageNum - 1) * req.PageSize
	records, total, err := models.NewHomeNoticeDao().QueryHomeNoticeByPage(ctx, int32(req.Type), int(offset), int(req.PageSize))
	if err != nil {
		logger.CtxErrorf(ctx, "query home notice list failed: %s", err.Error())
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	items := make([]*tools.HomeNotice, 0, len(records))
	for _, record := range records {
		items = append(items, &tools.HomeNotice{
			Id:        record.ID,
			Title:     record.Title,
			Content:   record.Content,
			Type:      tools.HomeNoticeType(record.NoticeType),
			UpdatedAt: record.UpdatedAt.Unix(),
			UpdatedBy: record.UpdatedBy,
		})
	}

	resp.Items = items
	resp.Total = total
	return
}

func (s *HomeNoticeService) SaveHomeNotice(ctx context.Context, req *tools.SaveHomeNoticeReq) (resp *tools.SaveHomeNoticeRsp, err error) {
	resp = &tools.SaveHomeNoticeRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	if req.Id == 0 {
		notice := &model.HomeNotice{
			Title:      req.Title,
			Content:    req.Content,
			NoticeType: int32(req.Type),
			UpdatedBy:  req.UpdatedBy,
			CreatedBy:  req.UpdatedBy,
		}
		err = models.NewHomeNoticeDao().CreateHomeNotice(ctx, notice)
		if err != nil {
			logger.CtxErrorf(ctx, "create home notice failed: %s", err.Error())
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}
		resp.Id = notice.ID
	} else {
		notice, err := models.NewHomeNoticeDao().GetHomeNoticeByID(ctx, req.Id)
		if err != nil {
			logger.CtxErrorf(ctx, "get home notice failed: %s", err.Error())
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}

		notice.Title = req.Title
		notice.Content = req.Content
		notice.UpdatedBy = req.UpdatedBy
		notice.NoticeType = int32(req.Type)

		err = models.NewHomeNoticeDao().SaveHomeNotice(ctx, notice)
		if err != nil {
			logger.CtxErrorf(ctx, "update home notice failed: %s", err.Error())
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}
		resp.Id = notice.ID
	}

	return
}

func (s *HomeNoticeService) DeleteHomeNotice(ctx context.Context, req *tools.DeleteHomeNoticeReq) (resp *tools.DeleteHomeNoticeRsp, err error) {
	resp = &tools.DeleteHomeNoticeRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	err = models.NewHomeNoticeDao().DeleteHomeNotice(ctx, req.Id)
	if err != nil {
		logger.CtxErrorf(ctx, "delete home notice failed: %s", err.Error())
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	return
}

func (s *HomeNoticeService) GetHomeNotice(ctx context.Context, req *tools.GetHomeNoticeReq) (resp *tools.GetHomeNoticeRsp, err error) {
	resp = &tools.GetHomeNoticeRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	notice, err := models.NewHomeNoticeDao().GetHomeNoticeByID(ctx, req.Id)
	if err != nil {
		logger.CtxErrorf(ctx, "get home notice failed: %s", err.Error())
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	resp.Notice = &tools.HomeNotice{
		Id:        notice.ID,
		Title:     notice.Title,
		Content:   notice.Content,
		Type:      tools.HomeNoticeType(notice.NoticeType),
		UpdatedAt: notice.UpdatedAt.UnixMilli(),
		UpdatedBy: notice.UpdatedBy,
	}

	return
}
