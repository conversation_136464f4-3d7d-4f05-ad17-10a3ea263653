package sms

import (
	"context"
	"crypto/sha1"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/tools/internal/config"
	"uofferv2/server/cmd/tools/internal/global"

	dysmsapi "github.com/alibabacloud-go/dysmsapi-20170525/v2/client"
	"github.com/alibabacloud-go/tea/tea"
)

// generateNonce 生成32位随机字符串
func generateNonce() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 32

	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// getCheckSum 计算CheckSum签名
func getCheckSum(appSecret, nonce, curTime string) string {
	// 构建签名字符串：AppSecret + Nonce + CurTime
	text := appSecret + nonce + curTime

	// 计算SHA1哈希值
	h := sha1.New()
	h.Write([]byte(text))

	// 转换为十六进制字符串
	return hex.EncodeToString(h.Sum(nil))
}

// buildHeaders 构建请求头
func buildHeaders(appKey, appSecret string) map[string]string {
	nonce := generateNonce()
	curTime := strconv.FormatInt(time.Now().Unix(), 10)
	checkSum := getCheckSum(appSecret, nonce, curTime)

	return map[string]string{
		"AppKey":       appKey,
		"Nonce":        nonce,
		"CurTime":      curTime,
		"CheckSum":     checkSum,
		"Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
	}
}

// YunxinSmsClient 网易云信短信客户端
type YunxinSmsClient struct {
	client *http.Client
}

// NewYunxinSmsClient 创建网易云信短信客户端
func NewYunxinSmsClient() *YunxinSmsClient {
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // 跳过SSL证书验证
		},
	}

	return &YunxinSmsClient{
		client: &http.Client{
			Transport: tr,
			Timeout:   10 * time.Second,
		},
	}
}

/*
{
    "code": xxx; // HTTP 状态码，返回值分为 200（代表成功）和其他（代表失败）。
    "msg": xxx; // `code` 返回的不是 200 时，会返回 `msg` 描述失败原因。
}
*/
// YunxinSmsResponse 网易云信响应结构
type YunxinSmsResponse struct {
	Code int    `json:"code"` // 状态码
	Msg  string `json:"msg"`  // sendID(requestID)
	Obj  string `json:"obj"`  // 发送的验证码
}

// sendRequest 发送HTTP请求
func (c *YunxinSmsClient) sendRequest(ctx context.Context, endpoint, apiPath string, data url.Values, appKey, appSecret string) (*YunxinSmsResponse, error) {
	requestURL := fmt.Sprintf("https://%s%s", endpoint, apiPath)

	req, err := http.NewRequestWithContext(ctx, "POST", requestURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	headers := buildHeaders(appKey, appSecret)
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析响应
	var yunxinResp YunxinSmsResponse
	if err := json.Unmarshal(body, &yunxinResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w, body: %s", err, string(body))
	}

	logger.CtxInfof(ctx, "网易云信短信响应: %+v", yunxinResp)
	return &yunxinResp, nil
}

// SendVerifyCode 发送验证码短信
func (c *YunxinSmsClient) SendVerifyCode(ctx context.Context, mobile, templateID, verifyCode, appKey, appSecret string) (*YunxinSmsResponse, error) {
	data := url.Values{}
	data.Set("mobile", mobile)
	data.Set("templateid", templateID)
	// 验证验证码格式：长度为 4 ～ 10 位，支持字母和数字
	if len(verifyCode) < 4 || len(verifyCode) > 10 {
		return nil, fmt.Errorf("验证码长度必须为4-10位")
	}

	// 检查验证码是否只包含字母和数字
	for _, char := range verifyCode {
		if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z')) {
			return nil, fmt.Errorf("验证码只能包含字母和数字")
		}
	}
	data.Set("authCode", verifyCode)
	// 尝试主域名，失败后尝试备用域名
	resp, err := c.sendRequest(ctx, global.ServerConfig.Sms.Endpoint, global.ServerConfig.Sms.VerifyCodeAPI, data, appKey, appSecret)
	if err != nil {
		logger.CtxErrorf(ctx, "主域名发送失败，尝试备用域名: %v", err)
		resp, err = c.sendRequest(ctx, global.ServerConfig.Sms.EndPointBackup, global.ServerConfig.Sms.VerifyCodeAPI, data, appKey, appSecret)
		if err != nil {
			logger.CtxErrorf(ctx, "备用域名发送失败: %v", err)
			return nil, err
		}
	}

	return resp, err
}

// YunxinSmsSender 网易云信短信发送器
type YunxinSmsSender struct {
	client *YunxinSmsClient
	config *config.ServerConfig
}

// NewYunxinSmsSender 创建网易云信短信发送器
func NewYunxinSmsSender(config *config.ServerConfig) *YunxinSmsSender {
	// 使用网易云信的配置字段
	client := NewYunxinSmsClient()
	return &YunxinSmsSender{
		client: client,
		config: config,
	}
}

// Send 实现SmsSender接口的Send方法
func (s *YunxinSmsSender) Send(ctx context.Context, phone string, params map[string]string, sign string, templateID string, appKey, appSecret string) (*dysmsapi.SendSmsResponse, error) {
	// 对于验证码短信，直接发送验证码
	if _, exists := params["code"]; exists {
		// 发送验证码短信
		resp, err := s.client.SendVerifyCode(ctx, phone, templateID, params["code"], appKey, appSecret)
		if err != nil {
			return nil, err
		}

		// 转换为标准响应格式
		if resp.Code == 200 {
			return &dysmsapi.SendSmsResponse{
				StatusCode: tea.Int32(200),
				Body: &dysmsapi.SendSmsResponseBody{
					Code:      tea.String("OK"),
					Message:   tea.String(fmt.Sprintf("发送成功, code: %d, sendid: %s, 验证码: %s", resp.Code, resp.Msg, resp.Obj)),
					RequestId: tea.String(resp.Msg),
				},
			}, nil
		} else {
			return &dysmsapi.SendSmsResponse{
				StatusCode: tea.Int32(int32(resp.Code)),
				Body: &dysmsapi.SendSmsResponseBody{
					Code:      tea.String("FAILED"),
					Message:   tea.String(fmt.Sprintf("发送失败, code: %d, 错误信息: %s, 错误详情: %s", resp.Code, resp.Msg, resp.Obj)),
					RequestId: tea.String(resp.Msg),
				},
			}, nil
		}
	}

	return nil, fmt.Errorf("不支持的短信类型")
}

// GetTemplateID 获取模板ID
func (s *YunxinSmsSender) GetTemplateID(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.GetDomesticId() != "" {
		return smsConfig.GetDomesticId()
	}
	return s.config.Sms.DomesticID
}

// GetSign 获取签名
func (s *YunxinSmsSender) GetSign(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.GetSignName() != "" {
		return smsConfig.GetSignName()
	}
	return s.config.Sms.Sign
}

// 构建符合网易云信短信规则的电话号码
func (s *YunxinSmsSender) GetPhone(ctx context.Context, countryCode, phone string) string {
	if countryCode != "86" {
		phone = fmt.Sprintf("+%s-%s", countryCode, phone)
	}
	logger.CtxInfof(ctx, "YunxinSmsSender Phone: %s", phone)
	return phone
}

func (s *YunxinSmsSender) GetAppKey(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.AccessKeyId != "" {
		return smsConfig.AccessKeyId
	}
	return s.config.Sms.AccessKeyID
}

func (s *YunxinSmsSender) GetAppSecret(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.AccessKeySecret != "" {
		return smsConfig.AccessKeySecret
	}
	return s.config.Sms.AccessKeySecret
}

// YunxinGlobalSmsSender 国际短信发送器
type YunxinGlobalSmsSender struct {
	client *YunxinSmsClient
	config *config.ServerConfig
}

// NewYunxinGlobalSmsSender 创建网易云信国际短信发送器
func NewYunxinGlobalSmsSender(config *config.ServerConfig) *YunxinGlobalSmsSender {
	// 使用网易云信的配置字段
	client := NewYunxinSmsClient()
	return &YunxinGlobalSmsSender{
		client: client,
		config: config,
	}
}

// Send 实现SmsSender接口的Send方法
func (s *YunxinGlobalSmsSender) Send(ctx context.Context, phone string, params map[string]string, sign string, templateID string, appKey, appSecret string) (*dysmsapi.SendSmsResponse, error) {
	// 对于验证码短信，直接发送验证码
	if _, exists := params["code"]; exists {
		// 发送验证码短信
		resp, err := s.client.SendVerifyCode(ctx, phone, templateID, params["code"], appKey, appSecret)
		if err != nil {
			return nil, err
		}

		// 转换为标准响应格式
		if resp.Code == 200 {
			return &dysmsapi.SendSmsResponse{
				StatusCode: tea.Int32(200),
				Body: &dysmsapi.SendSmsResponseBody{
					Code:      tea.String("OK"),
					Message:   tea.String(fmt.Sprintf("发送成功, code: %d, sendid: %s, 验证码: %s", resp.Code, resp.Msg, resp.Obj)),
					RequestId: tea.String(resp.Msg),
				},
			}, nil
		} else {
			return &dysmsapi.SendSmsResponse{
				StatusCode: tea.Int32(int32(resp.Code)),
				Body: &dysmsapi.SendSmsResponseBody{
					Code:      tea.String("FAILED"),
					Message:   tea.String(fmt.Sprintf("发送失败, code: %d, 错误信息: %s, 错误详情: %s", resp.Code, resp.Msg, resp.Obj)),
					RequestId: tea.String(resp.Msg),
				},
			}, nil
		}
	}

	return nil, fmt.Errorf("不支持的短信类型")
}

// GetTemplateID 获取模板ID
func (s *YunxinGlobalSmsSender) GetTemplateID(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.GetGlobalId() != "" {
		return smsConfig.GetGlobalId()
	}
	return s.config.Sms.GlobalID
}

// GetSign 获取签名
func (s *YunxinGlobalSmsSender) GetSign(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.GetSignName() != "" {
		return smsConfig.GetSignName()
	}
	return s.config.Sms.Sign
}

// 构建符合网易云信短信规则的电话号码
func (s *YunxinGlobalSmsSender) GetPhone(ctx context.Context, countryCode, phone string) string {
	if countryCode != "86" {
		phone = fmt.Sprintf("+%s-%s", countryCode, phone)
	}
	logger.CtxInfof(ctx, "YunxinSmsSender Phone: %s", phone)
	return phone
}

func (s *YunxinGlobalSmsSender) GetAppKey(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.AccessKeyId != "" {
		return smsConfig.AccessKeyId
	}
	return s.config.Sms.AccessKeyID
}

func (s *YunxinGlobalSmsSender) GetAppSecret(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.AccessKeySecret != "" {
		return smsConfig.AccessKeySecret
	}
	return s.config.Sms.AccessKeySecret
}
