package sms

import (
	"context"
	"encoding/json"
	"fmt"

	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/tools/internal/config"

	dysmsapi "github.com/alibabacloud-go/dysmsapi-20170525/v2/client"
	util "github.com/alibabacloud-go/tea-utils/service"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
)

// DomesticSmsSender 国内短信发送器
type DomesticSmsSender struct {
	client *dysmsapi.Client
	config *config.ServerConfig
}

func (s *DomesticSmsSender) Send(ctx context.Context, phone string, params map[string]string, sign string, templateCode string, appKey, appSecret string) (*dysmsapi.SendSmsResponse, error) {
	templateParam, _ := json.Marshal(params)
	req := &dysmsapi.SendSmsRequest{
		PhoneNumbers:  tea.String(phone),
		SignName:      tea.String(sign),
		TemplateCode:  tea.String(templateCode),
		TemplateParam: tea.String(string(templateParam)),
	}
	runtime := &util.RuntimeOptions{
		IgnoreSSL: tea.Bool(true), // 设置忽略SSL验证
	}
	return s.client.SendSmsWithOptions(req, runtime)
	// return s.client.SendSms(req)
}

func (s *DomesticSmsSender) GetTemplateID(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.DomesticId != "" {
		return smsConfig.DomesticId
	}
	return s.config.Sms.DomesticID
}

func (s *DomesticSmsSender) GetSign(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.SignName != "" {
		return smsConfig.SignName
	}
	return s.config.Sms.Sign
}

// 构建符合阿里云短信规则的电话号码
func (s *DomesticSmsSender) GetPhone(ctx context.Context, countryCode, phone string) string {
	logger.CtxInfof(ctx, "AliSmsSender Phone: %s", countryCode+phone)
	return countryCode + phone
}

func (s *DomesticSmsSender) GetAppKey(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.AccessKeyId != "" {
		return smsConfig.AccessKeyId
	}
	return s.config.Sms.AccessKeyID
}

func (s *DomesticSmsSender) GetAppSecret(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.AccessKeySecret != "" {
		return smsConfig.AccessKeySecret
	}
	return s.config.Sms.AccessKeySecret
}

// GlobalSmsSender 国际短信发送器
type GlobalSmsSender struct {
	client *dysmsapi.Client
	config *config.ServerConfig
}

func (s *GlobalSmsSender) Send(ctx context.Context, phone string, params map[string]string, sign string, templateCode string, appKey, appSecret string) (*dysmsapi.SendSmsResponse, error) {
	templateParam, _ := json.Marshal(params)
	req := &dysmsapi.SendSmsRequest{
		PhoneNumbers:  tea.String(phone),
		SignName:      tea.String(sign),
		TemplateCode:  tea.String(templateCode),
		TemplateParam: tea.String(string(templateParam)),
	}
	runtime := &util.RuntimeOptions{
		IgnoreSSL: tea.Bool(true), // 设置忽略SSL验证
	}
	return s.client.SendSmsWithOptions(req, runtime)
	// return s.client.SendSms(req)
}

func (s *GlobalSmsSender) GetTemplateID(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.GlobalId != "" {
		return smsConfig.GlobalId
	}
	return s.config.Sms.GlobalID
}

func (s *GlobalSmsSender) GetSign(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.SignName != "" {
		return smsConfig.SignName
	}
	return s.config.Sms.Sign
}

// 构建符合阿里云短信规则的电话号码
func (s *GlobalSmsSender) GetPhone(ctx context.Context, countryCode, phone string) string {
	logger.CtxInfof(ctx, "AliSmsSender Phone: %s", countryCode+phone)
	return countryCode + phone
}

func (s *GlobalSmsSender) GetAppKey(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.AccessKeyId != "" {
		return smsConfig.AccessKeyId
	}
	return s.config.Sms.AccessKeyID
}

func (s *GlobalSmsSender) GetAppSecret(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.AccessKeySecret != "" {
		return smsConfig.AccessKeySecret
	}
	return s.config.Sms.AccessKeySecret
}

// NoTemplateSmsSender 无模板短信发送器
type NoTemplateSmsSender struct {
	client *sdk.Client
	config *config.ServerConfig
}

func (s *NoTemplateSmsSender) Send(ctx context.Context, phone string, params map[string]string, sign string, templateCode string, appKey, appSecret string) (*dysmsapi.SendSmsResponse, error) {
	// 使用预设的内容格式
	content := fmt.Sprintf(s.config.Sms.NoTemplateContent,
		sign,
		params["code"],
		params["code"],
	)

	request := requests.NewCommonRequest()
	request.Method = "POST"
	request.Scheme = "https" // https | http
	request.Domain = "dysmsapi.aliyuncs.com"
	request.Version = "2017-05-25"
	request.ApiName = "SendMessageToGlobe"
	request.QueryParams["To"] = phone
	request.QueryParams["From"] = "18337384227"
	request.QueryParams["Message"] = content
	request.QueryParams["Type"] = "OTP"
	request.SetHTTPSInsecure(true)

	response, err := s.client.ProcessCommonRequest(request)
	if err != nil {
		return nil, err
	}
	res := &dysmsapi.SendSmsResponse{
		Body: &dysmsapi.SendSmsResponseBody{
			Code:    tea.String("OK"),
			Message: tea.String(response.GetHttpContentString()),
		},
	}
	return res, nil
}

func (s *NoTemplateSmsSender) GetTemplateID(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	return ""
}

func (s *NoTemplateSmsSender) GetSign(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.SignName != "" {
		return smsConfig.SignName
	}
	return s.config.Sms.Sign
}

// 构建符合阿里云短信规则的电话号码
func (s *NoTemplateSmsSender) GetPhone(ctx context.Context, countryCode, phone string) string {
	logger.CtxInfof(ctx, "AliSmsSender Phone: %s", countryCode+phone)
	return countryCode + phone
}

func (s *NoTemplateSmsSender) GetAppKey(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.AccessKeyId != "" {
		return smsConfig.AccessKeyId
	}
	return s.config.Sms.AccessKeyID
}

func (s *NoTemplateSmsSender) GetAppSecret(smsConfig *user.GetSmsConfigByBrandIdRsp) string {
	if smsConfig.AccessKeySecret != "" {
		return smsConfig.AccessKeySecret
	}
	return s.config.Sms.AccessKeySecret
}
