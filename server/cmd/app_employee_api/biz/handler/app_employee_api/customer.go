// Code generated by hertz generator.

package app_employee_api

import (
	"context"

	"uofferv2/server/cmd/app_employee_api/biz/model/server/cmd/app_employee_api"
	"uofferv2/server/cmd/app_employee_api/internal/services/customer_service"

	"github.com/cloudwego/hertz/pkg/app"
)

var _ = app_employee_api.GetBindCustomersReq{} // 防止没有引用编译错误

// GetBindCustomers .
// @router /customer/GetBindCustomers [POST]
func GetBindCustomers(ctx context.Context, c *app.RequestContext) {
	customer_service.GetBindCustomers(ctx, c)
}

// GetCustomerFollowEmployeeList .
// @router /customer/GetCustomerFollowEmployeeList [POST]
func GetCustomerFollowEmployeeList(ctx context.Context, c *app.RequestContext) {
	customer_service.GetCustomerFollowEmployeeList(ctx, c)
}

// AddCustomerSupportAgents .
// @router /customer/AddCustomerSupportAgents [POST]
func AddCustomerSupportAgents(ctx context.Context, c *app.RequestContext) {
	customer_service.AddCustomerSupportAgents(ctx, c)
}

// DeleteCustomerSupportAgents .
// @router /customer/DeleteCustomerSupportAgents [POST]
func DeleteCustomerSupportAgents(ctx context.Context, c *app.RequestContext) {
	customer_service.DeleteCustomerSupportAgents(ctx, c)
}
