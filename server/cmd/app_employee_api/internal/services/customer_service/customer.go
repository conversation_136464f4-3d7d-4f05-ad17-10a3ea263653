package customer_service

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/message"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/app_employee_api/biz/model/server/cmd/app_employee_api"
	"uofferv2/server/cmd/app_employee_api/internal/global"
	"uofferv2/server/cmd/app_employee_api/internal/httputil"
)

// GetBindCustomers .
// @router /customer/GetBindCustomers [POST]
func GetBindCustomers(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.GetBindCustomersReq
	err = c.BindAndValidate(&req)
	rpcSimpleCustomerResp, err := global.CustomerProfileClient.GetSimpleCustomerInfo(ctx, &customer.GetSimpleCustomerInfoReq{
		CustomerId: req.CustomerId,
	})
	errSimpleCustomerWrap := coderror.Warp(rpcSimpleCustomerResp, err)
	if errSimpleCustomerWrap != nil {
		logger.CtxErrorf(ctx, "GetSimpleCustomerInfo rpc error: %v", errSimpleCustomerWrap.Error())
		httputil.ResponseWithErrorWrap(c, errSimpleCustomerWrap)
		return
	}
	rpcResp, err := global.CustomerProfileClient.GetBindCustomers(ctx, &customer.GetBindCustomersReq{
		CustomerId: req.CustomerId,
		Identity:   int32(rpcSimpleCustomerResp.Customer.Identity),
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "GetBindCustomers rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	//获取rpcResp.Customers里面id的数组集合
	customerIds := make([]int64, 0, len(rpcResp.Customers))
	for _, cp := range rpcResp.Customers {
		customerIds = append(customerIds, int64(cp.Id))
	}
	identity := req.Identity
	if identity == 2 {
		identity = 3
	}
	rpcBatchGetAvatarResp, err := global.MessageClient.BatchGetAvatar(ctx, &message.BatchGetAvatarReq{
		UserId:   customerIds,
		UserType: identity,
	})
	errBatchGetAvatarWrap := coderror.Warp(rpcBatchGetAvatarResp, err)
	if errBatchGetAvatarWrap != nil {
		logger.CtxErrorf(ctx, "BatchGetAvatar rpc error: %v", errBatchGetAvatarWrap.Error())
		httputil.ResponseWithErrorWrap(c, errBatchGetAvatarWrap)
		return
	}
	httputil.ResponseSuccess(c, app_employee_api.GetBindCustomersRsp{
		Customers: ConvertBindCustomers2Api(rpcResp.Customers, rpcBatchGetAvatarResp.Items),
	})
}

// GetCustomerFollowEmployeeList .
// @router /customer/GetCustomerFollowEmployeeList [POST]
func GetCustomerFollowEmployeeList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req app_employee_api.GetCustomerFollowEmployeeListReq
	err = c.BindAndValidate(&req)

	rpcResp, err := global.CustomerProfileClient.GetCustomerFollowEmployeeIds(ctx, &customer.GetCustomerFollowEmployeeIdsReq{
		CustomerId: ctxmeta.MustGetAuth(ctx).Id,
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "GetCustomerFollowEmployeeIds rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// 没有跟进人
	if len(rpcResp.EmployeeIds) == 0 {
		httputil.ResponseSuccess(c, app_employee_api.GetCustomerFollowEmployeeListRsp{
			Employees: nil,
		})
		return
	}
	// 查员工详细信息
	respEmployeeInfo, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id:               rpcResp.EmployeeIds,
		EmploymentStatus: int32(1),
	})
	errEmployeeInfoWrap := coderror.Warp(respEmployeeInfo, err)
	if errEmployeeInfoWrap != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds rpc error: %v", errEmployeeInfoWrap.Error())
		httputil.ResponseWithErrorWrap(c, errEmployeeInfoWrap)
		return
	}
	if len(respEmployeeInfo.List) == 0 {
		httputil.ResponseSuccess(c, app_employee_api.GetCustomerFollowEmployeeListRsp{
			Employees: nil,
		})
		return
	}
	rpcBatchGetAvatarResp, err := global.MessageClient.BatchGetAvatar(ctx, &message.BatchGetAvatarReq{
		UserId:   rpcResp.EmployeeIds,
		UserType: 2,
	})
	errBatchGetAvatarWrap := coderror.Warp(rpcBatchGetAvatarResp, err)
	if errBatchGetAvatarWrap != nil {
		logger.CtxErrorf(ctx, "BatchGetAvatar rpc error: %v", errBatchGetAvatarWrap.Error())
		httputil.ResponseWithErrorWrap(c, errBatchGetAvatarWrap)
		return
	}
	avatarItems := rpcBatchGetAvatarResp.Items
	employees := make([]*app_employee_api.EmployeeItem, 0)
	for _, info := range respEmployeeInfo.List {
		avatar := ""
		if avatarItems[info.Id] != nil {
			avatar = avatarItems[info.Id].Avatar
		}

		employees = append(employees, &app_employee_api.EmployeeItem{
			RoleName: info.RoleName,
			RoleId:   int32(info.RoleId),
			Id:       int32(info.Id),
			Name:     info.Name,
			DeptId:   info.DeptId,
			DeptName: info.DeptName,
			Avatar:   avatar,
		})
	}
	httputil.ResponseSuccess(c, app_employee_api.GetCustomerFollowEmployeeListRsp{
		Employees: employees,
	})
}

// AddReferrerInformation .
// @router /customer/AddReferrerInformation [POST]
func AddCustomerSupportAgents(ctx context.Context, c *app.RequestContext) {
	var err error
	var req app_employee_api.AddCustomerSupportAgentsReq
	err = c.BindAndValidate(&req)
	rpcResp, err := global.CustomerProfileClient.AddCustomerFollowEmployee(ctx, &customer.AddCustomerFollowEmployeeReq{
		CustomerId:  int32(ctxmeta.MustGetAuth(ctx).Id),
		EmployeeIds: req.EmployeeIds,
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "AddCustomerFollowEmployee rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	httputil.ResponseSuccess(c, app_employee_api.AddCustomerSupportAgentsRsp{})
}

// DeleteCustomerSupportAgents .
// @router /customer/DeleteCustomerSupportAgents [POST]
func DeleteCustomerSupportAgents(ctx context.Context, c *app.RequestContext) {
	var err error
	var req app_employee_api.DeleteCustomerSupportAgentsReq
	err = c.BindAndValidate(&req)
	rpcResp, err := global.CustomerProfileClient.DeleteCustomerSupportAgents(ctx, &customer.DeleteCustomerSupportAgentsReq{
		CustomerId:  ctxmeta.MustGetAuth(ctx).Id,
		EmployeeIds: req.EmployeeIds,
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "DeleteCustomerSupportAgents rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	httputil.ResponseSuccess(c, app_employee_api.DeleteCustomerSupportAgentsRsp{})
}
