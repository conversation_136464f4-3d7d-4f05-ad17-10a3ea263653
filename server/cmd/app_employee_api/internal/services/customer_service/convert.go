package customer_service

import (
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/message"
	"uofferv2/server/cmd/app_employee_api/biz/model/server/cmd/app_employee_api"
)

func ConvertBindCustomers2Api(items []*customer.SimpleCustomerInfo, avatarItems map[int64]*message.AvatarItem) []*app_employee_api.GroupChatInfo {
	if items == nil {
		return nil
	}

	result := make([]*app_employee_api.GroupChatInfo, 0, len(items))
	for _, item := range items {
		avatar := ""
		if avatarItems[item.Id] != nil {
			avatar = avatarItems[item.Id].Avatar
		}
		imUserId := ""
		if avatarItems[item.Id] != nil {
			imUserId = avatarItems[item.Id].ImUserId
		}
		result = append(result, &app_employee_api.GroupChatInfo{
			Id:       int32(item.Id),
			Name:     item.Name,
			Avatar:   avatar,
			ImUserId: imUserId,
		})
	}
	return result
}
