# UOffer Global V2 项目文档

## 📚 文档导航

### 🏗️ 架构与设计
- [项目整体架构](./architecture/README.md)
- [数据库设计](./database/README.md)
- [API 设计规范](./api/README.md)

### 💰 财务模块
- [财务模块重构总结](./financial/refactor_summary.md)
- [财务模块实施指南](./financial/implementation_guide.md)
- **收款模块**
  - [API 优化方案](./financial/collection/api_optimization.md)
  - [数据模型优化](./financial/collection/model_optimization.md)
  - [服务层优化](./financial/collection/service_optimization.md)
- **支付模块**
  - [API 优化方案](./financial/payment/api_optimization.md)
  - [数据模型优化](./financial/payment/model_optimization.md)
  - [服务层优化](./financial/payment/service_optimization.md)

### 🧪 测试文档
- [集成测试指南](./testing/integration_test_guide.md)
- [单元测试规范](./testing/unit_test_guide.md)
- [性能测试手册](./testing/performance_test_guide.md)

### 🔧 开发工具
- [数据库 Lint 工具](./tools/dblint.md)
- [代码生成工具](./tools/codegen.md)
- [部署脚本使用](./tools/deployment.md)

### 📋 操作手册
- [环境配置指南](./operations/environment_setup.md)
- [服务部署手册](./operations/deployment_guide.md)
- [故障排除指南](./operations/troubleshooting.md)

### 📝 开发规范
- [代码风格指南](./standards/coding_style.md)
- [Git 提交规范](./standards/git_conventions.md)
- [文档编写规范](./standards/documentation_guide.md)

## 🚀 快速开始

如果您是新加入的开发者，建议按以下顺序阅读：

1. [项目整体架构](./architecture/README.md) - 了解项目结构
2. [环境配置指南](./operations/environment_setup.md) - 搭建开发环境
3. [开发规范](./standards/) - 了解团队规范
4. [集成测试指南](./testing/integration_test_guide.md) - 学习测试流程

## 📞 联系与反馈

如果您对文档有任何疑问或建议，请：

1. 提交 Issue 到项目仓库
2. 联系项目维护团队
3. 在团队会议中提出

---

**文档维护**：请确保在修改功能时同步更新相关文档。 