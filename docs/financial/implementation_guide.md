# 财务模块重构实施指南

本文档提供财务模块重构的具体实施方案和代码示例，帮助开发人员快速理解和执行重构任务。

## 🛠️ 工具函数应用示例

### 1. 字段映射工具使用

#### 当前代码 (需要优化)
```go
// FundUpdate 函数中的冗余字段更新逻辑
updateFields := make(map[string]interface{})
if req.OrderNo != "" {
    updateFields["order_no"] = req.OrderNo
}
if req.CustomerId != 0 {
    updateFields["customer_id"] = req.CustomerId
}
if req.Currency != "" {
    updateFields["currency"] = req.Currency
}
// ... 更多重复逻辑
```

#### 优化后代码
```go
// 使用字段映射工具
fieldMapping := map[string]string{
    "OrderNo":           "order_no",
    "CustomerId":        "customer_id", 
    "Currency":          "currency",
    "RealAmountOther":   "real_amount_other",
    "RealAmountRmb":     "real_amount_rmb",
    "ShouldAmountOther": "should_amount_other",
    "ShouldAmountRmb":   "should_amount_rmb",
    "ContractNo":        "contract_no",
    "Remark":            "remark",
}
excludeFields := []string{"FinancialFundId", "Status", "CreatedAt", "UpdatedAt"}
updateFields := utils.BuildUpdateFieldsWithExcludes(req, fieldMapping, excludeFields)
```

### 2. 查询构建器使用

#### 当前代码 (需要优化)
```go
// FundList 函数中的冗余查询条件
db := global.DB.WithContext(ctx).Model(&model.FinancialFund{})
if req.CustomerId != 0 {
    db = db.Where("customer_id = ?", req.CustomerId)
}
if req.FundNo != "" {
    db = db.Where("fund_no LIKE ?", "%"+req.FundNo+"%")
}
if len(req.FundTypeList) > 0 {
    db = db.Where("fund_type IN (?)", req.FundTypeList)
}
if req.CreatedAtStart != 0 && req.CreatedAtEnd != 0 {
    db = db.Where("created_at BETWEEN FROM_UNIXTIME(?) AND FROM_UNIXTIME(?)", 
        req.CreatedAtStart/1000, req.CreatedAtEnd/1000)
}
```

#### 优化后代码
```go
// 使用查询构建器
db := global.DB.WithContext(ctx).Model(&model.FinancialFund{})
qb := utils.NewQueryBuilder()
qb.Equal("customer_id", req.CustomerId).
   Like("fund_no", req.FundNo).
   In("fund_type", req.FundTypeList).
   TimeBetween("created_at", req.CreatedAtStart, req.CreatedAtEnd)
db = qb.Apply(db)
```

## 🔧 函数拆分示例

### FundCreate 函数拆分

#### 当前代码结构
```go
func FundCreate(ctx context.Context, req *order.FinancialFundCreateReq) (*order.FinancialFundCreateRsp, error) {
    // 1. 检查是否存在收款单 (30行代码)
    // 2. 存在则更新逻辑 (40行代码)  
    // 3. 不存在则创建逻辑 (50行代码)
    // 4. 创建支付记录 (20行代码)
    // 总计: 140行代码
}
```

#### 拆分后代码结构
```go
func FundCreate(ctx context.Context, req *order.FinancialFundCreateReq) (*order.FinancialFundCreateRsp, error) {
    existingFund, err := checkExistingFund(ctx, req)
    if err != nil {
        return nil, err
    }
    
    var fundId int64
    if existingFund != nil {
        fundId, err = handleExistingFund(ctx, existingFund, req)
    } else {
        fundId, err = createNewFund(ctx, req)
    }
    if err != nil {
        return nil, err
    }
    
    if err := createFinancialPaidRecords(ctx, fundId, req.FinancialPaidInfo); err != nil {
        return nil, err
    }
    
    return &order.FinancialFundCreateRsp{Id: fundId}, nil
}

func checkExistingFund(ctx context.Context, req *order.FinancialFundCreateReq) (*model.FinancialFund, error) {
    // 检查逻辑 (30行)
}

func handleExistingFund(ctx context.Context, fund *model.FinancialFund, req *order.FinancialFundCreateReq) (int64, error) {
    // 更新逻辑 (40行)
}

func createNewFund(ctx context.Context, req *order.FinancialFundCreateReq) (int64, error) {
    // 创建逻辑 (50行)
}
```

## 🔄 事务管理示例

### 事务边界优化

#### 当前代码 (无事务保护)
```go
func FundUpdate(ctx context.Context, req *order.FinancialFundUpdateReq) error {
    // 1. 更新主表
    err := models.FundUpdateFields(ctx, req.FinancialFundId, updateFields)
    if err != nil {
        return err
    }
    
    // 2. 删除旧支付记录
    if err := (&models.FinancialPaidDao{}).DeleteByFundID(ctx, req.FinancialFundId); err != nil {
        return err
    }
    
    // 3. 创建新支付记录
    if err := createFinancialPaidRecords(ctx, req.FinancialFundId, req.FinancialPaidInfo); err != nil {
        return err
    }
    
    return nil
}
```

#### 优化后代码 (事务保护)
```go
func FundUpdate(ctx context.Context, req *order.FinancialFundUpdateReq) error {
    return global.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        // 1. 更新主表
        if err := models.FundUpdateFieldsWithTx(ctx, tx, req.FinancialFundId, updateFields); err != nil {
            return err
        }
        
        // 2. 删除旧支付记录
        if err := (&models.FinancialPaidDao{}).DeleteByFundIDWithTx(ctx, tx, req.FinancialFundId); err != nil {
            return err
        }
        
        // 3. 创建新支付记录
        if err := createFinancialPaidRecordsWithTx(ctx, tx, req.FinancialFundId, req.FinancialPaidInfo); err != nil {
            return err
        }
        
        return nil
    })
}
```

## 📝 常量定义示例

### 枚举常量标准化

#### 当前代码 (魔法数字)
```go
// 收款类型判断
if fund.FundType == 1 {
    // 定金逻辑
} else if fund.FundType == 2 {
    // 首期款逻辑
} else if fund.FundType == 3 {
    // 尾款逻辑
}

// 审批状态判断
if req.Status == 2 {
    // 通过逻辑
} else if req.Status == 3 {
    // 驳回逻辑
}
```

#### 优化后代码 (使用常量)
```go
// 常量定义
const (
    // 收款类型
    FundTypeDeposit       = 1 // 定金类型
    FundTypeFirstPayment  = 2 // 首期款类型
    FundTypeFinalPayment  = 3 // 尾款类型
    FundTypeThirdPartyFee = 4 // 第三方费用类型
    
    // 审批状态
    ApproveStatusPending  = 1 // 待审批
    ApproveStatusApproved = 2 // 已通过
    ApproveStatusRejected = 3 // 已拒绝
    
    // 支款类型
    RefundTypeDeposit        = 1 // 退定金
    RefundTypeServiceFee     = 2 // 退服务费
    RefundTypeScholarship    = 3 // 奖学金
    RefundTypeDifference     = 4 // 退差价
    RefundTypePenalty        = 5 // 支付违约金
    RefundTypeThirdPartyFee  = 6 // 第三方申请费
)

// 使用常量
if fund.FundType == FundTypeDeposit {
    // 定金逻辑
} else if fund.FundType == FundTypeFirstPayment {
    // 首期款逻辑
} else if fund.FundType == FundTypeFinalPayment {
    // 尾款逻辑
}

if req.Status == ApproveStatusApproved {
    // 通过逻辑
} else if req.Status == ApproveStatusRejected {
    // 驳回逻辑
}
```

## 🧪 单元测试示例

### 核心函数测试

```go
func TestCalculateShouldAmountRmb(t *testing.T) {
    tests := []struct {
        name           string
        fundType       int32
        orderID        int64
        realAmountRmb  string
        expectedResult string
        expectError    bool
    }{
        {
            name:           "定金类型-正常计算",
            fundType:       FundTypeDeposit,
            orderID:        12345,
            realAmountRmb:  "1000.00000",
            expectedResult: "1000.00000",
            expectError:    false,
        },
        {
            name:           "首期款类型-需要查询订单支付信息",
            fundType:       FundTypeFirstPayment,
            orderID:        12345,
            realAmountRmb:  "5000.00000",
            expectedResult: "5000.00000", // 根据业务逻辑计算
            expectError:    false,
        },
        // 更多测试用例...
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := calculateShouldAmountRmb(context.Background(), tt.fundType, tt.orderID, tt.realAmountRmb)
            
            if tt.expectError {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tt.expectedResult, result)
            }
        })
    }
}
```

## 📋 实施检查清单

### 阶段一: 工具函数应用
- [ ] 识别所有字段更新函数
- [ ] 应用 `BuildUpdateFieldsWithExcludes` 工具
- [ ] 识别所有复杂查询函数
- [ ] 应用 `NewQueryBuilder` 工具
- [ ] 测试工具函数应用效果

### 阶段二: 函数拆分
- [ ] 识别超过100行的大函数
- [ ] 按职责拆分函数
- [ ] 提取公共逻辑
- [ ] 更新函数调用关系
- [ ] 补充函数文档

### 阶段三: 事务和错误处理
- [ ] 识别需要事务保护的业务流程
- [ ] 添加事务边界
- [ ] 统一错误处理逻辑
- [ ] 补充业务错误码
- [ ] 增强日志记录

### 阶段四: 测试和验证
- [ ] 编写单元测试
- [ ] 执行集成测试
- [ ] 性能基准测试
- [ ] 代码审查
- [ ] 文档更新

---

## 🎯 质量标准

### 代码质量指标
- 单个函数不超过50行
- 圈复杂度不超过10
- 代码重复率低于5%
- 单元测试覆盖率80%+

### 性能指标
- 查询响应时间不超过100ms
- 事务执行时间不超过500ms
- 内存使用优化20%+

遵循本指南实施重构，可以确保代码质量和系统稳定性的双重提升。
