# 财务支款 API 层优化待办事项

基于 `server/cmd/admin_api/biz/handler/admin_api/order.go` 和支款相关接口分析，以下为具体可执行的优化建议：

## 1. 具体接口参数校验优化

### RefundList 接口优化
- [ ] **RefundList 参数校验增强**
    - 描述：`/financial/RefundList` 接口缺少分页参数校验，`page_num` 和 `page_size` 可能为负数或超大值
    - 行动：添加参数校验 `page_num >= 1`, `page_size` 在 1-1000 范围内，防止恶意请求
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:RefundList`

### RefundDetail 接口优化  
- [ ] **RefundDetail ID校验**
    - 描述：`/financial/RefundDetail` 接口未校验 `id` 参数是否为正整数
    - 行动：添加 `id > 0` 校验，避免无效查询
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:RefundDetail`

### RefundApprove 接口优化
- [ ] **RefundApprove 状态校验**
    - 描述：`/financial/RefundApprove` 接口未校验 `approve_status` 参数是否在有效范围内
    - 行动：添加状态枚举校验（1=待审批，2=待支款，3=支款完成，4=审核驳回），防止无效状态值
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:RefundApprove`

## 2. 支款特有参数校验

### 金额校验优化
- [ ] **RefundCreate 金额合法性校验**
    - 描述：创建支款单时未校验 `real_amount_other` 和 `real_amount_rmb` 是否为正数
    - 行动：添加金额 > 0 校验，确保不能创建负数或零金额的支款单
    - 位置：`server/cmd/admin_api/internal/services/finanacial_service/refund_service.go`

### 账户信息校验
- [ ] **RefundCreate 收款账户校验**
    - 描述：`refund_receive_account_type` 和 `refund_receive_account` 参数未做格式校验
    - 行动：校验账户类型枚举值（1=支付宝，2=微信，3=银行卡），账户号码格式合法性
    - 位置：支款创建相关接口

## 3. 错误码标准化

### 统一错误返回格式
- [ ] **RefundList 错误码转换**
    - 描述：当前直接返回 Service 层错误，前端难以处理
    - 行动：添加错误码映射，如数据库错误→`REFUND_QUERY_FAILED`，参数错误→`INVALID_PARAMS`
    - 位置：`server/cmd/admin_api/internal/services/finanacial_service/refund_service.go:RefundList`

- [ ] **RefundApprove 业务错误细化**
    - 描述：审批失败时返回通用错误，无法区分具体原因
    - 行动：细化错误码：`REFUND_NOT_FOUND`、`REFUND_STATUS_INVALID`、`INSUFFICIENT_BALANCE`、`APPROVE_PERMISSION_DENIED`
    - 位置：`server/cmd/admin_api/internal/services/finanacial_service/refund_service.go:RefundApprove`

## 4. 幂等性与权限校验

### 支款创建幂等性
- [ ] **RefundCreate 幂等性保障**
    - 描述：支款单创建接口缺少幂等ID，可能重复创建
    - 行动：添加 `idempotent_key` 参数，API层校验并传递给Service层
    - 位置：支款创建相关接口

### 权限校验前置
- [ ] **RefundApprove 权限校验**
    - 描述：支款审批接口未在API层校验操作人权限
    - 行动：添加角色权限校验，确保只有财务人员可审批支款
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:RefundApprove`

- [ ] **RefundStatusUpdate 状态变更权限**
    - 描述：支款状态更新未校验操作权限
    - 行动：添加权限校验，不同状态变更需要不同级别权限
    - 位置：支款状态更新相关接口

## 5. 日志与审计

### 关键操作日志
- [ ] **RefundApprove 操作日志**
    - 描述：支款审批操作缺少API层日志记录
    - 行动：记录审批人、支款单ID、审批结果、金额、时间戳，便于审计
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:RefundApprove`

- [ ] **RefundCreate 创建日志**
    - 描述：支款单创建无操作日志
    - 行动：记录创建人、支款金额、客户信息、支款原因等关键数据
    - 位置：支款创建相关接口

- [ ] **ThirdRefundCreate 第三方支款日志**
    - 描述：第三方申请费支款创建缺少详细日志
    - 行动：记录工单信息、支款金额、操作人等关键数据
    - 位置：第三方支款创建接口

## 6. 具体接口优化

### ScholarshipRefundCreate 接口
- [ ] **奖学金支款参数校验**
    - 描述：奖学金支款接口缺少特有参数校验
    - 行动：校验 `scholarship_agreement`、`visa`、`student_card` 等文件上传参数
    - 位置：奖学金支款创建接口

### GetThirdRefund 接口  
- [ ] **第三方支款查询优化**
    - 描述：第三方支款查询接口缺少工单状态校验
    - 行动：添加 `workflow_id` 有效性校验，防止查询不存在的工单
    - 位置：第三方支款查询接口

### RefundAssociateList 接口
- [ ] **关联支款查询参数校验**
    - 描述：关联支款查询缺少订单ID校验
    - 行动：校验 `order_id` 参数有效性，确保查询的订单存在
    - 位置：关联支款查询接口

## 7. 响应格式标准化

### 统一分页响应
- [ ] **RefundList 分页信息补全**
    - 描述：列表接口分页信息不完整，缺少 `page_num`、`page_size` 返回
    - 行动：响应中补充当前页码、每页条数、总页数等分页元信息
    - 位置：`server/cmd/admin_api/internal/services/finanacial_service/refund_service.go:RefundList`

### 金额字段统一
- [ ] **金额字段格式标准化**
    - 描述：支款金额字段格式不一致，有些返回字符串，有些返回数字
    - 行动：统一使用字符串格式返回金额，保持精度一致
    - 位置：所有支款相关接口响应处理

## 8. 业务逻辑校验

### 支款期限校验
- [ ] **RefundDeadline 期限校验**
    - 描述：支款截止时间参数未校验是否为未来时间
    - 行动：校验 `refund_deadline` 必须大于当前时间，防止设置过期时间
    - 位置：支款创建和更新接口

### 支款类型校验
- [ ] **RefundType 类型枚举校验**
    - 描述：支款类型参数未严格校验枚举值
    - 行动：校验 `refund_type` 必须在 1-6 范围内（退定金、退服务费、奖学金等）
    - 位置：所有涉及支款类型的接口

## 9. 单元测试

### 接口测试覆盖
- [ ] **RefundList 接口测试**
    - 描述：缺少边界条件测试（如空结果、大分页等）
    - 行动：补充正常流程、异常参数、权限校验等测试用例
    - 位置：新建 `server/cmd/admin_api/biz/handler/admin_api/refund_test.go`

- [ ] **RefundApprove 接口测试**
    - 描述：缺少支款审批流程的完整测试
    - 行动：测试不同状态支款单的审批、权限校验、错误处理等场景
    - 位置：同上测试文件

- [ ] **支款金额校验测试**
    - 描述：缺少金额相关的边界测试
    - 行动：测试负数金额、超大金额、精度超限等异常情况
    - 位置：同上测试文件

---

以上优化建议均可直接分配给开发人员执行，每项都有明确的文件位置和具体行动。 