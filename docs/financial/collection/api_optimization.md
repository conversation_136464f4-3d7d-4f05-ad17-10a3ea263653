# 财务收款 API 层优化待办事项

基于 `server/cmd/admin_api/biz/handler/admin_api/order.go` 和相关接口分析，以下为具体可执行的优化建议：

## 1. 具体接口参数校验优化

### FundList 接口优化
- [ ] **FundList 参数校验增强**
    - 描述：`/financial/FundList` 接口缺少分页参数校验，`page_num` 和 `page_size` 可能为负数或超大值
    - 行动：添加参数校验 `page_num >= 1`, `page_size` 在 1-1000 范围内，防止恶意请求
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:FundList`

### FundDetail 接口优化  
- [ ] **FundDetail ID校验**
    - 描述：`/financial/FundDetail` 接口未校验 `id` 参数是否为正整数
    - 行动：添加 `id > 0` 校验，避免无效查询
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:FundDetail`

### FundApprove 接口优化
- [ ] **FundApprove 状态校验**
    - 描述：`/financial/FundApprove` 接口未校验 `status` 参数是否在有效范围内
    - 行动：添加 `status` 枚举校验（2=通过，3=驳回），防止无效状态值
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:FundApprove`

## 2. 错误码标准化

### 统一错误返回格式
- [ ] **FundList 错误码转换**
    - 描述：当前直接返回 Service 层错误，前端难以处理
    - 行动：添加错误码映射，如数据库错误→`FUND_QUERY_FAILED`，参数错误→`INVALID_PARAMS`
    - 位置：`server/cmd/admin_api/internal/services/finanacial_service/fund_service.go:FundList`

- [ ] **FundApprove 业务错误细化**
    - 描述：审批失败时返回通用错误，无法区分具体原因
    - 行动：细化错误码：`FUND_NOT_FOUND`、`FUND_STATUS_INVALID`、`APPROVE_PERMISSION_DENIED`
    - 位置：`server/cmd/admin_api/internal/services/finanacial_service/fund_service.go:FundApprove`

## 3. 幂等性与权限校验

### ThirdFundCreate 幂等性
- [ ] **第三方申请费创建幂等性**
    - 描述：`/financial/ThirdFundCreate` 接口缺少幂等ID，可能重复创建
    - 行动：添加 `idempotent_key` 参数，API层校验并传递给Service层
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:ThirdFundCreate`

### 权限校验前置
- [ ] **FundApprove 权限校验**
    - 描述：审批接口未在API层校验操作人权限
    - 行动：添加角色权限校验，确保只有财务人员可审批
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:FundApprove`

- [ ] **EditApprovedFinancialFund 权限校验**
    - 描述：编辑已审核收款单未校验操作权限
    - 行动：添加高级权限校验，限制编辑已审核单据的人员范围
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:EditApprovedFinancialFund`

## 4. 日志与审计

### 关键操作日志
- [ ] **FundApprove 操作日志**
    - 描述：审批操作缺少API层日志记录
    - 行动：记录审批人、收款单ID、审批结果、时间戳，便于审计
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:FundApprove`

- [ ] **ThirdFundCreate 创建日志**
    - 描述：第三方申请费创建无操作日志
    - 行动：记录创建人、金额、工单信息等关键数据
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:ThirdFundCreate`

## 5. 具体接口优化

### GetRelationExchangeRate 接口
- [ ] **汇率查询参数校验**
    - 描述：`/financial/GetRelationExchangeRate` 接口参数校验不全
    - 行动：校验 `currency` 格式，`order_id` 或 `workflow_id` 至少提供一个
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:GetRelationExchangeRate`

### GetWorkflowFund 接口  
- [ ] **工单收款查询优化**
    - 描述：`/financial/GetWorkflowFund` 接口缺少工单状态校验
    - 行动：添加 `workflow_id` 有效性校验，防止查询不存在的工单
    - 位置：`server/cmd/admin_api/biz/handler/admin_api/order.go:GetWorkflowFund`

## 6. 响应格式标准化

### 统一分页响应
- [ ] **FundList 分页信息补全**
    - 描述：列表接口分页信息不完整，缺少 `page_num`、`page_size` 返回
    - 行动：响应中补充当前页码、每页条数、总页数等分页元信息
    - 位置：`server/cmd/admin_api/internal/services/finanacial_service/fund_service.go:FundList`

### 时间格式统一
- [ ] **时间戳格式标准化**
    - 描述：各接口返回的时间格式不一致，有些用毫秒，有些用秒
    - 行动：统一使用毫秒时间戳，并在API层进行格式转换
    - 位置：所有收款相关接口响应处理

### 金额字段标准化
- [ ] **金额字段精度统一**
    - 描述：基于代码分析发现，金额字段使用 `decimal(20,5)` 存储，但API返回格式不一致
    - 行动：统一金额字段返回格式，保持5位小数精度，避免精度丢失
    - 位置：所有涉及金额字段的API响应处理

### 枚举值标准化
- [ ] **状态和类型字段枚举值说明**
    - 描述：API响应中的状态码、类型码缺少说明，前端难以理解
    - 行动：在API文档中补充枚举值说明，或在响应中增加描述字段
    - 位置：所有包含枚举字段的API接口

## 7. 单元测试

### 接口测试覆盖
- [ ] **FundList 接口测试**
    - 描述：缺少边界条件测试（如空结果、大分页等）
    - 行动：补充正常流程、异常参数、权限校验等测试用例
    - 位置：新建 `server/cmd/admin_api/biz/handler/admin_api/order_test.go`

- [ ] **FundApprove 接口测试**
    - 描述：缺少审批流程的完整测试
    - 行动：测试不同状态收款单的审批、权限校验、错误处理等场景
    - 位置：同上测试文件

---

以上优化建议均可直接分配给开发人员执行，每项都有明确的文件位置和具体行动。 