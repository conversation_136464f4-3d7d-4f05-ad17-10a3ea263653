# 财务收款服务集成测试指南

## 概述

本文档介绍如何运行财务收款服务的集成测试。集成测试通过 nacos 配置连接真实数据库，验证服务在实际环境中的表现。

## 文件说明

- `financial_fund_test.go` - 单元测试（Mock 测试）
- `financial_fund_integration_test.go` - 集成测试（真实数据库测试）

## 运行集成测试

### 前置条件

1. **Nacos 服务运行中**
   ```bash
   # 确保 nacos 服务已启动，默认端口 8848
   docker-compose -f supports/devops/nacos/docker-compose.yml up -d
   ```

2. **数据库服务运行中**
   ```bash
   # 确保 MySQL 数据库服务已启动
   docker-compose -f supports/devops/backend/docker-compose.yml up -d
   ```

3. **Nacos 配置已设置**
   - 确保在 nacos 中已配置 `order` 服务的数据库连接信息
   - 配置组：`local`（默认）或通过环境变量 `TEST_ENV` 指定

### 运行命令

#### 1. 运行所有集成测试
```bash
# 进入项目根目录
cd /path/to/uofferglobalv2

# 设置环境变量启用集成测试并忽略 proto 冲突
export INTEGRATION_TEST=true
export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore
export HOST_ENV=test

# 运行集成测试
go test ./server/cmd/order/internal/services/ -run TestFinancialFundIntegrationTestSuite -v
```

#### 2. 运行特定的集成测试
```bash
# 运行特定测试方法
export INTEGRATION_TEST=true
export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore
go test ./server/cmd/order/internal/services/ -run TestFinancialFundIntegrationTestSuite/TestRealFundCreate -v
```

#### 3. 指定测试环境
```bash
# 使用不同的 nacos 配置环境
export TEST_ENV=dev  # 可选值: local, dev, test, pre, prod
export INTEGRATION_TEST=true
export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore
go test ./server/cmd/order/internal/services/ -run TestFinancialFundIntegrationTestSuite -v
```

#### 4. 运行性能测试
```bash
# 运行性能基准测试
export INTEGRATION_TEST=true
export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore
go test ./server/cmd/order/internal/services/ -run TestFinancialFundIntegrationTestSuite/BenchmarkRealFundList -v
```

## 测试用例说明

### 核心功能测试

1. **TestRealFundCreate** - 收款单创建测试
   - 验证完整的收款单创建流程
   - 验证数据库记录的正确性
   - 验证支付记录的关联性

2. **TestRealFundUpdate** - 收款单更新测试
   - 验证事务处理的完整性
   - 验证支付记录的更新逻辑
   - 验证数据一致性

3. **TestRealFundDel** - 收款单删除测试
   - 验证软删除机制
   - 验证关联数据的级联删除
   - 验证事务回滚机制

4. **TestRealFundList** - 收款单列表查询测试
   - 验证分页查询功能
   - 验证过滤条件的正确性
   - 验证返回数据的完整性

### 业务逻辑测试

5. **TestRealCalculateShouldAmountRmb** - 应收金额计算测试
   - 验证定金、首款、尾款的计算逻辑
   - 验证已付金额的扣减逻辑
   - 验证不同收款类型的处理

6. **TestRealGetOrderRelations** - 订单关系查询测试
   - 验证共同提交人关系查询
   - 验证订单来源关系查询
   - 验证关系数据的正确性

7. **TestRealCreateFinancialPaidRecords** - 支付记录创建测试
   - 验证多币种支付记录创建
   - 验证交易单号的处理
   - 验证支付账户信息的关联

### 性能测试

8. **BenchmarkRealFundList** - 列表查询性能测试
   - 测试大数据量下的查询性能
   - 验证响应时间是否在可接受范围内
   - 提供性能基准数据

## 测试数据管理

### 自动清理机制
- 每个测试用例都包含数据清理逻辑
- 使用特殊的测试 ID 前缀避免与生产数据冲突
- 测试失败时也会尝试清理数据

### 测试数据特征
```go
// 测试订单号前缀
"TEST_INTEGRATION_*"
"CALC_TEST_*"
"UPDATE_TEST_*"
"DELETE_TEST_*"
"LIST_TEST_*"

// 测试订单 ID 范围
111111-999999 (避免与真实订单冲突)
```

## 故障排除

### 常见问题

1. **Proto 注册冲突错误**
   ```
   panic: proto: file "constants.proto" is already registered
   previously from: "uofferv2/kitex_gen/constants"
   currently from:  "uofferv2/server/cmd/admin_api/biz/model/constants"
   ```
   **解决方案**: 设置环境变量 `export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=ignore`
   
   **说明**: 这是因为同一个 proto 文件被多个包引用导致的命名空间冲突，设置此环境变量可以忽略冲突。

2. **测试被跳过**
   ```
   跳过集成测试: 设置环境变量 INTEGRATION_TEST=true 来启用
   ```
   **解决方案**: 设置环境变量 `export INTEGRATION_TEST=true`

3. **Nacos 连接失败**
   ```
   跳过集成测试: 无法连接 nacos 或数据库配置错误
   ```
   **解决方案**: 
   - 检查 nacos 服务是否运行
   - 检查 `config/nacos_config.yaml` 配置
   - 确认 nacos 中有对应的配置

3. **数据库连接失败**
   ```
   跳过集成测试: 无法连接数据库
   ```
   **解决方案**:
   - 检查数据库服务是否运行
   - 检查 nacos 中的数据库配置
   - 确认数据库权限设置

4. **测试数据冲突**
   ```
   测试失败: 数据已存在或约束冲突
   ```
   **解决方案**:
   - 手动清理测试数据
   - 检查测试用例的数据清理逻辑
   - 使用不同的测试 ID

### 调试技巧

1. **启用详细日志**
   ```bash
   export INTEGRATION_TEST=true
   go test ./server/cmd/order/internal/services/ -run TestFinancialFundIntegrationTestSuite -v -args -test.v
   ```

2. **单独运行失败的测试**
   ```bash
   go test ./server/cmd/order/internal/services/ -run TestFinancialFundIntegrationTestSuite/TestRealFundCreate -v
   ```

3. **检查数据库状态**
   ```sql
   -- 查看测试数据
   SELECT * FROM financial_fund WHERE order_no LIKE 'TEST_%';
   SELECT * FROM financial_paid WHERE financial_fund_id IN (
       SELECT id FROM financial_fund WHERE order_no LIKE 'TEST_%'
   );
   ```

## 最佳实践

1. **运行前准备**
   - 确保测试环境稳定
   - 备份重要数据（虽然测试有隔离机制）
   - 检查服务依赖状态

2. **测试执行**
   - 优先运行单元测试
   - 在稳定环境中运行集成测试
   - 关注测试输出和性能指标

3. **结果分析**
   - 检查所有测试是否通过
   - 分析性能测试结果
   - 验证数据清理是否完整

4. **持续集成**
   - 将集成测试纳入 CI/CD 流程
   - 设置合理的超时时间
   - 配置测试结果通知

## 注意事项

⚠️ **重要提醒**:
- 集成测试会连接真实数据库，请确保在测试环境中运行
- 测试数据会自动清理，但建议在非生产环境中执行
- 性能测试可能会对数据库产生一定负载
- 确保测试环境的数据库权限足够（需要创建、更新、删除权限）

## 🚀 新功能：自适应配置路径

### 配置路径优先级

集成测试现在支持自适应配置路径检测，按以下优先级查找配置文件：

1. **环境变量** `NACOS_CONFIG_PATH`
2. **命令行参数** `-config` 或 `-c`
3. **自动检测** - 从当前目录向上查找 `config/nacos_config.yaml`
4. **默认路径** - `config/nacos_config.yaml`

### 使用方式

#### 方式1：环境变量设置
```bash
export NACOS_CONFIG_PATH="/path/to/your/nacos_config.yaml"
go test -v ./server/cmd/order/internal/services/... -run TestFinancialFundIntegrationTestSuite
```

#### 方式2：命令行参数
```bash
go test -v ./server/cmd/order/internal/services/... -run TestFinancialFundIntegrationTestSuite -config="/path/to/your/nacos_config.yaml"
```

#### 方式3：自动检测（推荐）
```bash
# 在项目根目录下运行，会自动找到 config/nacos_config.yaml
cd /path/to/uofferglobalv2
go test -v ./server/cmd/order/internal/services/... -run TestFinancialFundIntegrationTestSuite
```

#### 方式4：在子目录运行
```bash
# 在任何子目录下运行，会自动向上查找配置文件
cd /path/to/uofferglobalv2/server/cmd/order
go test -v ./internal/services/... -run TestFinancialFundIntegrationTestSuite
```

## 🔧 配置要求

- 检查 `config/nacos_config.yaml` 配置
- 确保 nacos 服务可访问
- 确保测试数据库可连接

## 📝 测试用例

### 1. TestRealFundCreate
测试真实的收款单创建功能

### 2. TestRealCalculateShouldAmountRmb
测试应收金额计算逻辑

### 3. TestRealGetOrderRelations
测试订单关系查询功能

### 4. TestRealFundUpdate
测试收款单更新功能

### 5. TestRealFundDel
测试收款单删除功能

## 🚨 注意事项

1. **数据安全**：集成测试会操作真实数据库，请确保使用测试环境
2. **数据清理**：测试完成后会自动清理测试数据
3. **网络依赖**：需要能访问 nacos 和数据库服务
4. **权限要求**：确保测试账号有足够的数据库操作权限

## 🐛 故障排除

### 配置文件找不到
```
Error: read dataId: order group: svc config failed: open config/nacos_config.yaml: no such file or directory
```

**解决方案**：
1. 检查配置文件是否存在
2. 使用环境变量指定正确路径：`export NACOS_CONFIG_PATH="/correct/path/nacos_config.yaml"`
3. 或使用命令行参数：`-config="/correct/path/nacos_config.yaml"`

### 数据库连接失败
```
Error: 跳过集成测试: 无法连接数据库
```

**解决方案**：
1. 检查 nacos 配置中的数据库连接信息
2. 确保数据库服务正在运行
3. 检查网络连通性

## 📊 性能测试

项目还包含基准测试：

```bash
go test -v -bench=BenchmarkRealFundList ./server/cmd/order/internal/services/... -run=^$
```

## 🔄 持续集成

在 CI/CD 环境中，推荐使用环境变量方式：

```yaml
env:
  NACOS_CONFIG_PATH: ${{ secrets.NACOS_CONFIG_PATH }}
  TEST_ENV: test
``` 