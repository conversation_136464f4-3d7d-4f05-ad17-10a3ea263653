# 数据库 Lint 工具 (dblint)

## 📋 概述

`dblint` 是一个用于检查和修复数据库表结构不一致性的工具，确保数据库表符合项目规范。

## 🛠️ 功能特性

### 1. 字段类型检查
- **datetime 字段**：检查是否为 `datetime(3)` 格式
- **decimal 字段**：检查是否为 `decimal(20,5)` 格式  
- **by 字段**：检查 `created_by` 和 `updated_by` 是否为 `bigint` 类型

### 2. 主键规范检查
- 检查主键名称是否为 `id`
- 检查主键类型是否为 `bigint`

### 3. DDL 生成
- 自动生成修复 SQL 语句
- 输出到指定文件

## 🚀 使用方法

### 基本用法

```bash
cd supports/dblint
go run main.go [参数]
```

### 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `-H` | `************` | 数据库主机地址 |
| `-P` | `3008` | 数据库端口 |
| `-db` | `ukbackendv2` | 数据库名称 |
| `-u` | `ukuoffer_dev` | 数据库用户名 |
| `-p` | *(必填)* | 数据库密码 |
| `-o` | `/tmp/dblint.sql` | 输出 DDL 文件路径 |
| `-v` | `false` | 是否显示详细信息 |

### 使用示例

```bash
# 基本使用
go run main.go -p "your_password"

# 自定义数据库连接
go run main.go -H "localhost" -P 3306 -db "test_db" -u "root" -p "password"

# 输出详细信息并自定义输出文件
go run main.go -p "password" -v -o "./database_fixes.sql"
```

## 📊 检查规则

### DateTime 字段规范
```sql
-- ❌ 不规范
CREATE TABLE example (
    created_at datetime
);

-- ✅ 规范格式
CREATE TABLE example (
    created_at datetime(3)
);
```

### Decimal 字段规范
```sql
-- ❌ 不规范
CREATE TABLE example (
    amount decimal(10,2)
);

-- ✅ 规范格式  
CREATE TABLE example (
    amount decimal(20,5)
);
```

### By 字段规范
```sql
-- ❌ 不规范
CREATE TABLE example (
    created_by int
);

-- ✅ 规范格式
CREATE TABLE example (
    created_by bigint
);
```

### 主键规范
```sql
-- ❌ 不规范
CREATE TABLE example (
    pk_id int PRIMARY KEY
);

-- ✅ 规范格式
CREATE TABLE example (
    id bigint PRIMARY KEY AUTO_INCREMENT
);
```

## 📝 输出示例

工具会生成类似以下的 DDL 文件：

```sql
/* datetime columns */
ALTER TABLE `user_table` MODIFY COLUMN `created_at` datetime(3);
ALTER TABLE `order_table` MODIFY COLUMN `updated_at` datetime(3);

/* decimal columns */
ALTER TABLE `financial_fund` MODIFY COLUMN `amount` decimal(20,5);

/* by fields columns */
ALTER TABLE `product_table` MODIFY COLUMN `created_by` bigint;
```

## ⚠️ 注意事项

1. **生产环境谨慎使用**：建议先在测试环境验证
2. **数据备份**：执行 DDL 前务必备份数据库
3. **权限要求**：需要数据库 ALTER 权限
4. **影响评估**：大表修改可能影响性能

## 🔧 故障排除

### 连接失败
```
Error: connect to db mysql initialization failed
```
**解决方案**：检查数据库连接参数和网络连通性

### 权限不足
```
Error: ALTER command denied
```
**解决方案**：确保数据库用户有 ALTER 权限

### 密码未提供
```
Error: database password must be provided via the -p flag
```
**解决方案**：使用 `-p` 参数提供数据库密码

## 📈 最佳实践

1. **定期检查**：建议在开发流程中定期运行
2. **CI/CD 集成**：可集成到持续集成流程中
3. **团队规范**：确保团队了解并遵循数据库规范
4. **渐进式修复**：对于大型项目，建议分批修复

## 🔗 相关文档

- [数据库设计规范](../database/README.md)
- [开发环境配置](../operations/environment_setup.md)
- [故障排除指南](../operations/troubleshooting.md) 