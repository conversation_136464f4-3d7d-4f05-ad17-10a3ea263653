-- 创建客户托管邮箱账号表
CREATE TABLE customer_provisioned_emails (
  id bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  customer_id bigint NOT NULL COMMENT '客户id',
  `describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述说明',
  url varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '邮箱地址',
  password varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `forward` smallint(1) NOT NULL COMMENT '是否设置转发（1：否 2：是）',
  forward_url varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转发邮箱地址',
  created_at datetime(3) DEFAULT NULL COMMENT '创建时间',
  updated_at datetime(3) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=33142 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='客户托管邮箱账号';

-- 给 customers 表增加 remark 字段
ALTER TABLE customers 
ADD COLUMN remark longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注' AFTER refund_flag; 