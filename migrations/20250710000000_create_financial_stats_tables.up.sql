-- 创建财务统计相关表和存储过程
-- 用于支持财务报表和统计分析功能

-- 1. 创建统计游标表
CREATE TABLE financial_stats_cursor (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stat_type VARCHAR(50) NOT NULL COMMENT '统计类型: fund_approve, refund_approve',
    last_main_table_id BIGINT DEFAULT 0 COMMENT '主表最后统计的ID',
    last_log_table_id BIGINT DEFAULT 0 COMMENT '日志表最后统计的ID',
    last_stat_date DATE NULL COMMENT '最后统计日期',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stat_type (stat_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统计游标表';

-- 2. 创建收款统计表
CREATE TABLE financial_fund_daily_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stat_date DATE NOT NULL COMMENT '统计日期',
    brand_id BIGINT DEFAULT 0 COMMENT '出单品牌ID',
    account_type TINYINT DEFAULT 0 COMMENT '收款账号类型',
    account_id BIGINT DEFAULT 0 COMMENT '收款账号ID',

    -- 权限维度字段
    handle_by_staff_id BIGINT DEFAULT 0 COMMENT '直接处理人ID',
    order_staff_id BIGINT DEFAULT 0 COMMENT '订单关联员工ID',

    -- 核心统计指标
    new_pending_count INT DEFAULT 0 COMMENT '新增待审核数量',
    approved_count INT DEFAULT 0 COMMENT '审核通过数量',
    rejected_count INT DEFAULT 0 COMMENT '审核驳回数量',

    -- 时长统计（用于计算平均值）
    total_approve_duration_hours DECIMAL(15,2) DEFAULT 0 COMMENT '总审核时长(小时)',
    approve_completed_count INT DEFAULT 0 COMMENT '完成审核数量',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_date_dimensions_staff (stat_date, brand_id, account_type, account_id, handle_by_staff_id, order_staff_id),
    INDEX idx_stat_date (stat_date),
    INDEX idx_brand_id (brand_id),
    INDEX idx_account_type (account_type),
    INDEX idx_handle_by_staff_id (handle_by_staff_id),
    INDEX idx_order_staff_id (order_staff_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收款统计表 - 按日期/品牌/账户/权限维度统计收款审核数据';

-- 3. 创建支款审核统计表
CREATE TABLE financial_refund_approve_daily_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stat_date DATE NOT NULL COMMENT '统计日期',
    refund_type TINYINT DEFAULT 0 COMMENT '支款类型',

    -- 权限维度字段
    handle_by_staff_id BIGINT DEFAULT 0 COMMENT '直接处理人ID',
    order_staff_id BIGINT DEFAULT 0 COMMENT '订单关联员工ID',

    -- 审核相关统计
    new_pending_count INT DEFAULT 0 COMMENT '新增待审核数量',
    approved_count INT DEFAULT 0 COMMENT '审核通过数量',
    rejected_count INT DEFAULT 0 COMMENT '审核驳回数量',

    -- 时长统计
    total_approve_duration_hours DECIMAL(15,2) DEFAULT 0 COMMENT '总审核时长(小时)',
    approve_completed_count INT DEFAULT 0 COMMENT '完成审核数量',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_date_refund_type_staff (stat_date, refund_type, handle_by_staff_id, order_staff_id),
    INDEX idx_stat_date (stat_date),
    INDEX idx_refund_type (refund_type),
    INDEX idx_handle_by_staff_id (handle_by_staff_id),
    INDEX idx_order_staff_id (order_staff_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支款审核统计表 - 按日期/支款类型/权限维度统计支款审核数据';

-- 4. 创建支款打款统计表
CREATE TABLE financial_refund_payment_daily_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stat_date DATE NOT NULL COMMENT '统计日期',
    refund_type TINYINT DEFAULT 0 COMMENT '支款类型',

    -- 权限维度字段
    handle_by_staff_id BIGINT DEFAULT 0 COMMENT '直接处理人ID',
    order_staff_id BIGINT DEFAULT 0 COMMENT '订单关联员工ID',

    -- 打款相关统计
    new_pending_payment_count INT DEFAULT 0 COMMENT '新增待打款数量',
    payment_success_count INT DEFAULT 0 COMMENT '支款成功数量',
    payment_rejected_count INT DEFAULT 0 COMMENT '支款驳回数量',
    payment_timeout_count INT DEFAULT 0 COMMENT '支款超时数量',

    -- 时长统计
    total_payment_duration_hours DECIMAL(15,2) DEFAULT 0 COMMENT '总打款时长(小时)',
    payment_completed_count INT DEFAULT 0 COMMENT '完成打款数量',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_date_refund_type_staff (stat_date, refund_type, handle_by_staff_id, order_staff_id),
    INDEX idx_stat_date (stat_date),
    INDEX idx_refund_type (refund_type),
    INDEX idx_handle_by_staff_id (handle_by_staff_id),
    INDEX idx_order_staff_id (order_staff_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支款打款统计表 - 按日期/支款类型/权限维度统计支款打款数据';

-- 5. 初始化统计游标
INSERT INTO financial_stats_cursor (stat_type, last_main_table_id, last_log_table_id) VALUES
('fund_approve', 0, 0),
('refund_approve', 0, 0);

-- 6. 创建收款统计存储过程
DELIMITER $$
CREATE PROCEDURE StatFinancialFundDaily()
BEGIN
    DECLARE last_fund_id BIGINT DEFAULT 0;
    DECLARE last_log_id BIGINT DEFAULT 0;
    DECLARE max_fund_id BIGINT DEFAULT 0;
    DECLARE max_log_id BIGINT DEFAULT 0;

    -- 获取游标位置
    SELECT last_main_table_id, last_log_table_id
    INTO last_fund_id, last_log_id
    FROM financial_stats_cursor
    WHERE stat_type = 'fund_approve';

    -- 获取当前最大ID
    SELECT COALESCE(MAX(id), 0) INTO max_fund_id FROM financial_fund;
    SELECT COALESCE(MAX(id), 0) INTO max_log_id FROM financial_fund_approve_log;

    -- 如果没有新数据，直接返回
    IF last_fund_id >= max_fund_id AND last_log_id >= max_log_id THEN
        SELECT 'No new data' AS message; -- 可选: 输出一条消息
        -- 存储过程会自然结束，相当于 RETURN
    ELSE
        -- 统计新增待审核（基于主表的新记录）
        -- 通过goods_id关联products表获取brand_id，通过financial_paid表获取payment_account_id
        INSERT INTO financial_fund_daily_stats (
            stat_date, brand_id, account_type, account_id,
            handle_by_staff_id, order_staff_id, new_pending_count
        )
        SELECT
            DATE(f.created_at) as stat_date,
            COALESCE(p.brand_id, 0) as brand_id,  -- 通过goods_id关联products表获取brand_id
            COALESCE(fa.account_type, 0) as account_type,
            COALESCE(fp.payment_account_id, 0) as account_id,
            COALESCE(f.handle_by, 0) as handle_by_staff_id,
            COALESCE(ord_rel.user_id, 0) as order_staff_id,
            COUNT(*) as new_pending_count
        FROM financial_fund f
        LEFT JOIN products p ON f.goods_id = p.id  -- 通过goods_id关联products表
        LEFT JOIN financial_paid fp ON fp.financial_fund_id = f.id  -- 关联financial_paid表
        LEFT JOIN financial_account fa ON fp.payment_account_id = fa.id  -- 关联financial_account表
        LEFT JOIN order_relation ord_rel ON f.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
        WHERE f.id > last_fund_id
          AND f.id <= max_fund_id
          AND f.deleted_at = 0
        GROUP BY DATE(f.created_at), p.brand_id, fa.account_type, fp.payment_account_id, f.handle_by, ord_rel.user_id

        ON DUPLICATE KEY UPDATE
            new_pending_count = new_pending_count + VALUES(new_pending_count);

        -- 统计审核通过/驳回（基于日志表的新记录）
        -- 只统计第一次审核的时长：从创建时间到第一次审核结果的时长
        INSERT INTO financial_fund_daily_stats (
            stat_date, brand_id, account_type, account_id,
            handle_by_staff_id, order_staff_id,
            approved_count, rejected_count,
            total_approve_duration_hours, approve_completed_count
        )
        SELECT
            DATE(log.created_at) as stat_date,
            COALESCE(p.brand_id, 0) as brand_id,
            COALESCE(fa.account_type, 0) as account_type,
            COALESCE(fp.payment_account_id, 0) as account_id,
            COALESCE(f.handle_by, 0) as handle_by_staff_id,
            COALESCE(ord_rel.user_id, 0) as order_staff_id,

            SUM(CASE WHEN log.status = 2 THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN log.status = 3 THEN 1 ELSE 0 END) as rejected_count,

            -- 只统计第一次审核的时长：从创建到第一次审核结果
            SUM(
                CASE WHEN log.id = (
                    SELECT MIN(first_log.id)
                    FROM financial_fund_approve_log first_log
                    WHERE first_log.financial_fund_id = f.id
                    AND first_log.status IN (2, 3)
                ) THEN TIMESTAMPDIFF(HOUR, f.created_at, log.created_at)
                ELSE 0 END
            ) as total_approve_duration_hours,

            -- 只统计第一次审核的数量
            SUM(
                CASE WHEN log.id = (
                    SELECT MIN(first_log.id)
                    FROM financial_fund_approve_log first_log
                    WHERE first_log.financial_fund_id = f.id
                    AND first_log.status IN (2, 3)
                ) THEN 1 ELSE 0 END
            ) as approve_completed_count

        FROM financial_fund_approve_log log
        INNER JOIN financial_fund f ON log.financial_fund_id = f.id
        LEFT JOIN products p ON f.goods_id = p.id
        LEFT JOIN financial_paid fp ON fp.financial_fund_id = f.id
        LEFT JOIN financial_account fa ON fp.payment_account_id = fa.id
        LEFT JOIN order_relation ord_rel ON f.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
        WHERE log.id > last_log_id
          AND log.id <= max_log_id
          AND log.status IN (2, 3)  -- 只统计通过和驳回
          AND f.deleted_at = 0
        GROUP BY DATE(log.created_at), p.brand_id, fa.account_type, fp.payment_account_id, f.handle_by, ord_rel.user_id

        ON DUPLICATE KEY UPDATE
            approved_count = approved_count + VALUES(approved_count),
            rejected_count = rejected_count + VALUES(rejected_count),
            total_approve_duration_hours = total_approve_duration_hours + VALUES(total_approve_duration_hours),
            approve_completed_count = approve_completed_count + VALUES(approve_completed_count);

        -- 更新游标
        UPDATE financial_stats_cursor
        SET last_main_table_id = max_fund_id,
            last_log_table_id = max_log_id,
            last_stat_date = CURDATE()
        WHERE stat_type = 'fund_approve';
    END IF;

END$$
DELIMITER ;

-- 7. 创建支款统计存储过程
DELIMITER $$

CREATE PROCEDURE StatFinancialRefundDaily()
BEGIN
    DECLARE last_refund_id BIGINT DEFAULT 0;
    DECLARE last_log_id BIGINT DEFAULT 0;
    DECLARE max_refund_id BIGINT DEFAULT 0;
    DECLARE max_log_id BIGINT DEFAULT 0;

    -- 获取游标
    SELECT last_main_table_id, last_log_table_id
    INTO last_refund_id, last_log_id
    FROM financial_stats_cursor
    WHERE stat_type = 'refund_approve';

    -- 获取最大ID
    SELECT COALESCE(MAX(id), 0) INTO max_refund_id FROM financial_refund;
    SELECT COALESCE(MAX(id), 0) INTO max_log_id FROM financial_refund_approve_log;

    -- 如果没有新数据，直接结束
    IF last_refund_id >= max_refund_id AND last_log_id >= max_log_id THEN
        SELECT 'No new refund data' AS message;
    ELSE

        -- 统计支款审核信息（新增待审核）
        INSERT INTO financial_refund_approve_daily_stats (
            stat_date, refund_type, handle_by_staff_id, order_staff_id, new_pending_count
        )
        SELECT
            DATE(r.created_at) AS stat_date,
            r.refund_type,
            COALESCE(r.handle_by, 0) as handle_by_staff_id,
            COALESCE(ord_rel.user_id, 0) as order_staff_id,
            COUNT(*) AS new_pending_count
        FROM financial_refund r
        LEFT JOIN order_relation ord_rel ON r.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
        WHERE r.id > last_refund_id
          AND r.id <= max_refund_id
          AND r.deleted_at = 0
          AND r.approve_status = 1  -- 待审核状态
        GROUP BY DATE(r.created_at), r.refund_type, r.handle_by, ord_rel.user_id
        ON DUPLICATE KEY UPDATE
            new_pending_count = new_pending_count + VALUES(new_pending_count);

        -- 统计支款审核结果（审核通过/驳回）
        -- financial_refund_approve_log.status 和 financial_refund.approve_status 一一对应
        -- 1=待审批, 2=待支款(审核通过), 3=支款完成, 4=审核驳回

        INSERT INTO financial_refund_approve_daily_stats (
            stat_date, refund_type,
            handle_by_staff_id, order_staff_id,
            approved_count, rejected_count,
            total_approve_duration_hours, approve_completed_count
        )
        SELECT
            DATE(log.created_at) AS stat_date,
            r.refund_type,
            COALESCE(r.handle_by, 0) as handle_by_staff_id,
            COALESCE(ord_rel.user_id, 0) as order_staff_id,
            SUM(CASE WHEN log.status = 2 THEN 1 ELSE 0 END) AS approved_count,  -- 2=待支款(审核通过)
            SUM(CASE WHEN log.status = 4 THEN 1 ELSE 0 END) AS rejected_count,  -- 4=审核驳回

            -- 只统计第一次审核的时长：从创建到第一次审核结果
            SUM(
                CASE WHEN log.id = (
                    SELECT MIN(first_log.id)
                    FROM financial_refund_approve_log first_log
                    WHERE first_log.financial_refund_id = r.id
                      AND first_log.status IN (2, 4)  -- 2=审核通过, 4=审核驳回
                ) THEN TIMESTAMPDIFF(HOUR, r.created_at, log.created_at)
                ELSE 0 END
            ) AS total_approve_duration_hours,

            -- 只统计第一次审核的数量
            SUM(
                CASE WHEN log.id = (
                    SELECT MIN(first_log.id)
                    FROM financial_refund_approve_log first_log
                    WHERE first_log.financial_refund_id = r.id
                      AND first_log.status IN (2, 4)
                ) THEN 1 ELSE 0 END
            ) AS approve_completed_count
        FROM financial_refund_approve_log log
        INNER JOIN financial_refund r ON log.financial_refund_id = r.id
        LEFT JOIN order_relation ord_rel ON r.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
        WHERE log.id > last_log_id
          AND log.id <= max_log_id
          AND log.status IN (2, 4)  -- 2=审核通过(待支款), 4=审核驳回
          AND r.deleted_at = 0
        GROUP BY DATE(log.created_at), r.refund_type, r.handle_by, ord_rel.user_id
        ON DUPLICATE KEY UPDATE
            approved_count = approved_count + VALUES(approved_count),
            rejected_count = rejected_count + VALUES(rejected_count),
            total_approve_duration_hours = total_approve_duration_hours + VALUES(total_approve_duration_hours),
            approve_completed_count = approve_completed_count + VALUES(approve_completed_count);
        -- 统计支款打款信息（基于支款表的状态变更）
        INSERT INTO financial_refund_payment_daily_stats (
            stat_date, refund_type,
            handle_by_staff_id, order_staff_id,
            new_pending_payment_count, payment_success_count,
            payment_rejected_count, payment_timeout_count,
            total_payment_duration_hours, payment_completed_count
        )
        SELECT
            stat_date,
            refund_type,
            handle_by_staff_id,
            order_staff_id,
            SUM(new_pending_payment_count) AS new_pending_payment_count,
            SUM(payment_success_count) AS payment_success_count,
            SUM(payment_rejected_count) AS payment_rejected_count,
            SUM(payment_timeout_count) AS payment_timeout_count,
            SUM(total_payment_duration_hours) AS total_payment_duration_hours,
            SUM(payment_completed_count) AS payment_completed_count
        FROM (
            SELECT
                DATE(r.pass_time) AS stat_date,
                r.refund_type,
                COALESCE(r.handle_by, 0) as handle_by_staff_id,
                COALESCE(ord_rel.user_id, 0) as order_staff_id,
                CASE WHEN r.approve_status = 2 THEN 1 ELSE 0 END AS new_pending_payment_count,  -- 2=待支款
                0 AS payment_success_count,
                0 AS payment_rejected_count,
                0 AS payment_timeout_count,
                0 AS total_payment_duration_hours,
                0 AS payment_completed_count
            FROM financial_refund r
            LEFT JOIN order_relation ord_rel ON r.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
            WHERE r.id > last_refund_id
              AND r.id <= max_refund_id
              AND r.deleted_at = 0
              AND r.approve_status = 2  -- 2=待支款（审核通过进入待支款状态）
              AND r.pass_time IS NOT NULL

            UNION ALL

            SELECT
                DATE(r.complete_time) AS stat_date,
                r.refund_type,
                COALESCE(r.handle_by, 0) as handle_by_staff_id,
                COALESCE(ord_rel.user_id, 0) as order_staff_id,
                0 AS new_pending_payment_count,
                1 AS payment_success_count,
                0 AS payment_rejected_count,
                0 AS payment_timeout_count,
                CASE WHEN r.approve_status = 3 AND r.complete_time IS NOT NULL
                         AND r.complete_time != '1970-01-01 00:00:00'
                    THEN TIMESTAMPDIFF(HOUR,
                        (SELECT MIN(log.created_at)
                         FROM financial_refund_approve_log log
                         WHERE log.financial_refund_id = r.id
                           AND log.status = 2),  -- 找到第一次变成"待支款"状态的时间
                        r.complete_time)
                    ELSE 0 END AS total_payment_duration_hours,
                CASE WHEN r.approve_status = 3 AND r.complete_time IS NOT NULL
                         AND r.complete_time != '1970-01-01 00:00:00'
                         AND EXISTS (
                             SELECT 1 FROM financial_refund_approve_log log
                             WHERE log.financial_refund_id = r.id
                               AND log.status = 2
                         )
                    THEN 1 ELSE 0 END AS payment_completed_count
            FROM financial_refund r
            LEFT JOIN order_relation ord_rel ON r.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
            WHERE r.id > last_refund_id
              AND r.id <= max_refund_id
              AND r.deleted_at = 0
              AND r.approve_status = 3  -- 3=支款完成
              AND r.complete_time IS NOT NULL
              AND r.complete_time != '1970-01-01 00:00:00'  -- 排除默认空值

            UNION ALL

            SELECT
                DATE(r.reject_time) AS stat_date,
                r.refund_type,
                COALESCE(r.handle_by, 0) as handle_by_staff_id,
                COALESCE(ord_rel.user_id, 0) as order_staff_id,
                0 AS new_pending_payment_count,
                0 AS payment_success_count,
                CASE WHEN r.approve_status = 4 THEN 1 ELSE 0 END AS payment_rejected_count,  -- 4=审核驳回
                0 AS payment_timeout_count,
                0 AS total_payment_duration_hours,
                0 AS payment_completed_count
            FROM financial_refund r
            LEFT JOIN order_relation ord_rel ON r.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
            WHERE r.id > last_refund_id
              AND r.id <= max_refund_id
              AND r.deleted_at = 0
              AND r.approve_status = 4  -- 4=审核驳回
              AND r.reject_time IS NOT NULL

            UNION ALL

            -- 支款超时统计：approve_status=2(待支款) 且 超过refund_deadline 且 未支付(complete_time为空)
            SELECT
                DATE(r.refund_deadline) AS stat_date,
                r.refund_type,
                COALESCE(r.handle_by, 0) as handle_by_staff_id,
                COALESCE(ord_rel.user_id, 0) as order_staff_id,
                0 AS new_pending_payment_count,
                0 AS payment_success_count,
                0 AS payment_rejected_count,
                1 AS payment_timeout_count,
                0 AS total_payment_duration_hours,
                0 AS payment_completed_count
            FROM financial_refund r
            LEFT JOIN order_relation ord_rel ON r.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
            WHERE r.id > last_refund_id
              AND r.id <= max_refund_id
              AND r.deleted_at = 0
              AND r.approve_status = 2  -- 2=待支款
              AND r.refund_deadline IS NOT NULL
              AND r.refund_deadline < CURDATE()  -- 超过截止时间
              AND (r.complete_time IS NULL OR r.complete_time = '1970-01-01 00:00:00')  -- 未支付
        ) payment_stats
        WHERE stat_date IS NOT NULL
        GROUP BY stat_date, refund_type, handle_by_staff_id, order_staff_id
        ON DUPLICATE KEY UPDATE
            new_pending_payment_count = new_pending_payment_count + VALUES(new_pending_payment_count),
            payment_success_count = payment_success_count + VALUES(payment_success_count),
            payment_rejected_count = payment_rejected_count + VALUES(payment_rejected_count),
            payment_timeout_count = payment_timeout_count + VALUES(payment_timeout_count),
            total_payment_duration_hours = total_payment_duration_hours + VALUES(total_payment_duration_hours),
            payment_completed_count = payment_completed_count + VALUES(payment_completed_count);

        -- 更新游标
        UPDATE financial_stats_cursor
        SET last_main_table_id = max_refund_id,
            last_log_table_id = max_log_id,
            last_stat_date = CURDATE()
        WHERE stat_type = 'refund_approve';
    END IF;

END$$

DELIMITER ;

-- 8. 创建历史数据补录存储过程
-- Change the delimiter so MySQL treats the entire procedure as a single statement
DELIMITER $$

CREATE PROCEDURE `BackfillFinancialStats`()
BEGIN
    -- Variable declarations *must* be at the very beginning of the procedure body.
    DECLARE v_current_date DATE;
    DECLARE v_start_date DATE;

    -- Set the start date for the backfill (60 days ago)
    SET v_start_date = DATE_SUB(CURDATE(), INTERVAL 60 DAY);
    SET v_current_date = v_start_date;

    -- Loop through each day from the start date to the current date
    WHILE v_current_date <= CURDATE() DO

        -- Backfill statistics for fund collections (收款统计)
        INSERT INTO financial_fund_daily_stats (
            stat_date, brand_id, account_type, account_id,
            handle_by_staff_id, order_staff_id,
            new_pending_count, approved_count, rejected_count,
            total_approve_duration_hours, approve_completed_count
        )
        SELECT
            v_current_date AS stat_date,
            COALESCE(p.brand_id, 0) AS brand_id,
            COALESCE(fa.account_type, 0) AS account_type,
            COALESCE(fp.payment_account_id, 0) AS account_id,
            COALESCE(f.handle_by, 0) as handle_by_staff_id,
            COALESCE(ord_rel.user_id, 0) as order_staff_id,
            SUM(CASE WHEN DATE(f.created_at) = v_current_date THEN 1 ELSE 0 END) AS new_pending_count,
            SUM(CASE WHEN DATE(f.pass_time) = v_current_date THEN 1 ELSE 0 END) AS approved_count,
            SUM(CASE WHEN DATE(f.reject_time) = v_current_date THEN 1 ELSE 0 END) AS rejected_count,
            SUM(CASE
                WHEN
                    DATE(COALESCE(f.pass_time, f.reject_time)) = v_current_date
                    AND NOT EXISTS(
                        SELECT 1
                        FROM financial_fund_approve_log prev_log
                        WHERE prev_log.financial_fund_id = f.id
                          AND prev_log.status IN (2, 3)
                          AND prev_log.created_at < COALESCE(f.pass_time, f.reject_time)
                    )
                THEN
                    TIMESTAMPDIFF(HOUR, f.created_at, COALESCE(f.pass_time, f.reject_time))
                ELSE 0
            END) AS total_approve_duration_hours,
            SUM(CASE
                WHEN
                    DATE(COALESCE(f.pass_time, f.reject_time)) = v_current_date
                    AND NOT EXISTS(
                        SELECT 1
                        FROM financial_fund_approve_log prev_log
                        WHERE prev_log.financial_fund_id = f.id
                          AND prev_log.status IN (2, 3)
                          AND prev_log.created_at < COALESCE(f.pass_time, f.reject_time)
                    )
                THEN 1
                ELSE 0
            END) AS approve_completed_count
        FROM
            financial_fund f
            LEFT JOIN products p ON f.goods_id = p.id
            LEFT JOIN financial_paid fp ON fp.financial_fund_id = f.id
            LEFT JOIN financial_account fa ON fp.payment_account_id = fa.id
            LEFT JOIN order_relation ord_rel ON f.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
            WHERE (DATE(f.created_at) = v_current_date OR DATE(f.pass_time) = v_current_date OR DATE(f.reject_time) = v_current_date)
                AND f.deleted_at = 0
            GROUP BY p.brand_id, fa.account_type, fp.payment_account_id, f.handle_by, ord_rel.user_id
        HAVING new_pending_count > 0 OR approved_count > 0 OR rejected_count > 0
        ON DUPLICATE KEY UPDATE
            new_pending_count = VALUES(new_pending_count),
            approved_count = VALUES(approved_count),
            rejected_count = VALUES(rejected_count),
            total_approve_duration_hours = VALUES(total_approve_duration_hours),
            approve_completed_count = VALUES(approve_completed_count);

        -- Backfill statistics for refund approvals (支款审核统计)
        INSERT INTO financial_refund_approve_daily_stats (
            stat_date, refund_type,
            handle_by_staff_id, order_staff_id,
            new_pending_count, approved_count, rejected_count,
            total_approve_duration_hours, approve_completed_count
        )
        SELECT
            v_current_date AS stat_date,
            r.refund_type,
            COALESCE(r.handle_by, 0) as handle_by_staff_id,
            COALESCE(ord_rel.user_id, 0) as order_staff_id,
            SUM(CASE WHEN DATE(r.created_at) = v_current_date THEN 1 ELSE 0 END) AS new_pending_count,
            SUM(CASE WHEN DATE(r.pass_time) = v_current_date AND r.approve_status = 2 THEN 1 ELSE 0 END) AS approved_count,
            SUM(CASE WHEN DATE(r.reject_time) = v_current_date AND r.approve_status = 4 THEN 1 ELSE 0 END) AS rejected_count,
            SUM(CASE
                WHEN
                    DATE(COALESCE(r.pass_time, r.reject_time)) = v_current_date
                    AND r.approve_status IN (2, 4)
                    AND NOT EXISTS(
                        SELECT 1
                        FROM financial_refund_approve_log prev_log
                        WHERE prev_log.financial_refund_id = r.id
                          AND prev_log.status IN (2, 4)
                          AND prev_log.created_at < COALESCE(r.pass_time, r.reject_time)
                    )
                THEN
                    TIMESTAMPDIFF(HOUR, r.created_at, COALESCE(r.pass_time, r.reject_time))
                ELSE 0
            END) AS total_approve_duration_hours,
            SUM(CASE
                WHEN
                    DATE(COALESCE(r.pass_time, r.reject_time)) = v_current_date
                    AND r.approve_status IN (2, 4)
                    AND NOT EXISTS(
                        SELECT 1
                        FROM financial_refund_approve_log prev_log
                        WHERE prev_log.financial_refund_id = r.id
                          AND prev_log.status IN (2, 4)
                          AND prev_log.created_at < COALESCE(r.pass_time, r.reject_time)
                    )
                THEN 1
                ELSE 0
            END) AS approve_completed_count
        FROM
            financial_refund r
        LEFT JOIN order_relation ord_rel ON r.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
        WHERE
            (DATE(r.created_at) = v_current_date OR DATE(r.pass_time) = v_current_date OR DATE(r.reject_time) = v_current_date)
            AND r.deleted_at = 0
        GROUP BY r.refund_type, r.handle_by, ord_rel.user_id
        HAVING new_pending_count > 0 OR approved_count > 0 OR rejected_count > 0
        ON DUPLICATE KEY UPDATE
            new_pending_count = VALUES(new_pending_count),
            approved_count = VALUES(approved_count),
            rejected_count = VALUES(rejected_count),
            total_approve_duration_hours = VALUES(total_approve_duration_hours),
            approve_completed_count = VALUES(approve_completed_count);

        -- Backfill statistics for refund payments (支款打款统计)
        INSERT INTO financial_refund_payment_daily_stats (
            stat_date, refund_type,
            handle_by_staff_id, order_staff_id,
            new_pending_payment_count, payment_success_count,
            payment_rejected_count, payment_timeout_count,
            total_payment_duration_hours, payment_completed_count
        )
        SELECT
            v_current_date AS stat_date,
            r.refund_type,
            COALESCE(r.handle_by, 0) as handle_by_staff_id,
            COALESCE(ord_rel.user_id, 0) as order_staff_id,
            SUM(CASE WHEN DATE(r.pass_time) = v_current_date AND r.approve_status = 2 THEN 1 ELSE 0 END) AS new_pending_payment_count,
            SUM(CASE WHEN DATE(r.complete_time) = v_current_date AND r.approve_status = 3 THEN 1 ELSE 0 END) AS payment_success_count,
            SUM(CASE WHEN r.approve_status = 4 AND DATE(r.reject_time) = v_current_date THEN 1 ELSE 0 END) AS payment_rejected_count,
            SUM(CASE
                WHEN
                    r.approve_status = 2
                    AND r.refund_deadline IS NOT NULL
                    AND r.refund_deadline < v_current_date
                    AND (r.complete_time IS NULL OR r.complete_time = '1970-01-01 00:00:00')
                    AND DATE(r.pass_time) <= v_current_date
                THEN 1
                ELSE 0
            END) AS payment_timeout_count,
            SUM(CASE
                WHEN
                    DATE(r.complete_time) = v_current_date
                    AND r.approve_status = 3
                    AND r.complete_time IS NOT NULL
                    AND r.complete_time != '1970-01-01 00:00:00'
                THEN
                    TIMESTAMPDIFF(HOUR,
                        (SELECT MIN(log.created_at) FROM financial_refund_approve_log log WHERE log.financial_refund_id = r.id AND log.status = 2),
                        r.complete_time)
                ELSE 0
            END) AS total_payment_duration_hours,
            SUM(CASE
                WHEN
                    DATE(r.complete_time) = v_current_date
                    AND r.approve_status = 3
                    AND r.complete_time IS NOT NULL
                    AND r.complete_time != '1970-01-01 00:00:00'
                    AND EXISTS(SELECT 1 FROM financial_refund_approve_log log WHERE log.financial_refund_id = r.id AND log.status = 2)
                THEN 1
                ELSE 0
            END) AS payment_completed_count
        FROM
            financial_refund r
        LEFT JOIN order_relation ord_rel ON r.order_id = ord_rel.order_id AND ord_rel.action IN (6, 7) AND ord_rel.deleted_at = 0
        WHERE
            (DATE(r.pass_time) = v_current_date OR DATE(r.complete_time) = v_current_date OR DATE(r.reject_time) = v_current_date)
            AND r.deleted_at = 0
        GROUP BY r.refund_type, r.handle_by, ord_rel.user_id
        HAVING new_pending_payment_count > 0 OR payment_success_count > 0 OR payment_rejected_count > 0 OR payment_timeout_count > 0
        ON DUPLICATE KEY UPDATE
            new_pending_payment_count = VALUES(new_pending_payment_count),
            payment_success_count = VALUES(payment_success_count),
            payment_rejected_count = VALUES(payment_rejected_count),
            payment_timeout_count = VALUES(payment_timeout_count),
            total_payment_duration_hours = VALUES(total_payment_duration_hours),
            payment_completed_count = VALUES(payment_completed_count);

        -- Move to the next day
        SET v_current_date = DATE_ADD(v_current_date, INTERVAL 1 DAY);
    END WHILE;

    -- After the backfill is complete, update the cursor to the current maximum ID and date
    UPDATE financial_stats_cursor
    SET
        last_main_table_id = (SELECT COALESCE(MAX(id), 0) FROM financial_fund),
        last_log_table_id = (SELECT COALESCE(MAX(id), 0) FROM financial_fund_approve_log),
        last_stat_date = CURDATE()
    WHERE
        stat_type = 'fund_approve';

    UPDATE financial_stats_cursor
    SET
        last_main_table_id = (SELECT COALESCE(MAX(id), 0) FROM financial_refund),
        last_log_table_id = (SELECT COALESCE(MAX(id), 0) FROM financial_refund_approve_log),
        last_stat_date = CURDATE()
    WHERE
        stat_type = 'refund_approve';
END$$

-- Reset the delimiter back to the default
DELIMITER ;
