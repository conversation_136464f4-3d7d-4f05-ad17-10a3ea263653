-- 创建订单统计日数据表
CREATE TABLE IF NOT EXISTS `order_static_daily` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `uid` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `cate` TINYINT NOT NULL COMMENT '类型#1%新增订单|2%审核通过|3%申请支款',
    `num` INT UNSIGNED NOT NULL COMMENT '数量',
    `amount` DECIMAL(20,5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '金额',
    `day` DATE NOT NULL COMMENT '统计日期',
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    
    INDEX `idx_uid_day` (`uid`, `day`),
    INDEX `idx_cate_day` (`cate`, `day`),
    INDEX `idx_day` (`day`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单统计日数据表';

-- 创建订单工作流日志表
CREATE TABLE IF NOT EXISTS `order_workflow_log` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `order_id` BIGINT UNSIGNED NOT NULL COMMENT '订单ID',
    `status` TINYINT NOT NULL COMMENT '状态变更#1%创建|2%提交|3%审核中|4%审核通过|5%审核拒绝|6%申请支款|7%支款中|8%支款完成|9%支款失败',
    `operator_id` BIGINT UNSIGNED NOT NULL COMMENT '操作人ID',
    `operator_type` TINYINT NOT NULL DEFAULT 1 COMMENT '操作人类型#1%系统|2%用户|3%管理员',
    `remark` VARCHAR(255) DEFAULT NULL COMMENT '备注',
    `extra` JSON DEFAULT NULL COMMENT '额外信息',
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    
    INDEX `idx_order_id` (`order_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_operator_id` (`operator_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单工作流日志表';